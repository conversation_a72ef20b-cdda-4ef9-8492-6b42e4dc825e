type SearchFile = {
  path: string,
  content: string,
}

type SearchModes = {
  matchCase: boolean;
  matchWholeWord: boolean;
}

type SearchOptions = SearchModes & {
  prefixRegex?: RegExp;
  fileType?: string;
};

type SearchOperator = {
  searchText: string;
  options: SearchOptions;
};

type MultipleSearchParams = {
  files: Record<string, SearchFile>;
  operators: SearchOperator[];
  searchId: number;
};

type SingleSearchParams = {
  file: SearchFile;
  operator: SearchOperator;
  searchId: number;
};

type SearchData = SearchOptions & {
  searchText: string;
}

interface SearchMatch {
  text: string;
  start: number;
  end: number;
  lineContent: string;
  lineNumber: number;
}

interface SearchResult {
  filePath: string;
  matches: SearchMatch[];
  hasMatch: boolean;
}

export type {
  SearchFile,
  SearchOptions,
  SearchOperator,
  MultipleSearchParams,
  SingleSearchParams,
  SearchMatch,
  SearchResult,
  SearchModes,
  SearchData,
};
