import {
  PropertyType,
  FileType,
  FieldType,
} from '@aml-studio/client/pages/search/constants';

type FilterObjectType = PropertyType.Dimension
| PropertyType.Measure
| PropertyType.Metric
| FileType.Model
| FileType.Dataset
| FileType.Dashboard

type FilterFieldType = FieldType;

type FilterType = 'object' | 'field' | 'text';

interface BaseFilter {
  type: FilterType
}

interface ObjectFilter extends BaseFilter {
  type: 'object';
  objectType: FilterObjectType;
  objectName: string;
}

interface FieldFilter extends BaseFilter {
  type: 'field';
  fieldType: FilterFieldType;
  fieldName: string;
}

interface TextFilter extends BaseFilter {
  type: 'text';
  searchText: string;
}

type Filter = ObjectFilter | FieldFilter | TextFilter;

export type {
  Filter,
  FilterObjectType,
  FilterFieldType,
  ObjectFilter,
  FieldFilter,
  TextFilter,
};
