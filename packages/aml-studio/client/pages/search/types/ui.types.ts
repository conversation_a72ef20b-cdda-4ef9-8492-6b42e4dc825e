import type { Component } from 'vue';

enum NodeType {
  File = 'File',
  Text = 'Text',
}

type TextNode = {
  id: string;
  level: number;
  isExpanded: boolean;
  type: NodeType;
  path: string;
  text: string;
  start: number;
  end: number;
  lineContent: string;
  lineNumber: number;
};

type ChildNode = {
  type: NodeType;
  id: string;
};

type FileNode = {
  id: string;
  level: number;
  isExpanded: boolean;
  type: NodeType;
  label: string;
  path: string;
  children: ChildNode[];
};

type Option = {
  type: string;
  key: string;
  value: any;
  separator?: string;
  options?: {
    highlightable?: boolean;
    renderComponent?: Component;
    disabled?: boolean;
    onClickFn?: (option: Option) => void;
  };
}

enum OptionType {
  Filter = 'filter',
  History = 'history',
  Header = 'header',
}

type FilterOption = Option & {
  type: OptionType.Filter;
  keyLabel: string;
  valueLabel: string;
  helperText: string;
};

type HistoryOption = Option & {
  type: OptionType.History;
  value: string;
};

type HeaderOption = Option & {
  type: OptionType.Header;
}

type Node = FileNode | TextNode;

export type {
  Node,
  Option,
  FilterOption,
  HistoryOption,
  HeaderOption,
  FileNode,
  TextNode,
  ChildNode,
};

export {
  NodeType,
  OptionType,
};
