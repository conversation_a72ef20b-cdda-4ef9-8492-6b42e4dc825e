<template>
  <HTooltip
    v-bind="tooltipConfig"
  >
    <HButton
      data-ci="create-pr-action"
      size="sm"
      :type="disabled ? 'secondary-default' : 'primary-highlight'"
      :disabled="disabled"
      @click="emit('click')"
    >
      <div class="flex items-center p-0.5">
        <HIcon
          v-if="showLoading"
          name="circle-notch"
          size="md"
          class="mr-1"
          spin
        />
        <HIcon
          v-else-if="hasErrors"
          name="warning"
          size="sm"
          class="text-yellow-500 mr-1"
        />
        <span>{{ btnText }}</span>
      </div>
    </HButton>
  </HTooltip>
</template>

<script setup lang="ts">
import { HTooltip, HButton, HIcon } from '@holistics/design-system';
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  btnText: string;
  disabled: boolean;
  showLoading: boolean;
  tooltipText?: string;
  hasErrors?: boolean;
}>(), {
  disabled: false,
  showLoading: false,
  btnText: 'Create PR',
  tooltipText: undefined,
  hasErrors: false,
});

const emit = defineEmits<{
  click: [],
}>();

const tooltipConfig = computed(() => ({
  disabled: !props.tooltipText,
  content: props.tooltipText,
  placement: 'bottom-end',
}));
</script>
