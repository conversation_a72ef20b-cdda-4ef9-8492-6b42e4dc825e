<template>
  <CreatePrButton
    v-if="showCreatePRButton"
    btn-text="Create PR"
    :disabled="disableCreatePR"
    :show-loading="isCreatingPR"
    :has-errors="validationResult.isError()"
    :tooltip-text="createPrToolTip"
    @click="createPRAndRedirectToGithub"
  />
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { usePRWorkflow } from '@aml-studio/client/pages/composables/usePRWorkflow';
import { CurrentBranchState, PullRequestStatus } from '@aml-studio/client/types/allTypes';
import { useGitSync } from '@aml-studio/client/pages/composables/useGitSync';
import { checkFeatureToggle } from '@aml-studio/h/services';
import { validateProject } from '@aml-studio/client/store/modules/deployment/helpers/deployProject';
import { useStore } from 'vuex';
import { AMLStudioBottomPanel } from '@aml-studio/client/store/modules/uiState/constants';

import { usePRWorkflowSettings } from '../projectSettings/components/prWorkflowSettings/compositions/usePrWorkflowSettings';
import CreatePrButton from './components/CreatePrButton.vue';
import { useGitRepoState } from '../Git/composables/useGitRepoState';

const { prWorkflowSettings } = usePRWorkflowSettings();

const {
  createPR, isCreatingPR, isFetchingLatestPR, latestPRStatus, canDeployToProductionPrWorkflowMode,
} = usePRWorkflow();

const store = useStore();
const validationResult = computed(() => validateProject({
  allDiagnostics: store.getters['aml/editor/allDiagnostics'],
}));

const {
  repoState,
  isDeploying,
  isUnsaved,
  aheadProductionCommitCount,
} = useGitRepoState();

const { isSyncing } = useGitSync();

const isCreatePrDisabled = computed(() => {
  return isCreatingPR.value
         || isSyncing.value
         || isFetchingLatestPR.value
         || repoState.value === CurrentBranchState.uncommitted;
});

const isEnabledPrWorkflowSettings = computed(() => {
  return prWorkflowSettings?.value?.enabledPrWorkflow || false;
});

const showCreatePRButton = computed(() => {
  if (!checkFeatureToggle('aml_studio:pr_workflow') || !isEnabledPrWorkflowSettings.value) {
    return false;
  }
  if (isDeploying.value) {
    return false;
  }
  // this condition make the Publish button and CreatePR button not appear at the same time when loading
  if ((isFetchingLatestPR.value && !latestPRStatus.value) || isSyncing.value) {
    return true;
  }
  // if pull request is open, hide the CreatePR Button
  if (latestPRStatus.value?.state === PullRequestStatus.Open) {
    return false;
  }
  // TODO: checking if this logic works correctly with the Publish button
  if (canDeployToProductionPrWorkflowMode.value) {
    return false;
  }
  return true;
});

const createPRAndRedirectToGithub = async () => {
  // If there are errors, show the error panel instead of creating a PR
  if (validationResult.value.isError()) {
    // Open the problem tab to show errors
    store.dispatch('aml/uiState/setActiveBottomPanel', { activeBottomPanel: AMLStudioBottomPanel.ProblemPanel }, { root: true });
    return;
  }

  // No errors, proceed with creating PR
  const res = await createPR();
  if (res && res?.url) {
    window.open(res?.url, '_blank');
  }
};

const createPrToolTip = computed(() => {
  if (!validationResult.value.isError()) {
    return 'Create a PR for review and publishing';
  }
  return 'Cannot create PR due to syntax errors. Please view the Error tab for more details.';
});
const disableCreatePR = computed(() => {
  if (!checkFeatureToggle('aml_studio:pr_workflow')) {
    return false;
  }
  if (isUnsaved.value) {
    return true;
  }

  if (isCreatePrDisabled.value) {
    return true;
  }
  // does not have any commit ahead prod.
  if (aheadProductionCommitCount.value === 0) {
    return true;
  }
  return false;
});

</script>
