<template>
  <div class="h-1 w-full border-t border-solid border-[#E3E7ED]" />

  <div class="my-4">
    <div class="flex items-start justify-between align-top">
      <div>
        <div class="font-medium">
          Enable PR Workflow
        </div>

        <div class="mt-1 text-xs">
          Enforce pull request reviews before publishing changes.
          <a
            class="text-[#1B7CE4]"
            :href="generateDocsLink('/docs/continuous-integration/pr-workflow-auto-deploy')"
            target="_blank"
          >Learn more</a>
        </div>
      </div>

      <!-- Toggle switch for enabling/disabling PR workflow -->
      <span v-h-tooltip="{ content: DISABLE_EDIT_TEXT, disabled: currentProject.permissions.canUpdate }">
        <HSwitch
          v-model="enabledPrWorkflowSwitch"
          size="lg"
          :disabled="!currentProject.permissions.canUpdate || isTogglePrWorkflowSettings"
          data-ci="enable-pr-workflow-switch"
        />
      </span>
    </div>
    <!-- GitHub access token section - only shown when token exists -->
    <div
      v-if="prWorkflowSettings?.token !== undefined && prWorkflowSettings?.token !== null"
      class="mt-2 flex items-start justify-between gap-x-2 "
    >
      <div class="border-l border-l-gray-300 px-[10px]">
        <div class="font-medium">
          Github Access Token
        </div>
        <div class="font-light">
          Token with appropriate permissions to manage pull requests
        </div>
      </div>
      <div class="w-full">
        <!-- Masked password field for token -->
        <input
          type="password"
          class="w-full rounded-md border border-gray-300 bg-gray-200 p-2 text-gray-700"
          disabled
          value="**************************"
        >
        <!-- Display token expiration status -->
        <div
          v-if="tokenExpiredOrInvalid"
          class="mt-1 text-right text-xs font-light text-red-800"
        >
          This token is expired or no longer valid
        </div>
        <div
          v-else
          class="mt-1 text-right text-xs font-light opacity-50"
        >
          This token will expire on {{ formattedExpirationDate }}
        </div>
      </div>

      <!-- Edit token button -->
      <span v-h-tooltip="{ content: DISABLE_EDIT_TEXT, disabled: currentProject.permissions.canUpdate }">
        <HIcon
          name="pencil"
          class="cursor-pointer"
          data-ci="update-token-btn"
          @click="handleUpdateAccessTokenIconClick"
        />
      </span>

      <!-- Delete token button -->
      <span v-h-tooltip="{ content: DISABLE_EDIT_TEXT, disabled: currentProject.permissions.canUpdate }">
        <HIcon
          name="trash-alt"
          class="cursor-pointer text-red-600"
          data-ci="delete-token-btn"
          @click="handleDeleteTokenIconClick"
        />
      </span>
    </div>
  </div>

  <!-- Modal for enabling workflow / providing new token -->
  <ProvideAccessTokenModal
    v-model:shown="showProvideAccessTokenModal"
    :show-banner-token-expired="tokenExpiredOrInvalid"
    title="Provide Access Token"
    resolve-button="Validate"
    :resolve="resolveEnablePrWorkflowSettingsForModal"
  />

  <!-- Modal for updating an existing token -->
  <ProvideAccessTokenModal
    v-model:shown="showUpdateAccessTokenModal"
    title="Update Access Token"
    resolve-button="Validate"
    :resolve="resolveUpdateAccessTokenModal"
  />

  <!-- Confirmation modal for disabling PR workflow -->
  <HModal
    v-model:shown="showDisablePRWorkflowModal"
    title="Disable PR Workflow"
    resolve-button="Disable PR workflow"
    @resolve="disablePrWorkflowSettings"
  >
    <div>
      Disable PR workflow? Your access token will remain until manually removed.
    </div>
  </HModal>

  <!-- Confirmation modal for deleting the token -->
  <HModal
    v-model:shown="showDeleteAccessTokenModal"
    title="Delete Access Token"
    resolve-button="Delete token"
    @resolve="resolveDeleteAccessToken"
  >
    <div> Delete Access Token? This action will disable the PR workflow. </div>
  </HModal>
</template>

<script setup lang="ts">
import {
  computed, onMounted, reactive,
} from 'vue';
import { HModal, HSwitch, HIcon } from '@holistics/design-system';
import { useStore } from 'vuex';
import { ProjectData } from '@aml-studio/client/store/modules/project/state';
import { useRouter } from 'vue-router';
import { DateTime } from 'luxon';
import { generateDocsLink } from '@aml-studio/h/utils';
import { usePRWorkflowSettings } from './compositions/usePrWorkflowSettings';
import { DISABLE_EDIT_TEXT } from '../constants';
import ProvideAccessTokenModal from './components/ProvideAccessTokenModal.vue';

// Initialize PR workflow settings composable
const {
  prWorkflowSettings, enablePrWorkflowSettings, disablePrWorkflowSettings, deleteToken, updateAccessToken,
  tokenExpiredOrInvalid, isTogglePrWorkflowSettings,
} = usePRWorkflowSettings();

const store = useStore();
// Get current project data from store
const currentProject = computed<ProjectData>(() => store.getters['aml/project/currentProject']);

const router = useRouter();

// formats token expiration date to "DD Month YYYY"
const formattedExpirationDate = computed(() => {
  if (prWorkflowSettings.value?.tokenExpirationDate) {
    return DateTime.fromISO(prWorkflowSettings.value.tokenExpirationDate)
      .toFormat('dd MMMM yyyy');
  }
  return '';
});

// Central state for managing modal visibility
const modalStates = reactive({
  provideAccessToken: false,
  deleteAccessToken: false,
  disablePRWorkflow: false,
  updateAccessToken: false,
});

// checks if the user has permission to modify project settings
const hasRequiredPermissions = computed(() => {
  return currentProject.value.permissions.canUpdate;
});

// computed properties manage modal visibility, ensuring permissions are checked before showing
const showProvideAccessTokenModal = computed({
  get: () => hasRequiredPermissions.value && modalStates.provideAccessToken,
  set: (value: boolean) => {
    if (hasRequiredPermissions.value || !value) {
      modalStates.provideAccessToken = value;
    }
  },
});

const showDeleteAccessTokenModal = computed({
  get: () => hasRequiredPermissions.value && modalStates.deleteAccessToken,
  set: (value: boolean) => {
    if (hasRequiredPermissions.value || !value) {
      modalStates.deleteAccessToken = value;
    }
  },
});

const showDisablePRWorkflowModal = computed({
  get: () => hasRequiredPermissions.value && modalStates.disablePRWorkflow,
  set: (value: boolean) => {
    if (hasRequiredPermissions.value || !value) {
      modalStates.disablePRWorkflow = value;
    }
  },
});

const showUpdateAccessTokenModal = computed({
  get: () => hasRequiredPermissions.value && modalStates.updateAccessToken,
  set: (value: boolean) => {
    if (hasRequiredPermissions.value || !value) {
      modalStates.updateAccessToken = value;
    }
  },
});

// handles the PR workflow toggle switch logic based on current token state
// scenario 1: turning off -> disables workflow
// scenario 2: turning on with a valid token -> enables workflow
// scenario 3: turning on without a valid token -> prompts for a new token
const enabledPrWorkflowSwitch = computed({
  get () {
    return prWorkflowSettings?.value?.enabledPrWorkflow || false;
  },
  async set (value: boolean) {
    if (enabledPrWorkflowSwitch.value === value) {
      return;
    }
    if (!value) {
      // If turning off PR workflow
      await disablePrWorkflowSettings();
    } else if (prWorkflowSettings?.value?.token !== null && !tokenExpiredOrInvalid.value && !prWorkflowSettings?.value?.enabledPrWorkflow) {
      // If turning on PR workflow and valid token exists
      await enablePrWorkflowSettings();
    } else if (prWorkflowSettings?.value?.enabledPrWorkflow === false || tokenExpiredOrInvalid.value) {
      // If turning on PR workflow but no valid token exists
      showProvideAccessTokenModal.value = true;
    }
  },
});

// resolves the provide token modal; attempts to enable workflow and handles API failure
const resolveEnablePrWorkflowSettingsForModal = async (token: string) => {
  const success = await enablePrWorkflowSettings(token);
  if (!success) {
    // revert UI state if enabling workflow fails
    enabledPrWorkflowSwitch.value = false;
    showProvideAccessTokenModal.value = true;
  }
  return success;
};

// resolves the update token modal; attempts to update token via the composable
const resolveUpdateAccessTokenModal = async (token: string) => {
  const success = await updateAccessToken(token);
  return success;
};

// Show update token modal when edit icon is clicked
const handleUpdateAccessTokenIconClick = () => {
  showUpdateAccessTokenModal.value = true;
};

// resolves the delete token modal; triggers token deletion via the composable
const resolveDeleteAccessToken = async () => {
  await deleteToken();
};

// Show delete token confirmation modal when delete icon is clicked
const handleDeleteTokenIconClick = () => {
  showDeleteAccessTokenModal.value = true;
};

// checks for the `updateToken` flag in the navigation state and clears it
// this allows external navigation (e.g., from email link) to trigger the update token modal
const checkUpdateTokenFlag = () => {
  const hasUpdateToken = router.options.history.state?.updateToken === true;

  if (router.options.history.state && router.options.history.state.updateToken) {
    router.options.history.state.updateToken = false;

    window.history.replaceState(
      router.options.history.state,
      document.title,
    );
  }

  return hasUpdateToken;
};

// checks for the updateToken flag when the component mounts
onMounted(() => {
  if (checkUpdateTokenFlag()) {
    showUpdateAccessTokenModal.value = true;
  }
});

// We only need to check for the updateToken flag when the component mounts
// The onMounted hook at line 290 already handles this
// Removing the route watcher to prevent checking on every route change
</script>
