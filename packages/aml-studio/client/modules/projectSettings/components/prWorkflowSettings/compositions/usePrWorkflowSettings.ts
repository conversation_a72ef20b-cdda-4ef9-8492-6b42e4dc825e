import { usePRWorkflow } from '@aml-studio/client/pages/composables/usePRWorkflow';
import { MutationTypes } from '@aml-studio/client/store/modules/project/constants';
import { ProjectData } from '@aml-studio/client/store/modules/project/state';
import { PrWorkflowSettings } from '@aml-studio/client/types/allTypes';
import { snakeCaseToCamelCase } from '@aml-studio/client/utils/helpers';
import {
  DisablePrWorkflowResponse, EnablePrWorkflowResponse, UpdateTokenPrWorkflowResponse,
} from '@aml-studio/connector/services/services';
import { createInjectionState } from '@vueuse/core';
import { computed, ref } from 'vue';
import { useStore } from 'vuex';
import AMLManager from '@aml-studio/connector/AMLManager';
import { fail, success as showSuccess } from '@aml-studio/h/services/notifier';

const [useProvidePrWorkflowSettings, _usePRWorkflowSettings] = createInjectionState(() => {
  const { fetchLatestPR } = usePRWorkflow();
  const store = useStore();
  const currentProject = computed<ProjectData>(() => store.getters['aml/project/currentProject']);
  const prWorkflowSettings = computed<PrWorkflowSettings | undefined>(() => currentProject.value?.prWorkflowSettings);
  const projectId = computed(() => store.getters['aml/project/currentProjectId']);
  const isFetchingPrWorkflowSettings = ref(false);
  const isTogglePrWorkflowSettings = ref(false);
  let currentWorkflowPromise: Promise<any> | null = null;

  const executeWorkflowAction = async <T>(
    action: () => Promise<T>,
  ): Promise<T> => {
    try {
      if (currentWorkflowPromise) {
        await currentWorkflowPromise;
      }

      isTogglePrWorkflowSettings.value = true;
      const actionPromise = action();
      currentWorkflowPromise = actionPromise;

      const result = await actionPromise;
      return result;
    } finally {
      currentWorkflowPromise = null;
      isTogglePrWorkflowSettings.value = false;
    }
  };

  const enablePrWorkflowSettings = (token?: string) => {
    return executeWorkflowAction(async () => {
      const result = await AMLManager.AMLConnector.ProjectService.enablePrWorkflow({
        projectId: projectId.value,
        token,
      }) as EnablePrWorkflowResponse;

      if (result.errors || result.data?.error_message) {
        let errorMessage: string | undefined;
        if (result.errors && result.errors.length > 0) {
          errorMessage = result.errors[0].toString();
        } else {
          errorMessage = result.data?.error_message;
        }
        fail(errorMessage);
      } else if (result.success) {
        showSuccess('Enable PR Workflow successfully');
        const responseData = result.data ? snakeCaseToCamelCase(result.data as any, true) as Record<string, any> : {};
        store.commit(`aml/project/${MutationTypes.SET_CURRENT_PROJECT_DATA}`, {
          ...currentProject.value,
          prWorkflowSettings: {
            enabledPrWorkflow: true,
            tokenExpirationDate: responseData.data.tokenExpirationDate,
            token: 'notnil',
          },
        });
        await fetchLatestPR();
      }
      return result;
    });
  };

  const disablePrWorkflowSettings = () => {
    return executeWorkflowAction(async () => {
      const result = await AMLManager.AMLConnector.ProjectService.disablePrWorkflow({
        projectId: projectId.value,
        isDeleteToken: false,
      }) as DisablePrWorkflowResponse;

      const { success } = result;
      if (!success) {
        let errorMessage: string | undefined;
        if (result.errors && result.errors.length > 0) {
          errorMessage = result.errors[0].toString();
        } else {
          errorMessage = result.data?.error_message;
        }
        fail(errorMessage);
      } else {
        showSuccess('Disable PR Workflow successfully');
        store.commit(`aml/project/${MutationTypes.SET_CURRENT_PROJECT_DATA}`, {
          ...currentProject.value,
          prWorkflowSettings: {
            ...currentProject.value.prWorkflowSettings,
            enabledPrWorkflow: false,
          },
        });
      }
      return success;
    });
  };

  const deleteToken = () => {
    return executeWorkflowAction(async () => {
      const result = await AMLManager.AMLConnector.ProjectService.disablePrWorkflow({
        projectId: projectId.value,
        isDeleteToken: true,
      }) as DisablePrWorkflowResponse;

      const { success } = result;
      if (!success) {
        let errorMessage: string | undefined;
        if (result.errors && result.errors.length > 0) {
          errorMessage = result.errors[0].toString();
        } else {
          errorMessage = result.data?.error_message;
        }
        fail(errorMessage);
      } else {
        showSuccess('Disable PR Workflow successfully');
        store.commit(`aml/project/${MutationTypes.SET_CURRENT_PROJECT_DATA}`, {
          ...currentProject.value,
          prWorkflowSettings: {
            enabledPrWorkflow: false,
            tokenExpirationDate: undefined,
            token: null,
          },
        });
      }
      return success;
    });
  };

  const updateAccessToken = async (token: string) => {
    return executeWorkflowAction(async () => {
      const result = await AMLManager.AMLConnector.ProjectService.updateTokenPrWorkflow({
        projectId: projectId.value,
        token,
      }) as UpdateTokenPrWorkflowResponse;

      if (result.errors) {
        let errorMessage: string | undefined;
        if (result.errors && result.errors.length > 0) {
          errorMessage = result.errors[0].toString();
        } else {
          errorMessage = result.data?.error_message;
        }
        fail(errorMessage);
      } else if (result.success) {
        showSuccess('Update token successfully');

        const responseData = result.data ? snakeCaseToCamelCase(result.data as any, true) as Record<string, any> : {};
        store.commit(`aml/project/${MutationTypes.SET_CURRENT_PROJECT_DATA}`, {
          ...currentProject.value,
          prWorkflowSettings: {
            enabledPrWorkflow: true,
            tokenExpirationDate: responseData.data.tokenExpirationDate,
            token: 'notnil',
          },
        });
      }
      return result;
    });
  };

  const tokenNearlyExpired = computed(() => {
    if (prWorkflowSettings.value && prWorkflowSettings.value.tokenExpirationDate) {
      const expirationDate = new Date(prWorkflowSettings.value.tokenExpirationDate);
      const currentDate = new Date();
      const remainTime = expirationDate.getTime() - currentDate.getTime();

      // show alert if less than 5 days, but more than 0
      return remainTime < 5 * 24 * 60 * 60 * 1000 && remainTime > 0;
    }
    return false;
  });

  const tokenExpiredOrInvalid = computed(() => {
    if (prWorkflowSettings?.value?.token === undefined || prWorkflowSettings?.value?.token === null) {
      return false;
    }
    if (prWorkflowSettings.value && prWorkflowSettings.value.tokenExpirationDate) {
      const expirationDate = new Date(prWorkflowSettings.value.tokenExpirationDate);
      const currentDate = new Date();

      return expirationDate.getTime() <= currentDate.getTime();
    }
    return false;
  });

  const isEnabledPrWorkflowSettings = computed(() => {
    return prWorkflowSettings?.value?.enabledPrWorkflow || false;
  });

  return {
    isEnabledPrWorkflowSettings,
    prWorkflowSettings,
    enablePrWorkflowSettings,
    disablePrWorkflowSettings,
    deleteToken,
    updateAccessToken,
    isFetchingPrWorkflowSettings,
    tokenNearlyExpired,
    tokenExpiredOrInvalid,
    isTogglePrWorkflowSettings,
  };
});

export { useProvidePrWorkflowSettings };

export function usePRWorkflowSettings () {
  const PRWorkflowStore = _usePRWorkflowSettings();
  if (!PRWorkflowStore) {
    throw new Error('Please call usePrWorkflowSettings on the appropriate parent component');
  }
  return PRWorkflowStore;
}
