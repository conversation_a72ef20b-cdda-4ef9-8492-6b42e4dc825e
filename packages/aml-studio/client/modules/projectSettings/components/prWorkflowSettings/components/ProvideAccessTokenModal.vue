<template>
  <HModal
    v-model:shown="shown"
    :title="props.title"
    resolve-button="Validate"
    :prevent-press-escape="true"
    :prevent-click-outside="true"
    :prevent-close-on-resolve="preventCloseOnResolve"
    size="md"
    no-footer
    @dismiss="dismiss"
    @resolve="resolve"
  >
    <template #body>
      <HBanner
        v-if="props.showBannerTokenExpired"
        type="info"
        title="The old token is expired or no longer valid"
        theme="full-width"
      >
        Provide a new token to enable PR workflow
      </HBanner>
      <div class="p-6">
        <div class="font-light">
          <div class="font-base text-xs">
            <div class="mb-4 font-normal">
              Follow these steps to create new token:
            </div>
            <ol class="font-light">
              <li class="mb-4 flex items-center gap-1 font-semibold">
                1. <a
                  href="https://github.com/settings/personal-access-tokens/new"
                  target="_blank"
                >Create a new token</a> in Github's Developer Settings
              </li>
              <li class="mb-4 font-semibold">
                2. Configure Token Settings
                <ol class="ml-4 mt-1 flex flex-col gap-1 font-light">
                  <li class="flex items-center gap-1">
                    • Set an approriate expiration date (we recommend 90 days)
                  </li>
                  <li class="flex items-center gap-1">
                    • Select only the repository connected to Holistics
                  </li>
                </ol>
              </li>
              <li class="mb-4 font-semibold">
                3. Configure these specific repository permissions
                <ol class="ml-4 mt-1 flex flex-col gap-1 font-light">
                  <li class="flex items-center gap-1">
                    • Pull requests: Read-only
                  </li>
                  <li class="flex items-center gap-1">
                    • Webhooks: Read and write
                  </li>
                </ol>
              </li>
              <li class="mb-4 font-semibold">
                4. Click "Generate token" and copy the token to your clipboard
              </li>
              <li class="mb-2 font-semibold">
                5. Paste the token below to complete the integration
              </li>
            </ol>
          </div>
        </div>
        <form
          action="submit"
          class="relative"
        >
          <input
            v-model="token"
            :type="isPasswordVisible ? 'text' : 'password'"
            class="h-8 w-full rounded-md border border-gray-500 p-2 pr-8"
            :class="errorMessage ? 'border-red-500' : 'border-gray-300'"
            placeholder="Token"
            data-ci="pr-workflow-token-input"
            @input="errorMessage=undefined"
          >
          <button
            type="button"
            class="absolute right-2 top-1/2 -translate-y-1/2"
            @click="togglePasswordVisibility"
          >
            <HIcon
              :name="isPasswordVisible ? 'eye-open' : 'eye-slash'"
              size="lg"
            />
          </button>
        </form>
        <div
          v-if="errorMessage && missingPermissions.length === 0"
          class="mt-1 font-light text-red-500"
        >
          {{ errorMessage }}
        </div>

        <HBanner
          v-if="missingPermissions.length > 0"
          class="mt-4"
          type="danger"
          title="Missing required permissions"
        >
          This token is missing <span
            v-for="(permission, index) in missingPermissions"
            :key="index"
          ><strong>"{{ permission }}"</strong>{{ index < missingPermissions.length - 1 ? ' and ' : '' }}</span>. Please create a new token with correct settings:
          <ul class="my-1 flex flex-col gap-1 px-1 font-light">
            <li>• Pull Request: Read</li>
            <li>• Webhook: Read & Write</li>
          </ul>
          <a
            class="text-blue-600"
            href="https://github.com/settings/personal-access-tokens/new"
            target="_blank"
          >
            Add new token
          </a>
        </HBanner>
        <HBanner
          class="mt-4"
          type="warning"
          title="Important note"
          description="Save your token in a secure location. Github will only show it once"
        />
      </div>
      <div class="flex justify-end gap-x-2 border-t bg-[#F9FBFC] px-6 py-4">
        <HButton
          type="secondary-default"
          data-ci="dismiss-pr-workflow-settings"
          @click="dismiss"
        >
          Cancel
        </HButton>
        <HButton
          :disabled="disableResolveButton"
          :icon="disableResolveButton ? 'loading' : undefined"
          :icon-spin="disableResolveButton"
          type="primary-highlight"
          data-ci="resolve-pr-workflow-settings"
          @click="resolve"
        >
          {{ props.resolveButton }}
        </HButton>
      </div>
    </template>
  </HModal>
</template>

<script setup lang="ts">
import { snakeCaseToCamelCase } from '@aml-studio/client/utils/helpers';
import { EnablePrWorkflowResponse, UpdateTokenPrWorkflowResponse } from '@aml-studio/connector/services/services';
import {
  HButton, HModal, HBanner, HIcon,
} from '@holistics/design-system';
import { ref } from 'vue';

const shown = defineModel<boolean>('shown', { default: false });
const token = ref<string>('');
const errorMessage = ref<string | undefined>(undefined);
const disableResolveButton = ref<boolean>(false);
const isPasswordVisible = ref<boolean>(false);
const missingPermissions = ref<string[]>([]);
const preventCloseOnResolve = ref<boolean>(false);

const props = withDefaults(defineProps<{
  title?: string;
  resolveButton?: string;
  resolve:(token: string) => Promise<EnablePrWorkflowResponse | UpdateTokenPrWorkflowResponse>;
  dismiss?: () => void;
  showBannerTokenExpired?: boolean;
}>(), {
  title: 'Provide Access Token',
  resolveButton: 'Validate',
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  dismiss: () => {},
  showBannerTokenExpired: false,
});

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value;
};

const resolve = async () => {
  disableResolveButton.value = true;
  missingPermissions.value = [];

  try {
    if (!token.value || token.value === '') {
      errorMessage.value = 'Please enter the token';
      return;
    }
    if (token.value) {
      const { success, data } = await props.resolve(token.value);
      const responseData = data ? snakeCaseToCamelCase(data as any, true) as Record<string, any> : {};
      if (!success) {
        errorMessage.value = responseData?.errorMessage as string;
      } else {
        token.value = '';
        errorMessage.value = undefined;
        shown.value = false;
      }
    }
  } finally {
    disableResolveButton.value = false;
  }
};

const dismiss = () => {
  errorMessage.value = undefined;
  token.value = '';
  missingPermissions.value = [];
  shown.value = false;
  props.dismiss();
};
</script>
