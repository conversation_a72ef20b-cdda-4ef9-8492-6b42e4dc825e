<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { DateTime } from 'luxon';
import { usePRWorkflowSettings } from '../projectSettings/components/prWorkflowSettings/compositions/usePrWorkflowSettings';
import { ProjectData } from '../../store/modules/project/state';
import WarningIcon from './icons/WarningIcon.vue';
import DangerIcon from './icons/DangerIcon.vue';

const { tokenNearlyExpired, tokenExpiredOrInvalid, prWorkflowSettings } = usePRWorkflowSettings();
const store = useStore();
const currentProject = computed<ProjectData>(() => store.getters['aml/project/currentProject']);
// Format the expiration date
const formattedExpirationDate = computed(() => {
  if (prWorkflowSettings.value?.tokenExpirationDate) {
    const expirationDate = DateTime.fromISO(prWorkflowSettings.value.tokenExpirationDate);
    return expirationDate.toFormat('MMMM d, yyyy');
  }
  return '';
});
const router = useRouter();

const handleUpdateTokenClick = () => {
  router.push({
    path: `/studio/projects/${currentProject.value.id}/settings`,
    state: {
      updateToken: true,
    },
  });
};

const bannerStyle = computed(() => {
  if (tokenExpiredOrInvalid.value) {
    return 'border-red-300 bg-red-50';
  }
  return 'border-orange-300 bg-orange-50';
});
</script>

<template>
  <div
    v-if="(tokenNearlyExpired || tokenExpiredOrInvalid) && prWorkflowSettings?.enabledPrWorkflow"
    :class="`inline-flex items-center gap-x-2 border-y px-4 py-3  ${bannerStyle}`"
  >
    <WarningIcon
      v-if="tokenExpiredOrInvalid"
    />
    <DangerIcon
      v-else
    />

    <span
      v-if="tokenNearlyExpired"
      class="font-medium"
    >
      Github access token will expire on <strong class="font-bold">{{ formattedExpirationDate }}</strong>. Auto-publish & real-time PR status will be unavailable.
    </span>
    <span
      v-else-if="tokenExpiredOrInvalid"
      class="font-medium"
      v-text="'Github access token expired. Auto-publish & real-time PR status unavailable.'"
    />
    <button
      class="font-medium text-blue-600"
      @click="handleUpdateTokenClick"
    >
      Add new token
    </button>
  </div>
</template>
