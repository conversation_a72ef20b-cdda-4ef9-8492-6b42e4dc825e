<template>
  <PrStatusButton
    v-if="showPRStatus"
    :show-loading="false"
    :disabled="false"
    :btn-text="latestPRStatus?.prNumber ? `#${String(latestPRStatus.prNumber).slice(-6)}` : 'Unknown'"
    :tooltip-text="`View PR #${latestPRStatus?.prNumber} in Github. <br/> - Publishes automatically when PR is merged`"
    @click="redirectToGithubWithPROpen"
  />
</template>
<script setup lang="ts">
import { usePRWorkflow } from '@aml-studio/client/pages/composables/usePRWorkflow';
import { computed } from 'vue';
import { checkFeatureToggle } from '@aml-studio/h/services';
import { useGitSync } from '@aml-studio/client/pages/composables/useGitSync';
import { PullRequestStatus } from '@aml-studio/client/types/allTypes';
import PrStatusButton from './components/PrStatusButton.vue';
import { usePRWorkflowSettings } from '../projectSettings/components/prWorkflowSettings/compositions/usePrWorkflowSettings';
import { useGitRepoState } from '../Git/composables/useGitRepoState';

const { latestPRStatus, baseUrl, isFetchingLatestPR } = usePRWorkflow();
const { prWorkflowSettings } = usePRWorkflowSettings();
const { isSyncing } = useGitSync();
const isEnabledPrWorkflowSettings = computed(() => {
  return prWorkflowSettings?.value?.enabledPrWorkflow || false;
});
const { isDeploying } = useGitRepoState();

const redirectToGithubWithPROpen = async () => {
  if (latestPRStatus.value) {
    window.open(`${baseUrl.value}/pull/${latestPRStatus.value.prNumber}`, '_blank'); // Open GitHub PR page in new tab
  }
};

const showPRStatus = computed(() => {
  if (!checkFeatureToggle('aml_studio:pr_workflow') || !isEnabledPrWorkflowSettings.value) {
    return false;
  }
  if (isDeploying.value) {
    return false;
  }
  if ((isFetchingLatestPR.value && !latestPRStatus.value) || isSyncing.value) {
    return false;
  }
  if (latestPRStatus.value?.state === PullRequestStatus.Open) {
    return true;
  }
  if (!latestPRStatus.value) {
    return false;
  }
  return false;
});
</script>
