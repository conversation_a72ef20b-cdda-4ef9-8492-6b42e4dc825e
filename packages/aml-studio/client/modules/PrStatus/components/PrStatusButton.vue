<template>
  <HTooltip
    :content="tooltipText"
    placement="bottom-end"
    :html="true"
  >
    <HButton
      :type="disabled ? 'secondary-default' : 'primary-highlight'"
      size="sm"
      data-ci="create-pr-action"
      :disabled="disabled"
      class="!bg-[#CCEDE0]"
    >
      <div class="flex items-center gap-x-1 p-0.5">
        <OpenPrIcon />
        <span class="text-[#145239]">{{ btnText }}</span>
      </div>
    </HButton>
  </HTooltip>
</template>
<script setup lang="ts">
import { HButton, HTooltip } from '@holistics/design-system';
import OpenPrIcon from './icons/OpenPrIcon.vue';

withDefaults(defineProps<{
  btnText: string;
  disabled: boolean;
  showLoading: boolean;
  tooltipText?: string;
}>(), {
  disabled: false,
  showLoading: false,
  btnText: 'Create PR',
  tooltipText: undefined,
});

</script>
