import {
  ref,
  Ref,
} from 'vue';
import {
  concat,
} from 'lodash';
import {
  type DropdownOption,
} from '@holistics/design-system';
import {
  useSearchHistoryBrowsingState,
} from './useSearchHistoryBrowsingState';

const buildDropdownOptions = (key: string): DropdownOption => {
  return {
    key: `option-${key}`,
    label: key,
  };
};

const buildHeaderDropdownOptions = (key: string): DropdownOption => {
  return {
    key: `header-${key}`,
    label: key,
  };
};

describe('useSearchHistoryBrowsingState', () => {
  it('should increase browsing index', () => {
    const options = ref(concat(
      buildHeaderDropdownOptions('Search options'),
      buildDropdownOptions('0'),
      buildHeaderDropdownOptions('Recent Search'),
      buildDropdownOptions('2'),
    )) as unknown as Ref<DropdownOption[]>;

    const { currentBrowsingIndex, increaseBrowsingIndex, decreaseBrowsingIndex } = useSearchHistoryBrowsingState(options);
    expect(currentBrowsingIndex.value).toBe(-1);

    increaseBrowsingIndex();
    expect(currentBrowsingIndex.value).toBe(0);

    increaseBrowsingIndex();
    increaseBrowsingIndex();
    increaseBrowsingIndex();
    expect(currentBrowsingIndex.value).toBe(3);

    increaseBrowsingIndex();
    expect(currentBrowsingIndex.value).toBe(0);

    decreaseBrowsingIndex();
    expect(currentBrowsingIndex.value).toBe(3);
  });
});
