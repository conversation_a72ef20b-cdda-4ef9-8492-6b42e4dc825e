import {
  Ref, ref, readonly,
} from 'vue';
import {
  type DropdownOption,
} from '@holistics/design-system';
import { isEmpty } from 'lodash';

export function useSearchHistoryBrowsingState (options: Ref<DropdownOption[]>) {
  const STARTING_INDEX = -1;
  const FIRST_OPTION_INDEX = 0;

  const browsingIndex = ref(STARTING_INDEX);

  const currentBrowsingIndex = readonly(browsingIndex);

  const increaseBrowsingIndex = () => {
    if (isEmpty(options.value)) {
      return;
    }

    if (browsingIndex.value === options.value.length - 1) {
      browsingIndex.value = FIRST_OPTION_INDEX;
      return;
    }

    browsingIndex.value++;
  };

  const decreaseBrowsingIndex = () => {
    if (isEmpty(options.value)) {
      return;
    }

    if (browsingIndex.value === FIRST_OPTION_INDEX) {
      browsingIndex.value = options.value.length - 1;
      return;
    }

    browsingIndex.value--;
  };

  const resetBrowsingIndex = () => {
    browsingIndex.value = STARTING_INDEX;
  };

  return {
    currentBrowsingIndex,
    increaseBrowsingIndex,
    decreaseBrowsingIndex,
    resetBrowsingIndex,
  };
}
