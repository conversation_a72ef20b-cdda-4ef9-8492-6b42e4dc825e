# typed: true
# frozen_string_literal: true

# @note This class is used to generate the result data for the widgets when exporting dashboard
module ImageExporters::Services
  class WidgetResultGenerator
    extend T::Sig

    Block = T.type_alias do
      T.any(
        Dashboards::Types::TypedWidget,
        DashboardsV4::Materialized::Block,
      )
    end

    sig do
      params(
        widgets: T.any(
          T::Array[Block],
          ActiveRecord::Associations::CollectionProxy,
          ActiveRecord::Relation,
        ),
        job: Job,
        permission_rules: T.untyped,
        query_processing_timezone: T.untyped,
        widget_viz_conditions_map: T.untyped,
        check_widget_permission: T::Boolean,
        should_build_table_options: T::Boolean,
      ).void
    end
    def initialize(
      widgets:,
      job:,
      permission_rules:,
      query_processing_timezone:,
      widget_viz_conditions_map: {},
      check_widget_permission: true,
      should_build_table_options: false
    )
      @widgets = widgets.to_a
      @job = job
      @permission_rules = permission_rules
      @query_processing_timezone = query_processing_timezone
      @widget_viz_conditions_map = widget_viz_conditions_map || {}
      @check_widget_permission = check_widget_permission
      @should_build_table_options = should_build_table_options

      setup_widget_permissions if check_widget_permission
    end

    sig { params(widget_cache_map: T.untyped).returns(T.untyped) }
    def generate(widget_cache_map)
      normalized_widget_cache_map = normalize_widget_cache_map(widget_cache_map)

      @widgets.map do |widget|
        process_widget_data(widget, normalized_widget_cache_map)
      end
    end

    private

    sig { void }
    def setup_widget_permissions
      @widget_ability = DashboardWidgetsAbility.new(@job.user)
      Abilities::Services::AllowPublicDrillthroughService.new.call(@widget_ability)
    end

    sig { params(widget: Block, widget_cache_map: T.untyped).returns(T.untyped) }
    def process_widget_data(widget, widget_cache_map)
      result = nil
      error = nil

      begin
        result = generate_widget_result(widget, widget_cache_map)
      rescue StandardError => e
        log_widget_error(widget, e)
        error = e.message
      end

      {
        widget: prepare_widget_data(widget),
        result: result,
        error: error,
      }
    end

    sig { params(widget: Block, error: StandardError).void }
    def log_widget_error(widget, error)
      backtrace = error.backtrace&.join("\n") || ''
      @job.logger.debug("Widget #{widget.id} error: #{error.message}\n#{backtrace}")
    end

    sig { params(widget: Block).returns(T.untyped) }
    def prepare_widget_data(widget)
      if widget.is_a?(DashboardWidget)
        widget_title = determine_widget_title(widget)
        widget.as_json.merge({ title: widget_title })
      else
        # For dashboard v4, the widget data included in dashboard.definition
        # So we don't necessary to use widget data
        widget.uname
      end
    end

    sig { params(widget: Block).returns(String) }
    def determine_widget_title(widget)
      if widget.is_a?(DashboardWidget) && widget.data[:hide_title_and_description]
        ''
      else
        widget.title.blank? && widget.is_a?(DashboardWidget) && widget.source ? widget.source.title : widget.title
      end
    end

    sig do
      params(
        widget_cache_map: T.untyped,
      ).returns(T::Hash[T.any(Integer, String), Dashboards::Values::WidgetCacheMetadata])
    end
    def normalize_widget_cache_map(widget_cache_map)
      (widget_cache_map || {}).map do |widget_id, data|
        next nil unless data

        metadata = create_widget_cache_metadata(widget_id, data)
        [widget_id, metadata]
      end.compact.to_h
    end

    sig { params(widget_id: T.any(Integer, String), data: T.untyped).returns(Dashboards::Values::WidgetCacheMetadata) }
    def create_widget_cache_metadata(widget_id, data)
      case data
      when Dashboards::Values::WidgetCacheMetadata
        data
      else
        Dashboards::Values::WidgetCacheMetadata.new(
          widget_id: widget_id,
          cache_key: data,
        )
      end
    end

    sig { params(widget: Block, widget_cache_map: T.untyped).returns(T.untyped) }
    def generate_widget_result(widget, widget_cache_map)
      check_widget_permission(widget)

      case widget.source_type
      when 'QueryReport', 'VizBlock'
        process_query_report_widget(widget, widget_cache_map)
      when 'QueryMetric'
        ImageExporters::MetricWidget.new(widget, job: @job).generate_result_data
      else
        widget.title
      end
    end

    sig { params(widget: Block).void }
    def check_widget_permission(widget)
      if @check_widget_permission && @widget_ability.cannot?(:read, widget)
        raise Holistics::PermissionDenied, 'Permission Required'
      end
    end

    sig do
      params(
        widget: Block,
        widget_cache_map: T.untyped,
      ).returns(T.untyped)
    end
    def process_query_report_widget(widget, widget_cache_map)
      cache_metadata = widget_cache_map[widget.id]

      if (error = cache_metadata.error)
        raise error
      elsif (cache_key = cache_metadata.cache_key)
        generate_query_report_result(widget, cache_key)
      else
        nil
      end
    end

    sig do
      params(
        widget: Block,
        cache_key: T.untyped,
      ).returns(T.untyped)
    end
    def generate_query_report_result(widget, cache_key)
      origin_report = widget.is_a?(DashboardWidget) ? widget.source : widget
      options = build_query_report_options(widget)
      viz_conditions = @widget_viz_conditions_map[widget.id]

      ImageExporters::QueryReport.new(
        origin_report,
        job: @job,
        viz_conditions: viz_conditions,
        permission_rules: @permission_rules,
        query_processing_timezone: @query_processing_timezone,
      ).generate_renderer_input(cache_key, **options)
    end

    sig do
      params(
        widget: Block,
      ).returns(T.untyped)
    end
    def build_query_report_options(widget)
      return {} unless @should_build_table_options && widget.is_a?(DashboardWidget) && widget.data.try(:[], :view_mode) == 'table'
      # Only build table options for Query Report version 2
      {
        render_raw_table: true,
        page_size: widget.data.try(:[], :row_limit) || 15,
      }
    end
  end
end
