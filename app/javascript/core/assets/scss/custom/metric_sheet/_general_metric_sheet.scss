@import 'header_metric_sheet';
@import 'chart_metric_sheet';

.h-metric-sheet {
  @apply ag-standard;
  --ag-row-border-width: 0px; // Metric sheets typically don't have row borders

  .ag-root {
    background: var(--ag-background-color, theme('colors.white'));
  }

  /* The last column in metric sheet already have border from the wrapper, remove AG-Grid's */
  .ag-header-cell {
    &.ag-column-last::before {
      width: 0;
    }
  }
  .ag-header-group-cell {
    &.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
      width: 0;
    }
  }

  .ag-cell {
    &.ag-column-last {
      border-right-width: 0;
    }
  }
}
