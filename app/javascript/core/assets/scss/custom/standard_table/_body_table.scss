.ag-standard {
  .ag-root-wrapper {
    .ag-center-cols-container {
      background: var(--ag-background-color, theme('colors.white'));
    }

    .ag-row {
      &.ag-row-odd, &.ag-row-even {
        @apply border-b-0;
      }

      &:has(.h-sub-total-cell) {
        background: var(--ag-background-color, theme('colors.white'));
      }

      &:not(.ag-row-last, .ag-row-pinned:last-child) {
        .ag-cell:not(
          .h-range-selection-cell,
          .h-last-cell-in-row-group,
          :focus,
          .h-conditional-formatting-border
        ) {
          border-bottom: var(--ag-row-border);
        }
      }
    }
  }
}
