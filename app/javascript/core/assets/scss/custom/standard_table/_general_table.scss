@import 'header_table';
@import 'body_table';
@import 'floating_row_table';
@import 'pinned_column_table';

.ag-standard {
  --ag-header-column-border-height: 100%;
  --ag-cell-horizontal-padding: var(--ag-spacing);
  --ag-header-column-resize-handle-width: 0px;
  --ag-wrapper-border-radius: 3px;
  --ag-row-height: calc(var(--ag-font-size) * $default-table-line-height-ratio + var(var(--h-table-cell-padding-bottom, $default-vertical-padding) + var(--h-table-cell-padding-top, $default-vertical-padding)) - var(--h-table-border-width, 1px));
  --ag-line-height: calc(var(--ag-font-size) * $default-table-line-height-ratio - var(--h-table-border-width, 1px));

  .ag-root-wrapper {
    border-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));

    background-color: theme('colors.white');

    .ag-center-cols-container,
    .ag-pinned-left-cols-container {
      background: var(--ag-background-color, theme('colors.white'));
    }

    /* Spacing style */
    .ag-cell,
    .ag-header-cell,
    .ag-header-group-cell {
      padding-left: var(--h-table-cell-padding-left, $default-horizontal-padding);
      padding-right: var(--h-table-cell-padding-right, $default-horizontal-padding);
      line-height: calc(var(--ag-font-size) * $default-table-line-height-ratio);
    }
    // Component-specific padding
    .ag-cell,
    .ag-header-cell {
      padding-top: calc(var(--h-table-cell-padding-top, $default-vertical-padding) - var(--h-table-border-width, 1px));
      padding-bottom: calc(var(--h-table-cell-padding-bottom, $default-vertical-padding) - var(--h-table-border-width, 1px));
    }
    .ag-header-group-cell .ag-header-cell-comp-wrapper,
    // AG Grid automatically calculates row height for header group cells in wrapper instead of cell
    .ag-floating-bottom .ag-cell {
      // 1px comes from ag-grid
      padding-top: calc(var(--h-table-padding-top, $default-vertical-padding) - var(--h-table-border-width, 1px));
      padding-bottom: calc(var(--h-table-padding-bottom, $default-vertical-padding) - var(--h-table-border-width, 1px) - 1px);
    }
  }

  .h-loading-cell {
    @apply bg-gray-300 rounded-3xl animate-pulse h-4 mt-0.5;
  }

  .h-number-cell {
    @apply text-right tabular-nums;
  }

  .h-sub-total-title-cell {
    @apply text-left;
  }

  .h-row-number-cell {
    @apply tabular-nums;
  }

  .h-aggregated-cell {
    @apply font-semibold;
  }

  .h-cross-filter {
    &.h-filterable-element {
      @apply cursor-pointer;
    }

    &.h-active-element {
      @apply opacity-100;
    }

    &.h-inactive-element {
      @apply opacity-65;
    }
  }

  .h-range-selection-cell {
    @apply text-blue-700;
    // Product design: keep cell border for range selection cells
    border-bottom-width: var(--h-table-border-width, 1px);
    border-bottom-color: theme('colors.white');
    border-right-width: var(--h-table-border-width, 1px);

    &.h-background-color {
      @apply bg-blue-50;
    }

    &.h-left-border {
      @apply border-l-blue-700;
    }

    &.h-right-border {
      @apply border-r-blue-700;
    }

    &.h-top-border {
      @apply border-t border-t-blue-700;
    }

    &.h-bottom-border {
      @apply border-b-blue-700;
    }

    &.h-row-number-cell {
      @apply bg-blue-50;
      &.h-row-number-dragging {
        @apply bg-blue-50 text-blue-700;
      }
    }
  }
}