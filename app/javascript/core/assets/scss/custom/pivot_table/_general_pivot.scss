@import 'column_header_pivot';
@import 'row_header_pivot';
@import 'value_header_pivot';
@import 'body_pivot';
@import 'floating_row_pivot';
@import 'pinned_pivot';

.h-pivot {
  @apply ag-standard;

  --ag-pinned-column-border: var(--ag-pinned-column-border, 3px) solid var(--h-table-border-color, theme('colors.gray.400'));

  .ag-cell {
    border-right-width: var(--h-table-column-border-width, var(--h-table-border-width, 1px));

    /* The last column should use table border color instead of grid border color*/
    &.ag-column-last {
      // override the AG-Grid border style
      border-right-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));
      border-right-color: var(--ag-border-color, $default-border-color);
    }
  }

  .ag-header-cell {
    // AG-Grid adds header border styling using pseudo-elements
    &.ag-column-last::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }
  }

  .ag-header-group-cell {
    // AG-Grid adds header border styling using pseudo-elements
    &.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }

    &:not(.ag-column-last)::after {
      border-right: var(--ag-header-column-border);
      border-right-color: var(--h-table-grid-color, var(--h-table-border-color, $default-border-color));
    }
  }

  .h-last-cell-in-column-group { // Border for last cell in a column group
    @apply border-r focus:border-r-blue-400;
    border-right: var(--ag-column-border);
  }

  .ag-row {
    // Note: AG-Grid shows `total` title for pinned and unpinned area
    // Product Requirement: Show `total` title on the first column if had
    .h-header-total-title-cell:not(.ag-column-first) {
      @apply text-transparent;

      &.h-range-selection-cell {
        @apply text-transparent;
      }
    }
  }

  .h-conditional-formatting-border {
    @apply focus:border-b-blue-700;
  }
}
