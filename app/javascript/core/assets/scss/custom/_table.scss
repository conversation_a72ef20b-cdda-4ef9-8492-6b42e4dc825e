// // Import base variables first
// @import 'variables'; // Contains $default-*, $absolute-*, etc.

// // Import general, non-table-specific styles
// @import 'general'; // Contains .widget-viz-container, @keyframes shine, etc.

// // Import Table-specific styles (AG-Grid based)
// @import 'standard_table/general_table'; // Imports header, body, floating, pinned_column for .ag-standard
// @import 'pivot_table/general_pivot'; // Imports column_header, row_header, value_header, body, floating, pinned for .h-pivot
// @import 'metric_sheet/general_metric_sheet'; // Imports header, body, chart, pinned for .h-metric-sheet
// @import 'cohort_retention/general_cohort'; // Imports header, body, pinned for .h-cohort-retention

// // Import Data Table styles (AG-Grid based, specific .h-table overrides)
// @import 'data_table/general_data_table'; // Imports header, body, pinned for .h-table

// // Import Legacy Handsontable styles (Load last)
// // Includes base .handsontable, .ht_*, .pivot-table-wrapper, .data-table-wrapper,
// // .retention-heatmap, .metric-sheet-table, .conversion-funnel-table styles
// @import 'handsontable';

@import 'table_backup';
