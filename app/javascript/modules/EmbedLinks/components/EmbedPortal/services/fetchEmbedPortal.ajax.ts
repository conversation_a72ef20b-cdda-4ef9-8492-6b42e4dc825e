import { errorMessageFromAjax, standardGet } from '@/core/services/ajax';

export const fetchEmbedPortal = async (embedPortalId: number): Promise<{
  success: boolean;
  data?: any,
  error?: any
}> => {
  try {
    const url = `/api/v2/embed/embed_portals/${embedPortalId}`;
    const data = await standardGet(url);

    return {
      success: true,
      data,
    };
  } catch (error: any) {
    return {
      success: false,
      error: errorMessageFromAjax(error),
    };
  }
};
