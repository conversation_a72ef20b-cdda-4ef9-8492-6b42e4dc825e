import * as AqlHelperSource from './aqlHelperSource';
import { aqlExpressions, allAqlFunctions } from '../aqlEditorExpressionTemplates';
import { aqlSignatures } from '../aqlEditorSignatureHelpTemplates';

export const getAqlExpressions = async (fromAqlHelper: boolean) => {
  // Cannot directly check feature toggles here because its not supported in web workers context.
  if (!fromAqlHelper) return aqlExpressions;

  const result = await AqlHelperSource.getTemplates();
  return result;
};

export const getAllAqlExpressions = async (fromAqlHelper: boolean) => {
  // Cannot directly check feature toggles here because its not supported in web workers context.
  if (!fromAqlHelper) return allAqlFunctions;

  const result = await getAqlExpressions(fromAqlHelper);
  return result;
};

export const getAqlSignatures = async (fromAqlHelper: boolean) => {
  // Cannot directly check feature toggles here because its not supported in web workers context.
  if (!fromAqlHelper) return aqlSignatures;

  const result = await AqlHelperSource.getSignatures();
  return result;
};
