/**
 * Templates generated from the AQL Helper.
 */

import { DocItem, getDocs } from '../../components/editAqlField/aqlHelper/docLoader';
import { ExpressionTemplate, FunctionSignature, FunctionSignatureDict } from '../types';

const getFuncDoc = (doc: DocItem): string => {
  const description = doc.description ? `
**Description**

${doc.description}
` : '';

  const syntaxCode = doc?.syntaxes?.map(syn => {
    return `\`\`\`aql
${syn.type ? `// ${syn.type}\n` : ''}${syn.syntax}
\`\`\``;
  });
  const syntax = syntaxCode ? `
**Syntax**

${syntaxCode.join('\n&nbsp;\n')}
  ` : '';

  const returnType = doc.output ? `
**Returns**
\`\`\`md
${doc.output.name}${doc.output.description ? `: ${doc.output.description}` : ''}
\`\`\`
` : '';

  const examples = doc.examples ? doc.examples.map(eg => {
    return `\`\`\`aql
${eg.value}${eg.title ? ` // ${eg.title}` : ''}
\`\`\``;
  }) : '';
  const exampleSection = examples ? `
**Examples**

${examples.join('\n&nbsp;\n')}
` : '';

  return [
    description,
    syntax,
    returnType,
    exampleSection,
  ].filter(Boolean).join('\n&nbsp;\n');
};

export const getTemplates = async () => {
  const docs = await getDocs();

  const result: ExpressionTemplate[] = docs.functions.map(funcItem => {
    const funcName = funcItem.title.replaceAll('()', '');
    return {
      label: funcName,
      name: funcName,
      template: '',
      document: getFuncDoc(funcItem),
    } as ExpressionTemplate;
  });

  return result;
};

const getFuncParameters = (funcString: string) => {
  // Match the text between '(' and ')'.
  // E.g: "abc(x, y, z)" -> "x, y, z"
  const regex = /\(([^)]*)\)/;
  const result = regex.exec(funcString);

  if (result && result[1]) {
    return result[1]
      .split(',')
      .map(p => p.trim())
      .filter(Boolean);
  }

  return [];
};

const getFuncSignatures = (doc: DocItem) => {
  const inputDict: any = (doc.inputs ?? []).reduce((dict, inp) => {
    return {
      ...dict,
      [inp.name]: inp.description,
    };
  }, {});
  const result = (doc.syntaxes ?? []).map(syn => {
    const params = getFuncParameters(syn.syntax);
    return {
      label: syn.syntax,
      documentation: doc.description,
      parameters: params.map(param => {
        return {
          label: param,
          documentation: inputDict[param],
        };
      }),
      paramsCount: params.length,
    } as FunctionSignature;
  });

  return result;
};

export const getSignatures = async () => {
  const docs = await getDocs();

  const result: FunctionSignatureDict = {};

  docs.functions.forEach(func => {
    const funcName = func.title.replaceAll('()', '');
    result[funcName] = getFuncSignatures(func);
  });

  return result;
};
