import type { TreeNodeBase, TreeNodeKey } from '@holistics/design-system';

export type FilterFn<Node extends TreeNodeBase = TreeNodeBase> = (node: Node, text: string) => boolean;

const defaultFilterFn: FilterFn = (node, text) => node.label.toLowerCase().includes(text.toLowerCase());

/**
 * Search through tree nodes and return filtered nodes and expanded keys
 * @param nodes - The array of tree nodes to search through
 * @param text - The search text to filter by
 * @param filterFn - Optional custom filter function
 * @returns Object containing filtered nodes and expanded keys
 */
export function searchTreeNodes<Node extends TreeNodeBase = TreeNodeBase> (
  nodes: Node[],
  text: string,
  filterFn: FilterFn<Node> = defaultFilterFn,
): ({ nodes: Node[], expandedKeys: TreeNodeKey[] }) {
  // If search text is empty, return all nodes with no expanded keys
  if (text.trim() === '') return { nodes, expandedKeys: [] };

  const expandedKeys: TreeNodeKey[] = [];

  const searchNodes = (nodeList: Node[]): Node[] => {
    return nodeList.reduce<Node[]>((result, node) => {
      const isMatch = filterFn(node, text);
      const childNodes = (node.children || []) as Node[];
      const matchedChildren = searchNodes(childNodes);

      if (isMatch || matchedChildren.length) result.push({ ...node, children: matchedChildren });

      if (matchedChildren.length) expandedKeys.push(node.key);

      return result;
    }, []);
  };

  const filteredNodes = searchNodes(nodes);

  return {
    nodes: filteredNodes,
    expandedKeys,
  };
}
