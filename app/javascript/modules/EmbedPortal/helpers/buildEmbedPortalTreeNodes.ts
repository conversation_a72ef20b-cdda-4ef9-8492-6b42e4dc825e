import type {
  EmbedDashboard, EmbedDataset, EmbedObjectTreeNode, EmbedPortalConfig, NodePermission, PreviewNode,
} from '../types';

// Group keys
export const PREBUILT_DASHBOARD_NODE_KEY = 'prebuilt-dashboard';
export const ORG_WORKSPACE_NODE_KEY = 'org-workspace';
export const PERSONAL_WORKSPACE_NODE_KEY = 'personal-workspace';
export const DATASET_NODE_KEY = 'datasets';

// New dashboard node keys
export const NEW_ORG_DASHBOARD_NODE_KEY = 'new-org-dashboard';
export const NEW_PERSONAL_DASHBOARD_NODE_KEY = 'new-personal-dashboard';

export const generateNodeKey = ({ type, id }: PreviewNode): string => {
  return `${type}-${id}`;
};

export const buildRouteForNode = ({ type, id }: PreviewNode, token: string) => {
  switch (type) {
    case 'Dashboard':
      return {
        name: 'EmbedPortalDashboard',
        params: {
          dashboard_id: id,
        },
        query: {
          _token: token,
        },
      };
    case 'Dataset':
      return {
        name: 'EmbedPortalDataset',
        params: {
          dataset_id: id,
        },
        query: {
          _token: token,
        },
      };
    default:
      throw new Error(`Unknown type for ${type}`);
  }
};

export const buildDashboardNode = (
  embedDashboard: EmbedDashboard,
  token: string,
  { permission = {} } : { permission?: NodePermission } = {},
): EmbedObjectTreeNode => {
  return {
    label: embedDashboard.title,
    key: generateNodeKey(embedDashboard),
    icon: 'canvas',
    selectable: true,
    isLeaf: true,
    rawObject: embedDashboard,
    permission,
    to: buildRouteForNode({ type: 'Dashboard', id: embedDashboard.id }, token),
  };
};

const buildSectionNode = ({
  label, key, children, permission,
}: Pick<EmbedObjectTreeNode, 'label' | 'key' | 'children' | 'permission'>): EmbedObjectTreeNode => {
  return {
    label,
    key,
    children,
    permission,
    selectable: false,
    toggleAtAppend: true,
    props: {
      class: 'font-medium',
    },
  };
};

const buildDatasetNode = (embedDataset: EmbedDataset, token: string): EmbedObjectTreeNode => {
  return {
    label: embedDataset.title,
    key: generateNodeKey(embedDataset),
    icon: 'data-set',
    selectable: true,
    isLeaf: true,
    rawObject: embedDataset,
    permission: {},
    to: buildRouteForNode({ type: 'Dataset', id: embedDataset.id }, token),
  };
};

export const buildDashboardCreationNode = (key: string): EmbedObjectTreeNode => {
  return {
    label: '(Unsaved) dashboard',
    key,
    icon: 'canvas',
    selectable: true,
    isLeaf: true,
    permission: {},
  };
};

export const buildEmbedPortalTreeNodes = (
  embedPortal: EmbedPortalConfig,
  token: string,
): EmbedObjectTreeNode[] => {
  const allNodes: EmbedObjectTreeNode[] = [];
  const { actionBasedPermission } = embedPortal;

  if (embedPortal.embedObjects.dashboards.length > 0) {
    const prebuiltDashboardNode = buildSectionNode({
      label: 'Standard dashboards',
      key: PREBUILT_DASHBOARD_NODE_KEY,
      children: embedPortal.embedObjects.dashboards.map(dashboard => buildDashboardNode(dashboard, token)),
      permission: {},
    });
    allNodes.push(prebuiltDashboardNode);
  }

  if (actionBasedPermission.orgWorkspace.canView) {
    const hasEditPermissionInWorkspace = actionBasedPermission.orgWorkspace.canEdit;

    const orgNode = buildSectionNode({
      label: 'Shared workspace',
      key: ORG_WORKSPACE_NODE_KEY,
      permission: { canCreateChildren: hasEditPermissionInWorkspace },
      children: embedPortal.embedObjects.orgDashboards
        ?.map(dashboard => buildDashboardNode(dashboard, token, { permission: { canEdit: hasEditPermissionInWorkspace, canDelete: hasEditPermissionInWorkspace } })),
    });
    allNodes.push(orgNode);
  }

  if (actionBasedPermission.personalWorkspace.canView) {
    const hasEditPermissionInWorkspace = actionBasedPermission.personalWorkspace.canEdit;

    const personalNode = buildSectionNode({
      label: 'Personal workspace',
      key: PERSONAL_WORKSPACE_NODE_KEY,
      children: (embedPortal.embedObjects.personalDashboards)
        ?.map(dashboard => buildDashboardNode(dashboard, token, { permission: { canEdit: hasEditPermissionInWorkspace, canDelete: hasEditPermissionInWorkspace } })),
      permission: { canCreateChildren: hasEditPermissionInWorkspace },
    });
    allNodes.push(personalNode);
  }

  if (embedPortal.embedObjects.datasets.length > 0) {
    const datasetRootNode = buildSectionNode({
      label: 'Explore data',
      key: DATASET_NODE_KEY,
      children: embedPortal.embedObjects.datasets.map(dataset => buildDatasetNode(dataset, token)),
      permission: {},
    });
    allNodes.push(datasetRootNode);
  }

  return allNodes;
};
