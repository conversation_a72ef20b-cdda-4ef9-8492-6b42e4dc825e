import {
  buildDashboardNode,
  buildDashboardCreationNode,
  buildEmbedPortalTreeNodes,
  generateN<PERSON><PERSON><PERSON>,
  PREBUILT_DASHBOARD_NODE_KEY,
  ORG_WORKSPACE_NODE_KEY,
  PERSONAL_WORKSPACE_NODE_KEY,
  DATASET_NODE_KEY,
  NEW_ORG_DASHBOARD_NODE_KEY,
  NEW_PERSONAL_DASHBOARD_NODE_KEY,
} from '../buildEmbedPortalTreeNodes';
import type { EmbedDashboard, EmbedDataset, EmbedPortalConfig } from '../../types';

describe('buildEmbedPortalTreeNodes', () => {
  const mockToken = 'test-token-123';

  // Mock objects for testing
  const mockDashboard: EmbedDashboard = {
    id: 1,
    title: 'Test Dashboard',
    type: 'Dashboard',
    uname: 'test-dashboard',
  };

  const mockDataset: EmbedDataset = {
    id: 2,
    title: 'Test Dataset',
    type: 'Dataset',
    uname: 'test-dataset',
  };

  const mockEmbedPortal: EmbedPortalConfig = {
    id: 100,
    projectId: '1',
    uname: 'test-portal',
    embedObjects: {
      dashboards: [mockDashboard],
      datasets: [mockDataset],
      orgDashboards: [{ ...mockDashboard, id: 3, title: 'Org Dashboard' }],
      personalDashboards: [{ ...mockDashboard, id: 4, title: 'Personal Dashboard' }],
    },
    actionBasedPermission: {
      orgWorkspace: { canView: true, canEdit: true },
      personalWorkspace: { canView: true, canEdit: true },
    },
  };

  describe('generateNodeKey', () => {
    it('should generate a key in the format "type-id"', () => {
      const key = generateNodeKey(mockDashboard);
      expect(key).toBe('Dashboard-1');
    });
  });

  describe('buildDashboardNode', () => {
    it('should build a dashboard node with correct properties', () => {
      const node = buildDashboardNode(mockDashboard, mockToken);

      expect(node.label).toBe(mockDashboard.title);
      expect(node.key).toBe(generateNodeKey(mockDashboard));
      expect(node.icon).toBe('canvas');
      expect(node.selectable).toBe(true);
      expect(node.isLeaf).toBe(true);
      expect(node.rawObject).toBe(mockDashboard);
      expect(node.to).toMatchObject({
        name: 'EmbedPortalDashboard',
        params: { dashboard_id: mockDashboard.id },
        query: { _token: mockToken },
      });
    });

    it('should include permission information when provided', () => {
      const permission = { canEdit: true, canDelete: true };
      const node = buildDashboardNode(mockDashboard, mockToken, { permission });

      expect(node.permission).toEqual(permission);
    });
  });

  describe('buildDashboardCreationNode', () => {
    it('should build a personal dashboard creation node', () => {
      const node = buildDashboardCreationNode(NEW_PERSONAL_DASHBOARD_NODE_KEY);

      expect(node.label).toBe('(Unsaved) dashboard');
      expect(node.key).toBe(NEW_PERSONAL_DASHBOARD_NODE_KEY);
      expect(node.icon).toBe('canvas');
    });

    it('should build a team dashboard creation node', () => {
      const node = buildDashboardCreationNode(NEW_ORG_DASHBOARD_NODE_KEY);

      expect(node.key).toBe(NEW_ORG_DASHBOARD_NODE_KEY);
    });
  });

  describe('buildEmbedPortalTreeNodes', () => {
    it('should build all section nodes for a complete portal', () => {
      const nodes = buildEmbedPortalTreeNodes(mockEmbedPortal, mockToken);

      // Should have 4 sections: prebuilt, team, personal, datasets
      expect(nodes.length).toBe(4);

      // Check section node keys
      expect(nodes[0].key).toBe(PREBUILT_DASHBOARD_NODE_KEY);
      expect(nodes[1].key).toBe(ORG_WORKSPACE_NODE_KEY);
      expect(nodes[2].key).toBe(PERSONAL_WORKSPACE_NODE_KEY);
      expect(nodes[3].key).toBe(DATASET_NODE_KEY);

      // Check children counts
      expect(nodes[0].children?.length).toBe(1); // 1 prebuilt dashboard
      expect(nodes[1].children?.length).toBe(1); // 1 org dashboard
      expect(nodes[2].children?.length).toBe(1); // 1 personal dashboard
      expect(nodes[3].children?.length).toBe(1); // 1 dataset
    });

    it('should only include sections with content or permissions', () => {
      // Create a limited portal with no datasets and no personal workspace access
      const limitedPortal: EmbedPortalConfig = {
        ...mockEmbedPortal,
        embedObjects: {
          ...mockEmbedPortal.embedObjects,
          datasets: [], // No datasets
        },
        actionBasedPermission: {
          ...mockEmbedPortal.actionBasedPermission,
          personalWorkspace: { canView: false, canEdit: false }, // No personal workspace access
        },
      };

      const nodes = buildEmbedPortalTreeNodes(limitedPortal, mockToken);

      // Should only have 2 sections: prebuilt and team
      expect(nodes.length).toBe(2);
      expect(nodes[0].key).toBe(PREBUILT_DASHBOARD_NODE_KEY);
      expect(nodes[1].key).toBe(ORG_WORKSPACE_NODE_KEY);
    });

    it('should respect permission settings', () => {
      const portal: EmbedPortalConfig = {
        ...mockEmbedPortal,
        actionBasedPermission: {
          orgWorkspace: { canView: true, canEdit: false }, // Can view but not edit
          personalWorkspace: { canView: true, canEdit: true }, // Can view and edit
        },
      };

      const nodes = buildEmbedPortalTreeNodes(portal, mockToken);

      // Team workspace should not have create permission
      expect(nodes[1].permission?.canCreateChildren).toBe(false);

      // Personal workspace should have create permission
      expect(nodes[2].permission?.canCreateChildren).toBe(true);

      // Team dashboard children should not have edit/delete permissions
      expect(nodes[1].children?.[0].permission?.canEdit).toBe(false);
      expect(nodes[1].children?.[0].permission?.canDelete).toBe(false);

      // Personal dashboard children should have edit/delete permissions
      expect(nodes[2].children?.[0].permission?.canEdit).toBe(true);
      expect(nodes[2].children?.[0].permission?.canDelete).toBe(true);
    });
  });
});
