import type { TreeNodeBase } from '@holistics/design-system';
import { searchTreeNodes } from '../searchTreeNodes';

describe('searchTreeNodes', () => {
  // Test fixtures
  const sampleNodes: TreeNodeBase[] = [
    {
      key: '1',
      label: 'Root Node 1',
      children: [
        {
          key: '1-1',
          label: 'Child Node 1-1',
        },
        {
          key: '1-2',
          label: 'Child Node 1-2',
          children: [
            {
              key: '1-2-1',
              label: 'Grandchild Node 1-2-1',
            },
          ],
        },
      ],
    },
    {
      key: '2',
      label: 'Root Node 2',
      children: [
        {
          key: '2-1',
          label: 'Child Node 2-1',
        },
      ],
    },
    {
      key: '3',
      label: 'Root Node 3',
    },
  ];

  it('should return all nodes and empty expanded keys when search text is empty', () => {
    const result = searchTreeNodes(sampleNodes, '');
    expect(result.nodes).toEqual(sampleNodes);
    expect(result.expandedKeys).toEqual([]);
  });

  it('should filter nodes that match the search text', () => {
    const result = searchTreeNodes(sampleNodes, 'Root Node 1');
    expect(result.nodes.length).toBe(1);
    expect(result.nodes[0].key).toBe('1');
    expect(result.expandedKeys).toEqual([]);
  });

  it('should include parent nodes when children match the search text', () => {
    const result = searchTreeNodes(sampleNodes, 'Grandchild');
    expect(result.nodes.length).toBe(1);
    expect(result.nodes[0].key).toBe('1');
    expect(result.nodes[0].children?.length).toBe(1);
    expect((result.nodes[0].children as TreeNodeBase[])[0].key).toBe('1-2');
    expect(result.expandedKeys).toContain('1');
    expect(result.expandedKeys).toContain('1-2');
  });

  it('should handle case-insensitive search by default', () => {
    const result = searchTreeNodes(sampleNodes, 'root');
    expect(result.nodes.length).toBe(3);
  });

  it('should work with custom filter function', () => {
    const customFilterFn = (node: TreeNodeBase, text: string) => node.label.startsWith(text);
    const result = searchTreeNodes(sampleNodes, 'Child', customFilterFn);
    expect(result.nodes.length).toBe(2);
    expect(result.nodes.some(node => node.key === '1')).toBe(true);
    expect(result.nodes.some(node => node.key === '2')).toBe(true);
  });

  it('should handle nodes without children', () => {
    const result = searchTreeNodes(sampleNodes, 'Root Node 3');
    expect(result.nodes.length).toBe(1);
    expect(result.nodes[0].key).toBe('3');
    expect(result.expandedKeys).toEqual([]);
  });

  it('should return empty results when no matches found', () => {
    const result = searchTreeNodes(sampleNodes, 'Non-existent Node');
    expect(result.nodes).toEqual([]);
    expect(result.expandedKeys).toEqual([]);
  });
});
