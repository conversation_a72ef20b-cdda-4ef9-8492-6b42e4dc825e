import { computed, ComputedRef } from 'vue';
import { generateBlockId } from '@/modules/DashboardAsCode/utils/blocks';
import vizIcons from '@/modules/Viz/constants/vizIcons';
import { get, reduce } from 'lodash';
import { DashboardAsCode } from '@/modules/DashboardAsCode/types';

interface DataSchedule {
  source: Record<string, any>;
  source_data?: Record<string, any>;
}

interface SourceIconMap {
  [key: string]: string | null;
}

export function useWidgetSelectable (dataSchedule: ComputedRef<DataSchedule>) {
  const widgets: ComputedRef<Record<string, any>> = computed(() => {
    return get(dataSchedule.value, 'source.widgets', []);
  });

  const sourceIconMap: ComputedRef<SourceIconMap> = computed(() => {
    const { source } = dataSchedule.value;
    if (source.version === 4) {
      const { definition } = source as DashboardAsCode;
      if (!definition) return {};

      const vizTypes = definition.blocks.reduce<Record<string, string>>((acc, block) => {
        if (block.type !== 'VizBlock') {
          return acc;
        }
        acc[generateBlockId(source.id, block.uname)] = block?.viz?.viz_setting?.viz_type || '';
        return acc;
      }, {});

      return widgets.value.reduce((acc: SourceIconMap, widget: { id: string | number; }) => {
        acc[widget.id] = vizIcons?.[vizTypes?.[widget.id]] || null;
        return acc;
      }, {});
    }

    const reports = get(dataSchedule.value, 'source_data.reports', []);
    return reduce(reports, (iconMap: SourceIconMap, currReport) => {
      iconMap[currReport.id] = vizIcons[currReport.viz_setting?.viz_type || currReport.viz_type || ''] || null;
      return iconMap;
    }, {});
  });

  return {
    widgets,
    sourceIconMap,
  };
}
