<script setup lang="ts">
import {
  computed, ref, watch,
} from 'vue';
import { HSelect, type IconName, type SelectOption } from '@holistics/design-system';
import { type DatasetWithModels, unifyDataset, type VizFieldParamMetadata } from '@holistics/aml-std';
import { useDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';
import bTypeToHType from '@/modules/DataModels/utils/bTypeToHType';

interface VizFieldRef {
  model?: string
  field?: string
}

const modelValue = defineModel<VizFieldRef>();

const props = defineProps<{
  metadata: VizFieldParamMetadata
  dataset?: string
}>();

const { fetchDatasetService } = useDashboardConfigs();
const datasetObj = ref<DatasetWithModels>();

const placeholder = computed(() => {
  if (!datasetObj.value) {
    return 'No dataset specified';
  }
  if (props.metadata.metricsOnly) {
    return 'Select metric';
  }
  return 'Select field/metric';
});

const options = computed<SelectOption[]>(() => {
  if (!datasetObj.value) {
    return [];
  }
  const opts: SelectOption[] = [];

  if (datasetObj.value.metrics.length) {
    opts.push({
      label: 'Metrics',
      icon: 'metric-folder',
      value: 'metrics!!',
      initialExpanded: props.metadata.metricsOnly,
      children: datasetObj.value.metrics.map(metric => ({
        label: metric.name,
        value: `!!${metric.name}`,
        icon: 'type/aggregated',
      })),
    });
  }
  if (!props.metadata.metricsOnly) {
    datasetObj.value.data_models.forEach(model => {
      opts.push({
        label: model.label ?? model.name,
        value: `${model.name}!!`,
        icon: 'data-model',
        children: model.fields.map(field => ({
          label: field.label ?? field.name,
          value: `${model.name}!!${field.name}`,
          icon: `type/${bTypeToHType(field.type)}` as IconName,
        })),
      });
    });
  }

  return opts;
});

function onSelect (value: string) {
  const [model, field] = value.split('!!');
  modelValue.value = {
    model,
    field,
  };
}

watch(() => props.dataset, async (newVal, oldVal) => {
  if (newVal) {
    datasetObj.value = unifyDataset(await fetchDatasetService.fetch(newVal));
  }
  if (oldVal && newVal !== oldVal) {
    // reset selected value
    modelValue.value = undefined;
  }
}, {
  immediate: true,
});
</script>

<template>
  <HSelect
    :model-value="[modelValue?.model, modelValue?.field].join('!!')"
    :options="options"
    :disabled="!dataset"
    :placeholder="placeholder"
    filterable
    @update:model-value="val => onSelect(String(val))"
  />
</template>
