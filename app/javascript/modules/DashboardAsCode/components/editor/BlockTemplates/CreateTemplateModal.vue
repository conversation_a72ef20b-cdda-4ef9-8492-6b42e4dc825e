<script setup lang="ts">
import { computed, ref } from 'vue';
import {
  HModal, HButton, HAlert,
} from '@holistics/design-system';
import {
  type DashboardBlock, type ErrorBlock, type BlockPosition,
} from '@holistics/aml-std';
import { useToasts } from '@/core/composables/useToasts';
import { goToFile } from '@aml-studio/client/modules/LeftSidebar/models/nodeActions/utils';
import { isVizBlock, isIcBlock } from '../../../utils/blocks';
import { useDashboardConfigs } from '../../../composables/useDashboardConfigs';

const props = defineProps<{
  block: Exclude<DashboardBlock, ErrorBlock>
  position?: BlockPosition['position']
}>();
const shown = defineModel<boolean>('shown', { required: true });

const emit = defineEmits<{
  resolve: []
  dismiss: []
}>();

const { createTemplateFunc, extraDetails } = useDashboardConfigs();

const blockLabel = computed(() => {
  if (isVizBlock(props.block) || isIcBlock(props.block)) {
    return props.block.label || props.block.uname;
  }
  return undefined;
});
const modalTitle = computed(() => {
  if (blockLabel.value) {
    return `Add "${blockLabel.value}" to Block Library`;
  }
  return 'Add block to Block Library';
});

const title = ref('');
const description = ref('');
const thumbnail = ref('');

const { toast } = useToasts();
const isSaving = ref(false);
async function resolve () {
  if (!createTemplateFunc || isSaving.value) {
    return;
  }

  isSaving.value = true;

  try {
    const { filePath } = await createTemplateFunc(props.block.uname, {
      title: title.value,
      description: description.value,
      thumbnail: thumbnail.value,
      size: props.position,
    });

    toast.success('Block added to Block Library', {
      button: 'View code',
      onButtonClick: () => {
        if (extraDetails.projectId) {
          goToFile({
            filePath,
            projectId: extraDetails.projectId,
          });
        }
      },
    });
    emit('resolve');
  } catch (err: any) {
    toast.danger(err.message);
  } finally {
    isSaving.value = false;
  }
}

function dismiss () {
  emit('dismiss');
}
</script>

<template>
  <HModal
    v-model:shown="shown"
    data-ci="ci-create-template-modal"
    :title="modalTitle"
    prevent-click-outside
    prevent-close-animation-disabled
    @dismiss="dismiss"
    @resolve="resolve"
  >
    <HAlert
      v-if="!createTemplateFunc"
      title="Create template"
      type="danger"
    >
      This function is not available here...
    </HAlert>
    <div
      v-else
      class="flex-1 space-y-2"
    >
      <div>
        <div class="mb-1 font-medium">
          Title
        </div>
        <div>
          <input
            v-model="title"
            data-ci="ci-template-title"
            class="h-input"
            placeholder="Template title"
          >
        </div>
      </div>
      <div>
        <div class="mb-1 font-medium">
          Description
        </div>
        <div>
          <input
            v-model="description"
            data-ci="ci-template-description"
            class="h-input"
            placeholder="Template description"
            rows="5"
          >
        </div>
      </div>
      <div>
        <div class="mb-1 font-medium">
          Thumbnail URL
        </div>
        <div>
          <input
            v-model="thumbnail"
            data-ci="ci-template-thumbnail"
            class="h-input"
            placeholder="URL to Template thumbnail image"
            rows="5"
          >
        </div>
      </div>
    </div>

    <template #resolve-button>
      <HButton
        data-ci="ci-submit-create-template-modal"
        type="primary-highlight"
        class="ml-3"
        :disabled="isSaving"
        @click="resolve"
      >
        Confirm
      </HButton>
    </template>
  </HModal>
</template>
