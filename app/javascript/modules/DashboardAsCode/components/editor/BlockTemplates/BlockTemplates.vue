<script setup lang="ts">
import { computed, ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import {
  HButton, HTextHighlight, HSegmentedControl, HPopover, useModal,
  HTooltip,
} from '@holistics/design-system';
import { BUILTIN_MODULE_NAME, type BlockTemplate } from '@holistics/aml-std';
import { errorMessageFromAjax } from '@/core/services/ajax';
import SearchBox from '@/core/components/ui/SearchBox.vue';
import { buildAmlObjectUrl } from '@aml-studio/client/utils/amlUrlScheme';
import { useDashboardConfigs } from '../../../composables/useDashboardConfigs';
import ColoredLibIcon from './ColoredLibIcon.vue';
import TemplateModal from './TemplateModal.vue';
import { getTemplateThumbnail } from './utils';
import BlockTemplatesOnboarding from './BlockTemplatesOnboarding.vue';

const props = defineProps<{
  fetchTemplatesFunc:() => Promise<BlockTemplate[]>
  vertical?: boolean
}>();

const emit = defineEmits<{
  add: [params: { template: BlockTemplate, params: Record<string, any> }]
}>();

const popoverOpen = ref(false);
const initialized = ref(false);
const fetching = ref(false);
const templates = ref<BlockTemplate[]>([]);
const error = ref<string>();
async function fetchTemplates () {
  if (fetching.value) {
    return;
  }
  fetching.value = true;
  try {
    templates.value = await props.fetchTemplatesFunc();
  } catch (err) {
    error.value = errorMessageFromAjax(err as any);
  } finally {
    fetching.value = false;
  }
}

const searchText = ref('');
const type = ref<'all' | 'builtin' | 'custom'>('all');
const shownTemplates = computed(() => {
  let tmpl = templates.value;
  if (type.value === 'builtin') {
    tmpl = templates.value.filter((t) => t.name.startsWith(`${BUILTIN_MODULE_NAME}.blocks.`));
  } else if (type.value === 'custom') {
    tmpl = templates.value.filter((t) => !t.name.startsWith(`${BUILTIN_MODULE_NAME}.blocks.`));
  }
  if (!searchText.value) {
    return tmpl;
  }
  const unifiedSearchText = searchText.value.trim().toLowerCase();
  return tmpl.filter((template) => {
    return (template.metadata.title || template.name).toLowerCase().includes(unifiedSearchText)
     || (template.metadata.description || '').toLowerCase().includes(unifiedSearchText);
  });
});

const shownGroups = computed(() => {
  const groups = shownTemplates.value.reduce((acc, template) => {
    const groupName = template.metadata.metadata?.group ?? '';
    if (!acc[groupName]) {
      acc[groupName] = [];
    }
    acc[groupName].push(template);
    return acc;
  }, {} as Record<string, BlockTemplate[]>);

  return Object.entries(groups).map(([name, values]) => ({
    name,
    templates: values,
  })).sort((g1, g2) => {
    if (!g1.name) {
      // ensure 'Others' will be displayed last
      return 1;
    }
    return g1.name.localeCompare(g2.name);
  });
});

const { open: openModal } = useModal();
async function addTemplate (template: BlockTemplate) {
  if (!template.params?.length) {
    // if there's no params => add directly
    emit('add', {
      template,
      params: {},
    });

    popoverOpen.value = false;

    return;
  }

  const paramsValues = template.params.reduce<Record<string, any>>((acc, curr) => {
    acc[curr.name] = curr.defaultValue;
    return acc;
  }, {});

  // if all params are pre-filled (all params has default value) => add directly
  if (template.params.filter(p => p.defaultValue === undefined).length === 0) {
    emit('add', {
      template,
      params: paramsValues,
    });

    popoverOpen.value = false;

    return;
  }

  const { state, data } = await openModal(TemplateModal, {
    template,
    paramsValues,
  });
  if (state === 'dismiss') {
    return;
  }
  emit('add', {
    template,
    params: data[0],
  });
  popoverOpen.value = false;
}

function onPopoverShown () {
  if (!initialized.value) {
    // just init the fetch, no need to wait for it
    fetchTemplates();
    initialized.value = true;
  }
  nextTick(() => {
    const searchBoxInput = document.querySelector<HTMLInputElement>('.h-search-box input');
    searchBoxInput?.focus();
  });
}

const router = useRouter();
const { extraDetails } = useDashboardConfigs();
async function viewTemplateCode (template: BlockTemplate) {
  if (!extraDetails.projectId) {
    return;
  }

  popoverOpen.value = false;

  const newUrl = buildAmlObjectUrl({
    projectId: extraDetails.projectId,
    fqName: template.name,
  });

  await router.push(newUrl);
}
</script>

<template>
  <div class="relative">
    <HPopover
      v-model:open="popoverOpen"
      :arrow="false"
      :distance="12"
      floating-class="dac-block-templates-popover"
      :placement="vertical ? 'right' : 'top'"
      @post-open="onPopoverShown"
    >
      <template #default="{ open }">
        <HButton
          v-h-tooltip="{ content: 'Library', placement: vertical ? 'right' : 'bottom' }"
          data-ci="ci-block-library"
          type="tertiary-default"
          class="size-full"
          :active="open"
          :unified="vertical"
        >
          <div class="flex items-center gap-1">
            <ColoredLibIcon />
            <span v-if="!vertical">Library</span>
          </div>
        </HButton>
      </template>
      <template #content>
        <div class="mb-3 px-1 pt-1">
          <SearchBox
            v-model="searchText"
            placeholder="Search..."
          />
        </div>
        <div class="mb-3 flex px-1">
          <HSegmentedControl
            v-model="type"
            size="sm"
            :items="[{ value: 'all', label: 'All' }, { value: 'builtin', label: 'Built-in' }, { value: 'custom', label: 'Custom' }]"
          />
          <HButton
            v-h-tooltip="'Refresh library'"
            class="ml-auto"
            icon="refresh"
            :icon-spin="fetching"
            unified
            type="tertiary-default"
            size="sm"
            @click="fetchTemplates"
          />
        </div>
        <div
          class="mb-1 max-h-96 min-h-36 space-y-6 overflow-auto"
          style="scrollbar-gutter: stable both-edges;"
        >
          <div
            v-for="group in shownGroups"
            :key="group.name"
          >
            <div
              v-show="shownGroups.length > 1"
              class="mb-1 px-1 font-semibold"
            >
              {{ group.name || 'Others' }}
            </div>
            <div class="flex flex-wrap">
              <div
                v-for="template in group.templates"
                :key="template.name"
                :data-ci="`ci-template-${template.name}`"
                class="group relative mb-2 mr-2 w-32 min-w-0 cursor-pointer rounded p-1 hover:bg-gray-100"
                @click="addTemplate(template)"
              >
                <div
                  class="aspect-[4/3] rounded border bg-white bg-center bg-no-repeat"
                  :class="Boolean(template.metadata.thumbnail) ? 'bg-contain' : 'bg-cover'"
                  :style="{ backgroundImage: `url(${getTemplateThumbnail(template)})` }"
                />
                <div
                  class="mt-1 truncate text-gray-700"
                  :title="template.metadata.title || template.name"
                >
                  <HTextHighlight
                    :text="template.metadata.title || template.name"
                    :highlight="searchText"
                  />
                </div>
                <div
                  class="invisible absolute right-2 top-2 space-x-0.5 group-hover:visible"
                >
                  <HTooltip
                    v-if="template.metadata.description"
                    :content="template.metadata.description"
                  >
                    <HButton
                      size="sm"
                      type="clear-default"
                      unified
                      icon="info"
                    />
                  </HTooltip>
                  <HTooltip content="View Code">
                    <HButton
                      size="sm"
                      type="clear-default"
                      unified
                      icon="external-link"
                      @click.stop="viewTemplateCode(template)"
                    />
                  </HTooltip>
                </div>
              </div>
            </div>
          </div>

          <div
            v-show="!shownTemplates.length"
            class="text-gray-600"
          >
            No templates found.
          </div>
        </div>
      </template>
    </HPopover>
    <BlockTemplatesOnboarding
      class="absolute -right-1 -top-1"
    />
  </div>
</template>

<style lang="postcss">
.dac-block-templates-popover {
  .hui-popper-content {
    max-width: unset;
    width: 576px;
  }
}
</style>
