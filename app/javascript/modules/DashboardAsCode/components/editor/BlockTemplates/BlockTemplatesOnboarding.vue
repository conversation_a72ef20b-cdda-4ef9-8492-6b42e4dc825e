<template>
  <HPopoverOnboarding
    v-if="showNux"
    trigger="manual"
    :open="openNux"
    placement="right-start"
    right-button="Got it"
    @right-button-click="markSeen"
  >
    <Hotspot />
    <template #content>
      <div>
        <img
          src="https://cdn.holistics.io/assets/canvas-dashboard-block-library-20250409-745.svg"
          alt="Block Library"
          width="100%"
        >
      </div>
      <div class="text-title-xs">
        Block Library
      </div>
      <div class="mt-1 text-body-xs">
        Discover reusable dashboard blocks to create and customize visualizations with minimal effort.
        <a
          href="https://docs.holistics.io/docs/canvas-dashboard/guides/build-library-blocks"
          target="_blank"
        >Learn more</a>
      </div>
    </template>
  </HPopoverOnboarding>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { HPopoverOnboarding } from '@holistics/design-system';
import { markSeenNuxFeature, shouldShowNuxFeature } from '@/core/services/nux';
import Hotspot from '@/core/components/ui/Hotspot.vue';

const NUX_NAME = 'block-library';
const showNux = ref(shouldShowNuxFeature(NUX_NAME) && !window.H.testStates);
const openNux = ref(false);
const markSeen = () => {
  markSeenNuxFeature(NUX_NAME);
  showNux.value = false;
};

onMounted(() => {
  setTimeout(() => {
    openNux.value = true;
  }, 1000);
});
</script>
