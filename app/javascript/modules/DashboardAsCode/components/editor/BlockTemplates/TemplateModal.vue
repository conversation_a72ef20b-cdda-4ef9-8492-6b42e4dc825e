<script setup lang="ts">
import { ref } from 'vue';
import { HModal, HButton } from '@holistics/design-system';
import { type BlockTemplate } from '@holistics/aml-std';
import cloneDeep from 'lodash/cloneDeep';
import TemplateMetadata from './TemplateMetadata.vue';
import TemplateParamsForm from './TemplateParamsForm.vue';
import { isEmptyValue } from './utils';

const props = defineProps<{
  template: BlockTemplate
  paramsValues?: Record<string, any>
}>();
const shown = defineModel<boolean>('shown', { required: true });

const emit = defineEmits<{
  resolve: [params: Record<string, any>]
  dismiss: []
}>();

const templateParams = ref<Record<string, any>>(cloneDeep(props.paramsValues ?? {}));
const paramsFormRef = ref<InstanceType<typeof TemplateParamsForm>>();

function fillDefaultValuesIfNeeded () {
  Object.keys(templateParams.value).forEach((key) => {
    if (isEmptyValue(templateParams.value[key])) {
      const param = (props.template.params ?? []).find((p) => p.name === key);
      if (param?.defaultValue !== undefined) {
        templateParams.value[key] = param.defaultValue;
      } else if (param?.type === 'Boolean') {
        // Boolean param implicitly has `false` defaultValue
        templateParams.value[key] = false;
      }
    }
  });
}

function resolve () {
  if (paramsFormRef.value && !paramsFormRef.value.validate()) {
    return;
  }
  fillDefaultValuesIfNeeded();
  emit('resolve', templateParams.value);
}

function dismiss () {
  emit('dismiss');
}
</script>

<template>
  <HModal
    v-model:shown="shown"
    data-ci="ci-template-modal"
    :title="`${paramsValues ? 'Update' : 'Add'} ${template.metadata.title || template.name}`"
    prevent-click-outside
    prevent-close-animation-disabled
    @dismiss="dismiss"
    @resolve="resolve"
  >
    <TemplateMetadata
      :template="template"
      class="mb-4"
    />
    <div
      v-if="template.params?.length"
    >
      <TemplateParamsForm
        ref="paramsFormRef"
        v-model="templateParams"
        :params="template.params"
        :metadata="template.metadata.metadata"
      />
    </div>
    <div
      v-else
      class="text-gray-600"
    >
      This template has no parameters.
    </div>
    <template #resolve-button>
      <HButton
        data-ci="ci-submit-template-modal"
        type="primary-highlight"
        class="ml-3"
        @click="resolve"
      >
        Confirm
      </HButton>
    </template>
  </HModal>
</template>
