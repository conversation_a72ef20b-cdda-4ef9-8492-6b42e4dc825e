<script setup lang="ts">
import { TemplateFuncParamMetadata, type BlockTemplate, type BlockTemplateAdditionalMetadata } from '@holistics/aml-std';
import { computed, ref } from 'vue';
import { startCase } from 'lodash';
import HeredocInput from './HeredocInput.vue';
import StringInput from './StringInput.vue';
import DatasetInput from './DatasetInput.vue';
import NumberInput from './NumberInput.vue';
import VizFieldRefInput from './VizFieldRefInput.vue';
import BooleanInput from './BooleanInput.vue';
import { getParamMetadata, validateParam } from './utils';

const props = defineProps<{
  params: Required<BlockTemplate>['params']
  metadata: BlockTemplateAdditionalMetadata
}>();
const modelValue = defineModel<Record<string, any>>({ required: true });

function componentFor (type: string) {
  if (type.startsWith('Heredoc<')) {
    return HeredocInput;
  }
  if (type === 'Dataset') {
    return DatasetInput;
  }
  if (type === 'VizFieldRef') {
    return VizFieldRefInput;
  }
  if (type === 'Number' || type === 'Int') {
    return NumberInput;
  }
  if (type === 'Boolean') {
    return BooleanInput;
  }
  return StringInput;
}

const errors = ref<Record<string, string | undefined>>({});
const paramsMetadata = computed(() => {
  return props.params.reduce<Record<string, TemplateFuncParamMetadata>>((acc, curr) => {
    acc[curr.name] = getParamMetadata(props.metadata.params?.[curr.name], curr.type);
    return acc;
  }, {});
});

function validate () {
  let hasError = false;
  props.params.forEach(param => {
    const error = validateParam(modelValue.value[param.name], param, paramsMetadata.value[param.name]);

    if (error) {
      errors.value[param.name] = error;
      hasError = true;
    }
  });
  return !hasError;
}

function findBindingDataset (paramMeta: Record<string, any>, paramName: string): string | undefined {
  // try from metadata first
  if (paramMeta.dataset) {
    return paramMeta.dataset;
  }
  if (paramMeta.dataset_param) {
    return modelValue.value[paramMeta.dataset_param];
  }
  // if nothing found from metadata, try from params
  const datasetParam = props.params.find(p => p.type === 'Dataset' && p.name !== paramName);
  if (datasetParam) {
    return modelValue.value[datasetParam.name];
  }

  return undefined;
}

function bindingFor (param: Required<BlockTemplate>['params'][number]) {
  const paramMeta = paramsMetadata.value[param.name];

  if (param.type === 'VizFieldRef') {
    // find a dataset
    return {
      dataset: findBindingDataset(paramMeta, param.name),
    };
  }
  return {};
}

defineExpose({
  validate,
});
</script>

<template>
  <div class="space-y-2">
    <div
      v-for="param in props.params"
      :key="param.name"
      class="-m-1"
    >
      <div
        class="space-y-1 rounded p-1"
        :class="{ 'bg-red-50': !!errors[param.name]}"
      >
        <div class="flex">
          <span class="font-semibold">
            {{ startCase(param.name) }}
          </span>
          <span
            v-if="param.defaultValue === undefined"
            class="ml-1 text-red-600"
          >
            *
          </span>
          <span
            v-show="errors[param.name]"
            class="ml-auto text-red-600"
          >
            {{ errors[param.name] }}
          </span>
        </div>
        <div
          v-show="paramsMetadata[param.name].description"
          class="text-2xs text-gray-700"
        >
          {{ paramsMetadata[param.name].description }}
        </div>
        <div>
          <component
            :is="componentFor(param.type)"
            v-model="modelValue[param.name]"
            :data-ci="`ci-template-input-${param.name}`"
            :metadata="paramsMetadata[param.name] as any"
            v-bind="bindingFor(param)"
            @update:model-value="() => errors[param.name] = undefined"
          />
        </div>
      </div>
    </div>
  </div>
</template>
