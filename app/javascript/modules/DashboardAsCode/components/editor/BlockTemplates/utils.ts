import {
  type TemplateFuncParamMetadata, type BlockTemplate,
} from '@holistics/aml-std';

export function getTemplateThumbnail (template: BlockTemplate) {
  if (template.metadata.thumbnail) {
    return template.metadata.thumbnail;
  }
  switch (template.type) {
    case 'VizBlock':
      return 'https://cdn.holistics.io/assets/canvas-dashboard-viz-block-20250326-731.svg';
    case 'FilterBlock':
      return 'https://cdn.holistics.io/assets/canvas-dashboard-filter-block-20250326-734.svg';
    case 'PopBlock':
      return 'https://cdn.holistics.io/assets/canvas-dashboard-pop-block-20250326-733.svg';
    case 'DateDrillBlock':
      return 'https://cdn.holistics.io/assets/canvas-dashboard-datedrillblock-20250402-742.svg';
    case 'TextBlock':
      return 'https://cdn.holistics.io/assets/canvas-dashboard-text-block-20250402-741.svg';
    default:
      return 'https://docs.holistics.io/img/logo.png';
  }
}

export function getParamMetadata (metadata: TemplateFuncParamMetadata | undefined, paramType: string): TemplateFuncParamMetadata {
  if (!metadata) {
    return {
      type: 'GeneralParamMetadata',
    };
  }
  // include `"` => something like `param: "value 1" | "value 2"`
  if ((paramType === 'String' || paramType.includes('"')) && metadata.type === 'StringParamMetadata') {
    return metadata;
  }
  if (['Number', 'Int'].includes(paramType) && metadata.type === 'NumberParamMetadata') {
    return metadata;
  }
  // more viz field related types to come
  if (['VizFieldRef'].includes(paramType) && metadata.type === 'VizFieldParamMetadata') {
    return metadata;
  }
  // fall back to general metadata
  return {
    type: 'GeneralParamMetadata',
    description: metadata.description,
  };
}

export function isEmptyValue (value: any) {
  return value === undefined || value === '';
}

export function validateParam (value: any, param: { name: string, type: string, defaultValue?: any }, metadata: Record<string, any>) {
  // Required validation
  // for Boolean, it implicitly has `false` default value
  if (isEmptyValue(value) && param.defaultValue === undefined && param.type !== 'Boolean') {
    return 'Required';
  }

  if (param.type === 'Int' && !isEmptyValue(value) && !Number.isInteger(value)) {
    return 'Must be an Integer';
  }

  if (['Number', 'Int'].includes(param.type) && !isEmptyValue(value)) {
    if (typeof value !== 'number') {
      return 'Must be a Number';
    }
    const { min, max } = metadata;
    if (typeof min === 'number' && value < min) {
      return `Min value: ${min}`;
    }
    if (typeof max === 'number' && value > max) {
      return `Max value: ${max}`;
    }
  }

  return undefined;
}
