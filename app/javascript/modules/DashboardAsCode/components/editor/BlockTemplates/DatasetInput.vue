<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { type GeneralParamMetadata } from '@holistics/aml-std';
import { HSelect, type SelectOption } from '@holistics/design-system';
import type { DataSet } from '@holistics/types';
import { useDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';

defineProps<{
  metadata: GeneralParamMetadata
}>();

const modelValue = defineModel<string>();

const { fetchDatasetService } = useDashboardConfigs();

const datasets = ref<DataSet[]>([]);
const options = computed<SelectOption[]>(() => datasets.value.map(dataset => ({
  label: (dataset as any).label,
  value: dataset.id,
  icon: 'data-set',
})));

onMounted(async () => {
  datasets.value = await fetchDatasetService.fetchAllRaw({});
});
</script>

<template>
  <HSelect
    v-model="modelValue as any"
    :options="options"
    placeholder=""
    filterable
  />
</template>
