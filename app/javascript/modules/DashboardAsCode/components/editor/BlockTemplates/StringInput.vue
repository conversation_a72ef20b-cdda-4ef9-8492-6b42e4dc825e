<script setup lang="ts">
import { type StringParamMetadata } from '@holistics/aml-std';
import { HSelect } from '@holistics/design-system';
import ColorPicker from '@/core/components/ui/ColorPicker2.vue';

defineProps<{
  metadata: StringParamMetadata
  dataset?: string
}>();

const modelValue = defineModel<string>();
</script>

<template>
  <HSelect
    v-if="metadata.inputType === 'select'"
    v-model="modelValue"
    filterable
    :options="(metadata.options ?? []).map((value: string) => ({ label: value, value }))"
  />
  <ColorPicker
    v-else-if="metadata.inputType === 'color-picker'"
    v-model="modelValue"
  />
  <input
    v-else
    v-model="modelValue"
    class="h-input"
  >
</template>
