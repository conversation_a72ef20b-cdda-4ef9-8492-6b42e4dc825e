<script setup lang="ts">
import { type BlockTemplate } from '@holistics/aml-std';
import { getTemplateThumbnail } from './utils';

defineProps<{
  template: BlockTemplate
}>();

</script>

<template>
  <div class="flex space-x-4">
    <div class="rounded border">
      <div
        class="aspect-[4/3] w-32 rounded bg-white bg-center bg-no-repeat"
        :class="Boolean(template.metadata.thumbnail) ? 'bg-contain' : 'bg-cover'"
        :style="{ backgroundImage: `url(${getTemplateThumbnail(template)})` }"
      />
    </div>
    <div class="py-1">
      <div class="truncate font-semibold">
        {{ template.metadata.title || template.name }}
      </div>
      <div class="line-clamp-4">
        {{ template.metadata.description }}
      </div>
    </div>
  </div>
</template>
