import LRUCache from 'lru-cache';
import type VizInputData<PERSON>ache from '../input/DataCache';

export class VizFeCacheManager {
  private cache: LRUCache<string, VizInputDataCache>;

  constructor (capacity = 100, ttl = 1000 * 60 * 10) {
    this.cache = new LRUCache<string, VizInputDataCache>({
      max: capacity,
      ttl,
    });
  }

  get (key: string) {
    return this.cache.get(key);
  }

  set (key: string, cacheEntry: VizInputDataCache) {
    this.cache.set(key, cacheEntry);
  }

  rm (key: string) {
    this.cache.delete(key);
  }

  clear () {
    this.cache.clear();
  }
}
