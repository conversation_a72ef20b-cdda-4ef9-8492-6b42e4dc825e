# typed: true

module DashboardsV4::Materialized::View
  class TabLayout < T::Struct
    extend T::Sig

    const :dashboard, Dashboard
    const :tab_layout, DashboardsV4::Values::Definition::TabLayout

    sig { params(tab_unames: T.nilable(T::Array[String])).returns(T::Array[String]) }
    def uname_blocks_by_tabs(tab_unames = nil)
      tabs = get_tabs(tab_unames)

      tabs.map do |tab|
        tab.is_a?(DashboardsV4::Values::Definition::CanvasLayout) ? tab.blocks.keys : tab.blocks
      end.flatten.map(&:to_s)
    end

    sig { params(tab_unames: T.nilable(T::Array[String])).returns(T::Array[T.any(DashboardsV4::Values::Definition::CanvasLayout, DashboardsV4::Values::Definition::LinearLayout)]) }
    def get_tabs(tab_unames = nil)
      tab_unames.present? && tab_unames.any? ? tab_layout.tabs.select { |tab| tab_unames.include?(tab.uname) } : tab_layout.tabs
    end

    sig do
      params(tab_unames: T.nilable(T::Array[String]))
        .returns(T::Array[DashboardsV4::Materialized::Block])
    end
    def blocks(tab_unames)
      get_blocks(tab_unames)
    end

    sig do
      params(tab_unames: T.nilable(T::Array[String]))
        .returns(T::Array[DashboardsV4::Materialized::VizBlock])
    end
    def viz_blocks(tab_unames)
      T.cast(
        get_blocks(tab_unames, block_type: DashboardsV4::Values::Definition::Type::Viz),
        T::Array[DashboardsV4::Materialized::VizBlock],
      )
    end

    private

    sig do
      params(
        tab_unames: T.nilable(T::Array[String]),
        block_type: T.nilable(DashboardsV4::Values::Definition::Type),
      ).returns(T::Array[DashboardsV4::Materialized::Block])
    end
    def get_blocks(tab_unames, block_type: nil)
      uname_blocks = uname_blocks_by_tabs(tab_unames)
      definition_struct = dashboard.definition_struct
      raise 'Dashboard definition struct not found' unless definition_struct

      uname_blocks.map do |uname|
        block = definition_struct.find_block(T.must(uname))
        next nil unless block
        next nil if block_type && block.type != block_type

        DashboardsV4::Services::MaterializedBlockOperations.get_block(dashboard, block)
      end.compact
    end
  end
end

