/* eslint-disable @typescript-eslint/no-unused-vars */
import { expect, Page } from '@playwright/test';
import { test } from '../../fixtures/auto';
import { quickLogin } from '../../helpers';
import type { Tenant, User } from '../../types';

test.describe('Manage Users', () => {
  let tenant: Tenant;
  let admin: User;
  let growthAdmins: User[];
  let analysts: User[];
  let explorers: User[];
  let users: User[];
  let publicUsers: User[];

  test.beforeEach(async ({
    page, supports, records,
  }) => {
    await supports.invoke('ft_toggle_global', [
      'user:explorer_role',
      'users:pagination_api',
    ]);

    tenant = await supports.invoke('get_tenant');
    admin = await supports.invoke('get_user');

    analysts = await records.create<User>(Array.from({ length: 25 }, (_, i) => ({
      name: 'analyst',
      attrs: {
        email: `analyst${i + 1}@domain.com`,
        name: `Analyst ${i + 1}`,
        tenant_id: tenant.id,
      },
    })));

    explorers = await records.create<User>(Array.from({ length: 25 }, (_, i) => ({
      name: 'explorer',
      attrs: {
        email: `explorer${i + 1}@domain.com`,
        name: `Explorer ${i + 1}`,
        tenant_id: tenant.id,
      },
    })));

    users = await records.create<User>(Array.from({ length: 25 }, (_, i) => ({
      name: 'user',
      attrs: {
        email: `user${i + 1}@domain.com`,
        name: `User ${i + 1}`,
        tenant_id: tenant.id,
      },
    })));

    publicUsers = await records.create<User>(Array.from({ length: 25 }, (_, i) => ({
      name: 'public_user',
      attrs: {
        email: `public_user${i + 1}@domain.com`,
        name: `Public User ${i + 1}`,
        tenant_id: tenant.id,
      },
    })));

    growthAdmins = await records.create<User>(Array.from({ length: 10 }, (_, i) => ({
      name: 'user',
      attrs: {
        email: `growth_admin${i + 1}@domain.com`,
        name: `Growth Admin ${i + 1}`,
        role: 'growth_admin',
        tenant_id: tenant.id,
      },
    })));

    await quickLogin(page, admin.id, '/manage/users');
    await expect(page).toHaveTitle(/Manage Users/);
    await expect(page.locator('.ci-user-table')).toBeVisible();
  });

  async function expectCounters (page: Page, counters: { admin?: number, analyst?: number, explorer?: number, user?: number }) {
    for (const [key, counter] of Object.entries(counters)) {
      await expect(page.locator(`.ci-counter-by-role--${key}`)).toHaveText(counter.toString());
    }
  }

  test('with default filters', async ({ page }) => {
    const nextBtn = page.locator('.ci-next-btn');
    const prevBtn = page.locator('.ci-prev-btn');

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });

    // 1st page
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText(admin.email);
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // 2nd page
    await nextBtn.click();

    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // 3rd page
    await nextBtn.click();

    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // 2nd page (via prev)
    await prevBtn.click();

    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // 1st page (via prev)
    await prevBtn.click();

    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText(admin.email);
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  test('when search text is provided', async ({ page }) => {
    const input = page.locator('.ci-search-user');

    await input.pressSequentially('r3', { delay: 100 });

    await expectCounters(page, { explorer: 1, user: 1 });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(2);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // Case insensitive
    await input.clear();
    await input.pressSequentially('USER3', { delay: 100 });

    await expectCounters(page, { user: 1 });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(1);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  // TODO: implement `select_h_select_option`
  test.skip('when status filter is selected', async ({ page, records }) => {
    const pendingUsers = records.create<User>(Array.from({ length: 15 }, (_, i) => ({
      name: 'user',
      attrs: {
        email: `pending_user${i + 1}@domain.com`,
        name: `Pending User ${i + 1}`,
        invitation_status: 'Pending',
        tenant_id: tenant.id,
      },
    })));

    const deletedUsers = records.create<User>(Array.from({ length: 15 }, (_, i) => ({
      name: 'user',
      attrs: {
        email: `deleted_user${i + 1}@domain.com`,
        name: `Deleted User ${i + 1}`,
        deleted_at: new Date().toISOString(),
        tenant_id: tenant.id,
      },
    })));
  });

  // TODO: implement `select_h_select_option`
  test.skip('when role filter is selected', async ({ page }) => {
    // Admin
    await expectCounters(page, { admin: 1 });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(1);
    await expect(page.locator('.ci-user-email').first()).toHaveText(admin.email);
    await expect(page.locator('.ci-user-email').last()).toHaveText(admin.email);

    // Analyst
    await expectCounters(page, { analyst: analysts.length });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // Explorer
    await expectCounters(page, { explorer: explorers.length });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // User
    await expectCounters(page, { user: users.length });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  // TODO: implement `select_h_select_option`
  test.skip('when API access filter is selected', async ({ page }) => {
    // Has API Key
    await expectCounters(page, { admin: 1 });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(1);
    await expect(page.locator('.ci-user-email').first()).toHaveText(admin.email);
    await expect(page.locator('.ci-user-email').last()).toHaveText(admin.email);

    // No API Key
    await expectCounters(page, { analyst: analysts.length, explorer: explorers.length, user: users.length });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  test('with user initials order', async ({ page }) => {
    // Asc
    await page.locator('th.ci-col--initials').click();

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // Desc
    await page.locator('th.ci-col--initials').click();

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  test('with user id order', async ({ page }) => {
    // Asc
    await page.locator('th.ci-col--id').click();

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText(admin.email);
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');

    // Desc
    await page.locator('th.ci-col--id').click();

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });

  test('with user name order', async ({ page }) => {
    // Asc (default) - already tested

    // Desc
    await page.locator('th.ci-col--name').click();

    await expectCounters(page, {
      admin: 1,
      analyst: analysts.length,
      explorer: explorers.length,
      user: users.length,
    });
    await expect(page.locator('.ci-user-table tbody tr')).toHaveCount(25);
    await expect(page.locator('.ci-user-email').first()).toHaveText('<EMAIL>');
    await expect(page.locator('.ci-user-email').last()).toHaveText('<EMAIL>');
  });
});
