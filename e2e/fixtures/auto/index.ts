import { mergeTests } from '@playwright/test';
import { test as dbCleanerTest } from './databaseCleaner';
import { test as supportsTest } from './supports';
import { test as recordsTest } from './records';
import type { DataSource } from '../../types';
import { skipOnboardingFlow } from '../../helpers';

export const test = mergeTests(
  dbCleanerTest,
  supportsTest,
  recordsTest,
).extend({
  // A simple way to globally setup records within the context of `databaseCleaner`.
  records: async ({ supports, records }, use) => {
    const tenant = await supports.invoke('get_tenant');
    const admin = await supports.invoke('get_user');

    await records.create<DataSource>([
      {
        name: 'data_source',
        attrs: {
          name: 'pg',
          dbtype: 'postgresql',
          dbconfig: {
            dbname: 'htest',
            host: 'localhost',
            port: '5432',
            user: 'holistics',
          },
          tenant_id: tenant.id,
        },
      },
    ]);

    await skipOnboardingFlow(records, admin.id, tenant.id);

    await use(records);
  },
});
