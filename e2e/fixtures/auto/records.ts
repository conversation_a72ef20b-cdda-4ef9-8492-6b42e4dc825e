/* eslint-disable space-before-function-paren */
import { APIRequestContext } from '@playwright/test';
import { test as base } from './databaseCleaner';

export const test = base.extend<{ records: Records }>({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  records: async ({ request, databaseCleaner }, use) => {
    const records = new Records(request);
    await use(records);
  },
});

const BASE_URL = '/test/records';

export class Records {
  request: APIRequestContext;

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  async create<M extends Record<string, any>>(configs: RecordsCreateConfig<M>[]) {
    const res = await this.request.post(`${BASE_URL}/factory_create`, {
      data: { configs },
    });

    if (!res.ok()) {
      const body = (await res.body()).toString();
      throw new Error(body);
    }

    const { objects } = await res.json();

    return objects as M[];
  }
}

export type RecordsCreateConfig<M extends Record<string, any>> = { name: string, attrs: Partial<M> }
