/* eslint-disable no-unsafe-finally */
/* eslint-disable no-console */
import { test as base } from '@playwright/test';

interface DatabaseCleanerFixture {
  databaseCleaner: void;
}

export const test = base.extend<DatabaseCleanerFixture>({
  databaseCleaner: [
    async ({ request }, use) => {
      try {
        const startRes = await request.post('/test/database_cleaner/start');
        if (!startRes.ok()) {
          const body = (await startRes.body()).toString();
          throw new Error(body);
        }
      } catch (err) {
        console.error(err);
        throw err;
      }

      try {
        await use();
      } finally {
        try {
          const cleanRes = await request.post('/test/database_cleaner/clean');
          if (!cleanRes.ok()) {
            const body = (await cleanRes.body()).toString();
            throw new Error(body);
          }
        } catch (err) {
          console.error(err);
          throw err;
        }
      }
    },
    { auto: true },
  ],
});
