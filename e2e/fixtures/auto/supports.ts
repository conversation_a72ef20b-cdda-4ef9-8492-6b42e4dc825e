/* eslint-disable space-before-function-paren */
import { APIRequestContext } from '@playwright/test';
import { test as base } from './databaseCleaner';
import {
  FeatureToggle, Tenant, User, UserRole,
} from '../../types';

export const test = base.extend<{ supports: Supports }>({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  supports: async ({ request, databaseCleaner }, use) => {
    const supports = new Supports(request);
    await use(supports);
  },
});

const BASE_URL = '/test/supports';

export class Supports {
  request: APIRequestContext;

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  invoke: Invoke = async (method, ...args) => {
    const res = await this.request.post(`${BASE_URL}/invoke`, {
      data: { name: method, args },
    });

    if (!res.ok()) {
      const body = (await res.body()).toString();
      throw new Error(body);
    }

    const { data } = await res.json();

    return data;
  };
}

interface Invoke {
  (method: 'get_tenant', name?: string): Promise<Tenant>
  (method: 'get_user', tenant_id?: number, role?: UserRole): Promise<User>
  (method: 'ft_toggle_global', fts: (string | [string, boolean])[]): Promise<FeatureToggle>
}
