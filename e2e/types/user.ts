export interface User {
  id: number,
  uid?: number,
  foreign_id?: number,

  name: string,
  initials: string,
  email: string,

  role: UserR<PERSON>,

  allow_authentication_token: boolean,
  authentication_token?: string,
  session_token?: string,

  invitation_status?: string,

  dev_mode_enabled?: boolean,

  title?: string,
  job_title?: string,

  enable_export_data: boolean,
  settings: { time_zone?: string },
  nux?: Record<string, any>,

  tenant_id: number,
  deleted_at: string,
  created_at: string,
  updated_at: string,
}

export type UserRole = 'growth_admin' | 'admin' | 'analyst' | 'explorer' | 'user' | 'public'
