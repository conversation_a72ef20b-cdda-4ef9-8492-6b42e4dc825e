import { Page, Request } from '@playwright/test';

export const waitForNetworkIdle = async (
  page: Page,
  {
    idleTime = 1_100, // Just slightly higher than the default polling interval
    timeout = 10_000,
    checkInterval = 200,
  }: { idleTime?: number, timeout?: number, checkInterval?: number } = {},
): Promise<void> => {
  const pending = new Set<Request>();
  let lastRequestEvent = Date.now();

  const onRequest = (req: any) => {
    pending.add(req);
    lastRequestEvent = Date.now();
  };

  const onRequestDone = (req: any) => {
    pending.delete(req);
    lastRequestEvent = Date.now();
  };

  page.on('request', onRequest);
  page.on('requestfinished', onRequestDone);
  page.on('requestfailed', onRequestDone);

  return new Promise((resolve, reject) => {
    const start = Date.now();

    const check = () => {
      const now = Date.now();
      const timeSinceLastRequestEvent = now - lastRequestEvent;

      if (pending.size === 0 && timeSinceLastRequestEvent >= idleTime) {
        cleanup();
        resolve();
      } else if (now - start > timeout) {
        cleanup();
        reject(new Error(`waitForNetworkIdle timed out after ${timeout}ms`));
      }
    };

    const interval = setInterval(check, checkInterval);

    const cleanup = () => {
      clearInterval(interval);
      page.off('request', onRequest);
      page.off('requestfinished', onRequestDone);
      page.off('requestfailed', onRequestDone);
    };
  });
};
