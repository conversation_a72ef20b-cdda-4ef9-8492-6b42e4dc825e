/* eslint-disable camelcase */
import { Records } from '../fixtures/auto/records';
import { SurveyAnswer } from '../types';

export function skipOnboardingFlow (records: Records, userId: number, tenantId: number) {
  return records.create<SurveyAnswer>(
    ([
      ['onboarding:welcome_screen', { passed: true }],
      ['onboarding:has_data_model', { created: true, onboarding_data_source_id: 1 }],
      ['onboarding:first_data_set', { created: true, project_id: 1, data_set_path: 'wdwdwd.dataset.aml' }],
      ['onboarding:first_relationship', { skipped: true }],
      ['onboarding:aml_studio_tour', { is_done: true }],
      ['onboarding:reporting_tour', { is_done: true }],
      ['onboarding:tutorials', { seen: true }],
    ] as const).map(([question_key, data]) => ({
      name: 'survey_answer',
      attrs: {
        question_key,
        data,
        tenant_id: tenantId,
        user_id: userId,
      },
    })),
  );
}
