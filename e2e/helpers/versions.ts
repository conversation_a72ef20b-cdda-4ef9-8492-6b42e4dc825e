import { Supports } from '../fixtures/auto/supports';

export function setupVersion (supports: Supports, version: Version) {
  return supports.invoke('ft_toggle_global', FT_BY_VERSION[version]);
}

const FT_BY_VERSION: Record<Version, [string, boolean][]> = {
  '4.0': [
    ['data_models:manager', false],
    ['data_models:custom_field', true],
    ['data_sets:enabled', true],
    ['metric:hide', true],
    ['new_dropdown:clean', true],
    ['header:remove_data_center', true],
    ['data_manager:hide_import_and_transform', true],
    ['user:explorer_role', true],
    ['query_templates:hide', true],
    ['filter_templates:hide', true],
    ['data_connection:enabled', true],
    ['data_imports:v1_creation', false],
    ['data_transforms:v1_creation', false],
    ['dashboard_widgets:strict_version', true],
    ['dashboards_v1:creation', false],
    ['sql_report:creation', false],
    ['dataset_report:allow_standalone', false],
    ['aml_studio:enable', true],
    ['data_sets:creation_in_reporting', false],
    ['aml_studio:dashboard_v4', true],
    // Dynamic
    ['personal_workspace:enabled', true],
    ['dashboards_v3:creation', true],
    ['settings:bust_cache:hide', true],
    ['pivot:export_excel_with_format', true],
    ['relationships:auto_sync_from_fkeys', true],
    ['new_timezone_config', true],
    ['enable_tenant_week_start_day', true],
    ['slack_sender:upload_image_to_slack', true],
    ['viz:pivot_v2', true],
    ['data_models:sql_generation_gem_on_single_model', true],
    ['viz:table_v2', true],
    ['dm_adjacency_map:include_data_loads', true],
    ['annotations:enabled', false],
    ['data_models:aql', true],
    ['dashboards:dashboard_consumption', true],
    ['reporting:model_field_dependencies', true],
    ['smartlook:enable', true],
    ['interactive_control:date_drill', true],
    ['dashboards_v4:codegen', true],
    ['viz_setting:amql_filter_groups', true],
    ['viz_setting:amql_adhoc_fields', true],
    ['gleap', true],
    ['data_models:aql_as_default', true],
    ['regression:enabled', true],
    ['viz:action', true],
    ['reference:enabled', true],
    ['viz:cache_key_v2', true],
    ['percent_of_total:enabled', true],
    ['ag-grid:allow_save_column_width_size', true],
    ['moving_calculation:enabled', true],
    ['drill_features:view_underlying_data', true],
    ['drill_features:drill_down', true],
  ],
  '3.0': [
    ['data_models:manager', true],
    ['data_models:custom_field', true],
    ['data_sets:enabled', true],
    ['report:select_data_model', true],
    ['metric:hide', true],
    ['new_dropdown:clean', true],
    ['header:remove_data_center', true],
    ['data_manager:hide_import_and_transform', true],
    ['user:explorer_role', true],
    ['dashboards_v3:creation', true],
    ['query_templates:hide', true],
    ['filter_templates:hide', true],
    ['data_connection:enabled', true],
    ['data_imports:v1_creation', false],
    ['data_transforms:v1_creation', false],
    ['dashboard_widgets:strict_version', true],
    ['dashboards_v1:creation', false],
    ['sql_report:creation', false],
    ['dataset_report:allow_standalone', false],
    ['aml_studio:enable', false],
    ['data_sets:creation_in_reporting', true],
    ['aml_studio:dashboard_v4', false],
    // Dynamic
    ['personal_workspace:enabled', true],
    ['settings:bust_cache:hide', true],
    ['pivot:export_excel_with_format', true],
    ['relationships:auto_sync_from_fkeys', true],
    ['new_timezone_config', true],
    ['enable_tenant_week_start_day', true],
    ['slack_sender:upload_image_to_slack', true],
    ['viz:pivot_v2', true],
    ['data_models:sql_generation_gem_on_single_model', true],
    ['viz:table_v2', true],
    ['annotations:enabled', false],
    ['dashboards:dashboard_consumption', true],
    ['interactive_control:date_drill', true],
    ['dashboards_v4:codegen', false],
    ['gleap', true],
    ['regression:enabled', true],
    ['reference:enabled', true],
    ['drill_features:view_underlying_data', true],
    ['drill_features:drill_down', true],
  ],
  2.7: [
    ['data_models:manager', true],
    ['data_models:data_model_panel', true],
    ['data_models:custom_field', true],
    ['data_sets:enabled', true],
    ['report:select_data_model', true],
    ['user:explorer_role', true],
    ['dashboards_v3:creation', true],
    ['dashboard_widgets:strict_version', true],
    ['dashboards_v1:creation', true],
    ['sql_report:creation', true],
    ['dataset_report:allow_standalone', false],
    ['aml_studio:enable', false],
    ['new_timezone_config', false],
    ['data_sets:creation_in_reporting', true],
    ['aml_studio:dashboard_v4', false],
    // Dynamic
    ['personal_workspace:enabled', false],
    ['metric:hide', false],
    ['data_manager:hide_import_and_transform', false],
    ['query_templates:hide', false],
    ['filter_templates:hide', false],
    ['data_connection:enabled', false],
    ['relationships:auto_sync_from_fkeys', false],
    ['data_imports:v1_creation', false],
    ['data_transforms:v1_creation', false],
    ['import_v3:conversion', false],
    ['annotations:enabled', true],
    ['interactive_control:date_drill', true],
    ['gleap', true],
  ],
  2.5: [
    ['data_models:manager', true],
    ['data_models:data_model_panel', true],
    ['data_models:custom_field', true],
    ['data_sets:enabled', true],
    ['report:select_data_model', true],
    ['dashboards_v3:creation', false],
    ['query_templates:hide', false],
    ['filter_templates:hide', false],
    ['data_imports:v1_creation', true],
    ['data_transforms:v1_creation', true],
    ['dashboard_widgets:strict_version', false],
    ['dashboards_v1:creation', true],
    ['sql_report:creation', true],
    ['dataset_report:allow_standalone', true],
    ['aml_studio:enable', false],
    ['new_timezone_config', false],
    ['data_sets:creation_in_reporting', true],
    ['aml_studio:dashboard_v4', false],
    // Dynamic
    ['personal_workspace:enabled', false],
    ['metric:hide', false],
    ['data_manager:hide_import_and_transform', false],
    ['user:explorer_role', true],
    ['data_connection:enabled', false],
    ['relationships:auto_sync_from_fkeys', false],
    ['annotations:enabled', true],
    ['interactive_control:date_drill', true],
  ],
  '2.0': [
    ['data_models:manager', false],
    ['data_sets:enabled', false],
    ['metric:hide', false],
    ['new_dropdown:clean', false],
    ['header:remove_data_center', false],
    ['data_manager:hide_import_and_transform', false],
    ['user:explorer_role', false],
    ['dashboards_v3:creation', false],
    ['query_templates:hide', false],
    ['filter_templates:hide', false],
    ['data_connection:enabled', false],
    ['data_imports:v1_creation', true],
    ['data_transforms:v1_creation', true],
    ['dashboard_widgets:strict_version', true],
    ['dashboards_v1:creation', true],
    ['sql_report:creation', true],
    ['aml_studio:enable', false],
    ['new_timezone_config', false],
    ['aml_studio:dashboard_v4', false],
    // Dynamic
    ['personal_workspace:enabled', false],
    ['relationships:auto_sync_from_fkeys', false],
    ['annotations:enabled', true],
  ],
};

export type Version = '4.0' | '3.0' | '2.7' | '2.5' | '2.0'
