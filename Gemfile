source 'https://rubygems.org'

gem 'rails', '~> 6.1.0'

# Components
gem 'redis'
gem 'sidekiq', '< 7' # lock sidekiq version

gem 'puma', require: false


source 'https://44ef5602:<EMAIL>/' do
  gem 'sidekiq-pro', '< 6'
end

# Database connectors
gem 'aws-sdk-athena', '1.79.0'
# Google BigQuery
gem 'google-cloud-bigquery'

gem 'mongo', '~> 2.20.0'
gem 'mysql2', git: 'https://github.com/holistics/mysql2.git'
gem 'pg', '1.4.6'
gem 'presto-client', git: 'https://github.com/holistics/presto-client-ruby.git' # Our fork with faraday upgraded
gem 'ruby-oci8', '2.2.12', require: 'oci8' # oracle integration
gem 'ruby-odbc' # clickhouse
gem 'tiny_tds', '~> 2.1.5' # sql server

# Rails
gem 'actionmailer'
gem 'net-pop', require: false
gem 'net-imap', require: false
gem 'net-smtp', require: false
# active_model_serializers version >=0.10 would have breaking changes need decent adaptations
# we should switch to alternative ones as they no longer develop this project
# https://github.com/rails-api/active_model_serializers#status
gem 'active_model_serializers', '~> 0.9.13'
gem 'alba', '3.4.0'
gem 'activerecord-import'
gem 'active_record_query_trace', '1.8.2'

gem 'tzinfo-data', '1.2022.7'
gem 'dotenv-rails'
gem 'hashid-rails', '~> 1.4'
gem 'premailer-rails'
# Lock sprockets version https://github.com/rails/sprockets-rails/issues/444#issuecomment-637817050
gem 'sprockets', '<4'

unless ENV['TAPIOCA']
  gem 'public_activity', '~> 2.0.2'
end

# Integrations
gem 'net-sftp', '~> 4.0'
gem 'net-ssh'

# Security
gem 'devise', '~> 4.9.4'
gem 'jwt'
gem 'lockbox'
gem 'omniauth-google-oauth2', '~> 1.2.1'
gem 'omniauth-rails_csrf_protection', '~> 1.0.1'
gem 'omniauth-saml', '~> 2.2.3'
gem 'rack-attack', '~> 6.4.0'
gem 'recaptcha', require: 'recaptcha/rails'
gem 'devise-two-factor', '~> 4.1.0'
# Permission
gem 'cancancan', '~> 3.4.0'

# Assets
gem 'sass-rails'

# Active admin
gem 'activeadmin', '~> 3.2.2'
gem 'activeadmin_json_editor', '~> 0.0.10'
gem 'jquery-ui-rails', git: 'https://github.com/jquery-ui-rails/jquery-ui-rails.git', tag: 'v7.0.0'

# Excel
gem 'caxlsx'
# Use our forked version of creek to fix the security issue while not changing the app behavior
# https://github.com/holistics/holistics/pull/10087
# This fork will exclude the Date parser changed in the original creek gem
gem 'creek', git: 'https://github.com/holistics/creek.git', branch: 'gems/bump-http-5.2.0'
gem 'fast_excel'
gem 'spreadsheet'

# Performance
gem 'fast_blank' # String#blank?
gem 'lz4-ruby'
gem 'msgpack_rails'
gem 'oj'
# gem 'zip-zip' # Fix rubyzip dependency issue

# Utils
gem 'browser'
gem 'chronic'
gem 'colorize'
gem 'hashdiff'
gem 'mime-types', '~> 3.4' # MIME::Types
gem 'sorbet-runtime'

# Logs
gem 'lograge'
gem 'logstash-event'
gem 'logstash-logger'

# Others
gem 'http', '~> 5.2.0'
gem 'retriable'

# https://docs.aws.amazon.com/sdk-for-ruby/v3/api/
# gem 'aws-sdk', '~> 3'
gem 'aws-sdk-s3', '~> 1.156'

gem 'google-apis-sheets_v4'
gem 'google-apis-people_v1'
gem 'google-apis-oauth2_v2'
gem 'google-apis-drive_v3'
gem 'google-apis-analytics_v3'
gem 'google-apis-analyticsreporting_v4'
# These is a potential performance issue with gem representable version 3.2.0
# https://www.notion.so/holistics/Slow-operations-after-upgrade-PG-and-Google-API-gems-14d1ada41f754ac79734b32e27f14d7b?pvs=4#29454176c29a415dbda75d77d67cdf63
gem 'representable', '3.0.4'

# CLI
gem 'thor', require: false
gem 'whenever', require: false
gem 'cli-ui', require: false
gem 'tabulo', require: false
gem 'tty-prompt', require: false

# https://github.com/mperham/sidekiq/wiki/Pro-Metrics
# https://github.com/mperham/sidekiq/blob/master/Pro-Changes.md#361
gem 'dogstatsd-ruby'

# For partition script
gem 'pgslice'

# For adding tracing comment to queries
gem 'marginalia'

# For N+1 detection
gem 'prosopite', git: 'https://github.com/holistics/prosopite', branch: 'main', require: false
gem 'pg_query', require: false

# For Sidekiq manager
gem 'xmlrpc'

gem 'ruby-openai', '7.3.1'
gem 'ffi', '< 1.17.0'

group :development, :test do
  gem 'sorbet'

  gem 'binding_of_caller', require: false
  gem 'pry-byebug'
  gem 'pry-doc', require: false

  gem 'danger', '9.5.1'
  gem 'fixture_builder'

  gem 'awesome_print'
  gem 'licensed', require: false
  gem 'rubocop', require: false
  gem 'rubocop-rspec', require: false
  gem 'whenever-test', require: false
end

group :development do
  gem 'better_errors'
  gem 'meta_request', '~> 0.8.2' # rails panel
  gem 'parallel', require: false # stress test script

  gem 'bootsnap', require: false
  gem 'brakeman', '~> 5.4.1', require: false
  gem 'nokogiri', require: false
  gem 'tapioca', '0.16.10', require: false
end

group :test do
  gem 'capybara'
  gem 'selenium-webdriver'
  gem 'webdrivers'

  gem 'rails-controller-testing' # for `assigns`
  gem 'rspec_junit_formatter', require: false
  gem 'rspec-mocks'
  gem 'rspec-rails', '~> 4.0'
  gem 'rspec-retry'

  # https://github.com/DatabaseCleaner/database_cleaner/blob/master/History.rdoc
  gem 'database_cleaner', '2.0.2'
  gem 'factory_bot_rails'
  gem 'faker'

  gem 'rubyXL', '3.4.27'
  gem 'simplecov', require: false
  gem 'shoulda-matchers'
  gem 'timecop'
  gem 'vcr'
  gem 'webmock'
  gem 'deprecation_toolkit'

  # Visual Regression Testing
  gem 'chunky_png'
  gem 'rmagick'
end

group :production do
  gem 'terser'
  gem 'puma_worker_killer', require: false
end

gem 'rack', '~> 2.2'

group :development, :production, :test do
  gem 'debug', require: false
end

gem 'childprocess', '~> 3.0'

gem 'sorbet-coerce'

gem 'sorbet-static-and-runtime'

Dir.glob(File.expand_path('gems/*', __dir__)).each do |path|
  gem(File.basename(path), path: path) if File.directory?(path)
end
gem 'batch_api', '~> 0.3.0'

gem 'aml_studio', path: 'engines/aml_studio'

gem 'committee', '~> 5.4'

gem 'combine_pdf', '~> 1.0'
gem 'matrix', '~> 0.4' # gem combine_pdf require matrix but not declare it as runtime dependency

gem 'rgl', '~> 0.5.7'

gem 'strong_migrations', '~> 1.0.0'

source 'https://rubygems.pkg.github.com/holistics' do
  gem 'aml', '~> 4.0'
  gem 'coding_utils', '~> 1.0', '>=1.0.3'
  gem 'data_connector', '~> 3.2'
  gem 'holistics_aml', '5.1.0'
  gem 'holistics_config', '~> 0.3'
  gem 'postgres_cache', '~> 3.0.1'
  gem 'sql_generation', '~> 5.8'
end

gem 'stackprof', require: false

gem 'anbt-sql-formatter', require: false

gem 'opentelemetry-sdk'
gem 'opentelemetry-semantic_conventions'
gem 'opentelemetry-exporter-otlp'
gem 'opentelemetry-instrumentation-base', require: false

gem 'opentelemetry-instrumentation-action_view'
gem 'opentelemetry-instrumentation-active_record'
gem 'opentelemetry-instrumentation-active_support'
gem 'opentelemetry-instrumentation-aws_sdk'
gem 'opentelemetry-instrumentation-concurrent_ruby'
gem 'opentelemetry-instrumentation-faraday'
gem 'opentelemetry-instrumentation-http'
gem 'opentelemetry-instrumentation-net_http'
gem 'opentelemetry-instrumentation-rails'
gem 'opentelemetry-instrumentation-redis'
gem 'opentelemetry-instrumentation-sidekiq'
gem 'opentelemetry-instrumentation-http_client'
gem 'opentelemetry-instrumentation-pg'

gem 'ed25519', '~> 1.2', '>= 1.2.4'
gem 'bcrypt_pbkdf', '~> 1.1'

gem 'vite_rails', '~> 3.0'
gem 'crawler_detect'

gem 'red-arrow', '~> 17.0', require: false
gem 'red-arrow-flight', '~> 17.0', require: false

# NOTE: these gems are no longer bundled in Ruby 3 standard libraries
gem 'net-http'
gem 'sorted_set'
gem 'uri', '0.12.4'

gem "octokit", "~> 4.21"
