# typed: true

module AmlStudio
  module PrWorkflow
    class UpdateToken < T::Struct
      extend T::Sig
      include Queueable
      include Service
      include ServiceConcerns::Asyncable

      const :token, String
      const :project, AmlStudio::Project

      sig { returns(T.any(Response::JobResult::Success, Response::JobResult::Error)) }
      def call
        unless project.external_integrated?
          return Response::JobResult::Error.new(error_message: 'Project is not integrated with external')
        end

        # allow users to update the token while PR workflow is disabled (but the token must be present)
        unless T.must(project.external_git_integration).token.present?
          return Response::JobResult::Error.new(error_message: 'Token is not set to update')
        end

        result = EnablePrWorkflow.new(token: token, project: project).handle_workflow
        return result if result.is_a?(Response::JobResult::Error)

        project.create_activity_with_action(
          action: 'update_token_pr_workflow',
        )

        Response::JobResult::Success.new(message: 'Token validated', data: result.data)
      end
    end
  end
end
