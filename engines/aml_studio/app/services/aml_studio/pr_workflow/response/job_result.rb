# typed: true

module AmlStudio
  module PrWorkflow
    module Response
      class JobResult
        class Error < T::Struct
          const :status, String, default: 'error'
          const :error_message, String, default: 'Something went wrong, please try again'
          const :error, T.untyped, default: nil
        end

        class Success < T::Struct
          const :status, String, default: 'success'
          const :message, String, default: 'Success'
          const :data, T.untyped, default: nil
        end
      end
    end
  end
end
