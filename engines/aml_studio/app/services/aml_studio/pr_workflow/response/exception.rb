# typed: true

module AmlStudio
  module PrWorkflow
    module Response
      class Exception < Holistics::InvalidOperation
        extend T::Sig

        class ErrorType < T::Enum
          enums do
            ConcurrentUpdateOnPrWorkflowError = new('concurrent_update_on_pr_workflow')
          end
        end

        sig { returns(ErrorType) }
        attr_reader :error_type

        sig { params(error_type: ErrorType, details: T.untyped).void }
        def initialize(error_type:, details:)
          super
          @error_type = error_type
          @details = details
        end
      end

      class ConcurrentUpdateError < Exception
        def initialize
          super(
            error_type: ErrorType::ConcurrentUpdateOnPrWorkflowError,
            details: 'There is another user try to update the project. Please try again later',
          )
        end
      end
    end
  end
end
