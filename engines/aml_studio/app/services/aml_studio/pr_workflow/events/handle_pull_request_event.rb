# typed: true

module AmlStudio
  module PrWorkflow
    module Events
      class HandlePullRequestEvent < T::Struct
        extend T::Sig

        # List of valid PR actions we want to handle
        WHITE_LIST_PR_ACTIONS = [
          'opened', # open a PR
          'closed', # change from open to close
          'reopened', # change from close to open
          'converted_to_draft', # change from draft to open
          'ready_for_review', # change from draft to open
          'edited', # in case it changed the title or base branch
        ].freeze

        const :parsed_data, T::Hash[Symbol, T.untyped]
        const :project, ::AmlStudio::Project

        sig { void }
        def call
          pull_request = T.cast(parsed_data[:pr_data], AmlStudio::Values::PullRequest)
          should_create_deploy_event = parsed_data[:merged_pr_event]
          action_type = parsed_data[:action_type]
          should_create_pull_request_event = parsed_data[:open_pr_related_event]

          unless WHITE_LIST_PR_ACTIONS.include?(action_type) && project.production_branch == pull_request.base_branch
            return
          end

          # only record events for open pull request
          if should_create_pull_request_event
            # TODO: need to check the updated_at on frontend to make sure the event is not stale
            project.create_event(
              event_type: AmlStudio::ProjectEvent::EventType::PullRequest,
              branch_name: pull_request.head_branch,
              created_at: pull_request.updated_at, # should record created_at as PR updated_at
              data: {
                trigger: {
                  type: 'pull_request',
                  pull_request: pull_request.as_json,
                },
              },
            )
          end

          # only deploy when the pull request is merged
          # check the event_type to make sure it is a state change event (not edited)
          if should_create_deploy_event
            commit_to_deploy = T.must(pull_request.merge_commit_sha)
            job = submit_deploy_job(
              pull_request.head_branch,
              commit_to_deploy,
            )

            # record the job id here for tracking the deploy status
            project.create_event(
              event_type: AmlStudio::ProjectEvent::EventType::Deploy,
              branch_name: pull_request.head_branch,
              data: {
                trigger: {
                  type: 'pull_request',
                  job_id: job.id,
                  pull_request: pull_request.as_json,
                },
              },
            )
          end
        end

        private

        sig { params(branch_name: String, commit_oid: String).returns(T.untyped) }
        def submit_deploy_job(branch_name, commit_oid)
          # TODO: should find corresponding user to GitHub user to deploy (not admin)
          tenant = T.must(project.tenant)
          admin = T.cast(tenant.admin_users.first, User)
          project.async(
            tag: 'cicd',
            user_id: admin.id,
            tenant_id: project.tenant_id,
            self_cache: false,
          ).auto_deploy(admin, commit_oid)
        end
      end
    end
  end
end
