# typed: true

module AmlStudio
  module PrWorkflow
    class EnablePrWorkflow < T::Struct
      include Queueable
      include Service
      include ServiceConcerns::Asyncable

      const :token, T.nilable(String), default: nil
      const :project, AmlStudio::Project

      sig { returns(T.any(Response::JobResult::Success, Response::JobResult::Error)) }
      def call
        unless project.external_integrated?
          return Response::JobResult::Error.new(error_message: 'Project is not integrated with external')
        end

        if project.enabled_pr_workflow?
          return Response::JobResult::Error.new(error_message: 'PR workflow has already enabled on this project')
        end

        res = handle_workflow

        if res.is_a?(Response::JobResult::Success)
          project.create_activity_with_action(
            action: 'enable_pr_workflow',
          )
        end

        res
      end

      sig { returns(T.any(Response::JobResult::Success, Response::JobResult::Error)) }
      def handle_workflow
        created_webhook_id = T.let(nil, T.nilable(Integer))
        git_client = T.let(nil, T.untyped)
        old_webhook_id = T.let(nil, T.nilable(Integer))

        begin
          ActiveRecord::Base.transaction do
            unless Db::Lock.try_advisory_xact_lock_with_id(:pr_workflow_on_project, T.must(project.id))
              raise AmlStudio::PrWorkflow::Response::ConcurrentUpdateError
            end

            # step 1: update token + enable pr workflow into database
            external_git_integration = find_or_create_external_git_integration!

            project.lock!('FOR UPDATE')
            external_git_integration.lock!('FOR UPDATE')

            if token.nil? && external_git_integration.token.nil?
              raise Holistics::InvalidOperation, 'Please provide the token to continue'
            end

            # update the token if it's provided
            encrypted_token = token.present? ? SourceControl::Backend.passphrase_encryptor.encrypt(T.must(token)) : external_git_integration.token
            external_git_integration.token = encrypted_token
            project.settings = project.settings.merge(enabled_pr_workflow: true)

            # step 2: check required permission (read PR + read/write webhook)
            git_client = AmlStudio::GitFlows::GitClient::ClientBuilder.new(
              git_provider: T.must(project.git_provider),
              token: SourceControl::Backend.passphrase_encryptor.decrypt(T.must(external_git_integration.token)),
            ).call

            git_client.validate_read_pr_permission!

            external_git_integration.token_expiration_date = git_client.token_expiration_date.in_time_zone

            # step 3: store old webhook id for later deletion
            old_webhook_id = external_git_integration.webhook_id

            # step 4: always create a new webhook
            webhook_secret = StringUtils.random_string(16)
            encrypted_webhook_secret = SourceControl::Backend.passphrase_encryptor.encrypt(webhook_secret)
            external_git_integration.webhook_secret = encrypted_webhook_secret

            created_webhook_id = git_client.create_webhook(
              project.webhook_callback_url,
              webhook_secret,
            )

            external_git_integration.webhook_id = created_webhook_id

            external_git_integration.save!
            project.save!
          end

          # step 5: delete old webhook after transaction success if it exists
          if old_webhook_id.present? && git_client.present?
            begin
              git_client.delete_webhook(T.must(old_webhook_id))
            rescue StandardError
              # Silently ignore any errors when deleting webhook
              return Response::JobResult::Success.new(
                message: 'Enable PR workflow successfully',
                data: {
                  token_expiration_date: T.must(project.external_git_integration).token_expiration_date,
                  warning_message: "Failed to delete old webhook with id #{old_webhook_id}",
                },
              )
            end
          end

          Response::JobResult::Success.new(
            message: 'Enable PR workflow successfully',
            data: {
              token_expiration_date: T.must(project.external_git_integration).token_expiration_date,
            },
          )
        rescue Holistics::InvalidOperation => e
          # Handle business validation errors
          Response::JobResult::Error.new(error_message: e.message)
        rescue StandardError => e
          # Clean up webhook if it was created but transaction failed
          if created_webhook_id.present? && git_client.present?
            git_client.delete_webhook(created_webhook_id)
          end

          # Re-raise the original error
          raise e
        end
      end

      private

      sig { returns(AmlStudio::ExternalGitIntegration) }
      def find_or_create_external_git_integration!
        if project.external_git_integration.present?
          return T.must(project.external_git_integration)
        end

        integration = AmlStudio::ExternalGitIntegration.find_or_create_by!(
          provider: T.must(project.git_provider).provider,
          repo_url: T.must(project.git_provider).normalize_git_url_to_https,
        )
        project.update!(external_git_integration: integration)
        integration
      end
    end
  end
end
