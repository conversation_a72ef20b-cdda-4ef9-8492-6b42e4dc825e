# typed: true

module AmlStudio
  module PrWorkflow
    class DisablePrWorkflow < T::Struct
      const :project, AmlStudio::Project
      const :is_delete_token, T::<PERSON><PERSON>an, default: false

      def call
        raise Holistics::InvalidOperation, 'Project is not integrated with external' unless project.external_integrated?

        unless FeatureToggle.active?(
          Project::FT_PR_WORKFLOW, project.tenant_id,
        )
          raise Holistics::InvalidOperation,
                'PR Workflow feature is not enabled on this tenant'
        end

        unless project.external_git_integration_id.present?
          raise Holistics::InvalidOperation,
                'PR workflow is not enabled on this tenant yet'
        end

        external_git_integration = T.must(project.external_git_integration)
        webhook_deleted = T.let(false, T::Boolean)
        webhook_id = T.let(nil, T.nilable(Integer))

        begin
          ActiveRecord::Base.transaction do
            unless Db::Lock.try_advisory_xact_lock_with_id(:pr_workflow_on_project, T.must(project.id))
              raise AmlStudio::PrWorkflow::Response::ConcurrentUpdateError
            end

            project.lock!('FOR UPDATE')
            # step 1: check and clear pr workflow setting (if needed)
            external_git_integration.lock!('FOR UPDATE')

            project.update!(settings: project.settings.merge(enabled_pr_workflow: false))

            # step 2: try to delete webhook if the token is deleted
            git_client = AmlStudio::GitFlows::GitClient::ClientBuilder.new(
              git_provider: T.must(project.git_provider),
              token: SourceControl::Backend.passphrase_encryptor.decrypt(T.must(external_git_integration.token)),
            ).call

            # ignore the error here if webhook deletion fail
            begin
              if is_delete_token && external_git_integration.webhook_id.present?
                webhook_id = external_git_integration.webhook_id
                git_client.delete_webhook(T.must(webhook_id))
                webhook_deleted = true
              end
            rescue StandardError => e
              # Silently ignore any errors when deleting webhook
            end

            # update the data into database
            if is_delete_token
              external_git_integration.update!(webhook_secret: nil,
                                               token: nil,
                                               token_expiration_date: nil,
                                               webhook_id: nil,)
            end
          end
        rescue StandardError => e
          # Try to recreate the webhook if it's been deleted but the transaction failed
          if webhook_deleted && webhook_id.present? && external_git_integration.token.present?
            begin
              git_client = AmlStudio::GitFlows::GitClient::ClientBuilder.new(
                git_provider: T.must(project.git_provider),
                token: SourceControl::Backend.passphrase_encryptor.decrypt(T.must(external_git_integration.token)),
              ).call

              webhook_secret = StringUtils.random_string(16)
              # Recreate the webhook
              created_webhook_id = git_client.create_webhook(
                project.webhook_callback_url,
                webhook_secret,
              )

              encrypted_webhook_secret = SourceControl::Backend.passphrase_encryptor.encrypt(webhook_secret)
              external_git_integration.update!(
                webhook_id: created_webhook_id,
                webhook_secret: encrypted_webhook_secret,
              )
            rescue StandardError => webhook_error
              # Silently ignore errors when recreating webhook
            end
          end
          raise e
        end
      end
    end
  end
end
