# typed: true
# frozen_string_literal: true

module AmlStudio
  module GitFlows
    module GitClient
      class ClientBuilder < T::Struct
        const :git_provider, AmlStudio::Values::GitProvider
        const :token, String

        sig { returns(Client) }
        def call
          case git_provider.provider
          when 'GitHub'
            GithubClient.new(git_provider: git_provider, token: token)
          else
            raise Holistics::InvalidOperation, "Unsupported git provider: #{git_provider.provider}"
          end
        end
      end
    end
  end
end
