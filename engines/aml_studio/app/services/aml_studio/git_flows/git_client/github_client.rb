# typed: true

module AmlStudio
  module GitFlows
    module GitClient
      class GithubClient < T::Struct
        include Client
        const :git_provider, AmlStudio::Values::GitProvider
        const :token, String

        sig { override.void }
        def validate_read_pr_permission!
          client.pull_requests(git_provider.repo_path, per_page: 1, state: 'all')
        rescue Octokit::NotFound
          # do nothing
        rescue Octokit::Forbidden, Octokit::Error
          raise Holistics::InvalidOperation, '<PERSON><PERSON> does not have permission to read pull requests'
        end

        sig { override.returns(Time) }
        def token_expiration_date
          # Check if we already have a client with headers from a previous request
          if client.last_response&.headers && client.last_response.headers['github-authentication-token-expiration']
            return Time.parse(client.last_response.headers['github-authentication-token-expiration'])
          end

          # Otherwise make the simplest API call possible
          client.user
          headers = client.last_response.headers

          if headers && headers['github-authentication-token-expiration']
            return Time.parse(headers['github-authentication-token-expiration'])
          end

          raise StandardError, 'Cannot retrieve the token expiration date'
        rescue Octokit::Forbidden, Octokit::Error => e
          raise Holistics::InvalidOperation, 'Invalid token'
        end

        sig do
          override.params(head_branch: String, base_branch: String).returns(T.nilable(AmlStudio::Values::PullRequest))
        end
        def fetch_latest_open_pr(head_branch, base_branch)
          pull_requests = client.pull_requests(
            git_provider.repo_path,
            state: 'open',
            head: "#{git_provider.repo_owner}:#{head_branch}",
            base: base_branch,
            per_page: 1,
            sort: 'created',
            direction: 'desc',
          )

          return nil if pull_requests.empty?

          pr_data = pull_requests.first

          AmlStudio::Values::PullRequest.new(
            merge_commit_sha: pr_data[:merge_commit_sha],
            state: AmlStudio::Values::PrStatus::Open,
            title: pr_data[:title],
            merged_at: pr_data[:merged_at],
            number: pr_data[:number],
            updated_at: pr_data[:updated_at],
            head_branch: pr_data[:head][:ref].to_s,
            base_branch: pr_data[:base][:ref].to_s,
            url: pr_data[:html_url],
          )
        rescue Octokit::NotFound
          nil
        rescue Octokit::Forbidden, Octokit::Error
          raise Holistics::InvalidOperation, 'Token does not have permission to read pull requests'
        end

        sig { override.params(webhook_url: String, webhook_secret: String).returns(Integer) }
        def create_webhook(webhook_url, webhook_secret)
          response = client.create_hook(
            git_provider.repo_path,
            'web',
            {
              url: webhook_url,
              content_type: 'json',
              secret: webhook_secret,
              insecure_ssl: 0,
            },
            events: ['pull_request', 'push'],
            active: true,
          )
          response[:id]
        rescue Octokit::Forbidden
          raise Holistics::InvalidOperation, 'Token does not have permission to write webhooks'
        rescue Octokit::Error => e
          raise StandardError, 'Error while creating webhook, please try again later'
        end

        sig { override.params(webhook_id: Integer).returns(T::Boolean) }
        def delete_webhook(webhook_id)
          return true if webhook_id.nil?

          client.remove_hook(git_provider.repo_path, webhook_id)
          true
        rescue Octokit::Forbidden, Octokit::Error
          raise Holistics::InvalidOperation, 'Token does not have permission to write webhooks'
        end

        sig { override.params(webhook_id: Integer).returns(T::Boolean) }
        def webhook_exists?(webhook_id)
          hook = client.hook(git_provider.repo_path, webhook_id)
          hook.present?
        rescue Octokit::NotFound
          false
        rescue Octokit::Forbidden, Octokit::Error
          raise Holistics::InvalidOperation, 'Token does not have permission to read webhooks'
        end

        sig do
          override.params(
            payload: String,
            signature: String,
            webhook_secret: String,
          ).returns(T::Boolean)
        end
        def self.validate_webhook_signature(payload, signature, webhook_secret)
          actual_signature = signature.gsub('sha256=', '')
          expected_signature = OpenSSL::HMAC.hexdigest(
            OpenSSL::Digest.new('sha256'),
            webhook_secret,
            payload,
          )

          Rack::Utils.secure_compare(actual_signature, expected_signature)
        end

        sig do
          override.params(
            payload: T::Hash[String, T.untyped],
          ).returns(
            {
              action_type: String,
              pr_data: AmlStudio::Values::PullRequest,
              merged_pr_event: T::Boolean,
              open_pr_related_event: T::Boolean,
            },
          )
        end
        def self.parse_pull_request_event(payload)
          is_merged = payload.dig('pull_request', 'merged')
          is_draft = payload.dig('pull_request', 'draft')
          action_type = payload['action']
          is_closed = payload.dig('pull_request', 'state') == 'closed'

          state = if is_merged
                    AmlStudio::Values::PrStatus::Merged
                  elsif is_closed
                    AmlStudio::Values::PrStatus::Closed
                  elsif is_draft
                    AmlStudio::Values::PrStatus::Draft
                  else
                    AmlStudio::Values::PrStatus::Open
                  end

          pr_data = AmlStudio::Values::PullRequest.new(
            number: payload.dig('pull_request', 'number'),
            title: payload.dig('pull_request', 'title'),
            merge_commit_sha: payload.dig('pull_request', 'merge_commit_sha'),
            head_branch: payload.dig('pull_request', 'head', 'ref').to_s,
            base_branch: payload.dig('pull_request', 'base', 'ref').to_s,
            state: state,
            updated_at: Time.parse(payload.dig('pull_request', 'updated_at')),
            merged_at: if payload.dig('pull_request', 'merged_at')
                         Time.parse(payload.dig('pull_request', 'merged_at'))
                       else
                         nil
                       end,
            url: payload.dig('pull_request', 'html_url'),
          )

          open_pr_related_event = case action_type
                                  when 'opened', 'closed', 'reopened', 'ready_for_review'
                                    true
                                  when 'edited'
                                    state == AmlStudio::Values::PrStatus::Open
                                  when 'converted_to_draft'
                                    true
                                  else
                                    false
                                  end

          {
            action_type: action_type,
            pr_data: pr_data,
            open_pr_related_event: open_pr_related_event,
            merged_pr_event: is_merged && action_type == 'closed',
          }
        end

        sig { override.params(payload: T::Hash[String, T.untyped]).returns(T::Hash[String, String]) }
        def self.parse_repo_urls_from_payload(payload)
          {
            html_url: payload.dig('repository', 'html_url').to_s,
            ssh_url: payload.dig('repository', 'ssh_url').to_s,
          }
        end

        private

        sig { returns(Octokit::Client) }
        def client
          @client ||= Octokit::Client.new(access_token: token)
        end
      end
    end
  end
end
