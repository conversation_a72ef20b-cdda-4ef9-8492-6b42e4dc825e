# typed: true

module AmlStudio
  module GitFlows
    module GitClient
      module Client
        extend T::Sig
        extend T::Helpers
        abstract!
        include Kernel

        sig { abstract.params(webhook_url: String, webhook_secret: String).returns(Integer) }
        def create_webhook(webhook_url, webhook_secret); end

        sig { abstract.params(webhook_id: Integer).returns(T::Boolean) }
        def webhook_exists?(webhook_id); end

        sig { abstract.params(webhook_id: Integer).returns(T::Boolean) }
        def delete_webhook(webhook_id); end

        sig { abstract.returns(Time) }
        def token_expiration_date; end

        sig do
          abstract.params(head_branch: String, base_branch: String).returns(T.nilable(AmlStudio::Values::PullRequest))
        end
        def fetch_latest_open_pr(head_branch, base_branch); end

        sig { abstract.void }
        def validate_read_pr_permission!; end

        module ParserMethods
          extend T::Sig
          extend T::Helpers
          abstract!

          # Process a pull request event payload and return all necessary data
          sig do
            abstract.params(
              payload: T::Hash[String, T.untyped],
            ).returns(
              {
                action_type: String,
                pr_data: AmlStudio::Values::PullRequest,
                open_pr_related_event: T::Boolean,
                merged_pr_event: T::Boolean,
              },
            )
          end
          def parse_pull_request_event(payload); end

          sig do
            abstract.params(
              payload: T.untyped,
              signature: String,
              webhook_secret: String,
            ).returns(T::Boolean)
          end
          def validate_webhook_signature(payload, signature, webhook_secret); end

          sig do
            abstract.params(
              payload: T::Hash[String, T.untyped],
            ).returns(T::Hash[String, String])
          end
          def parse_repo_urls_from_payload(payload); end
        end

        mixes_in_class_methods(ParserMethods)
      end
    end
  end
end
