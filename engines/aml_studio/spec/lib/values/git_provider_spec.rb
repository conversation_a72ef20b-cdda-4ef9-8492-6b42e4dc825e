# typed: false
# frozen_string_literal: false

require 'rails_helper'

describe AmlStudio::Values::GitProvider do
  describe '#normalize_ssh_url' do
    it 'formats ssh url correctly' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'github.com',
        root_url: '**************:owner/repo.git',
        path_to_repo: 'owner/repo',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitH<PERSON>',
        repo_owner: 'owner',
        repo_name: 'repo',
      )

      expect(git_provider.normalize_ssh_url).to eq('ssh://**************/owner/repo')
    end
  end

  describe '#normalize_git_url_to_https' do
    it 'converts to https by default' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'github.com',
        root_url: '**************:owner/repo.git',
        path_to_repo: 'owner/repo',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitHub',
        repo_owner: 'owner',
        repo_name: 'repo',
      )

      expect(git_provider.normalize_git_url_to_https).to eq('https://github.com/owner/repo')
    end

    it 'preserves http protocol if specified' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'gitlab.com',
        root_url: 'http://gitlab.com/owner/repo',
        path_to_repo: 'owner/repo',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitLab',
        use_https: false,
        repo_owner: 'owner',
        repo_name: 'repo',
      )

      expect(git_provider.normalize_git_url_to_https).to eq('http://gitlab.com/owner/repo')
    end
  end

  describe 'GitLab URLs with subgroups' do
    it 'handles subgroups in repo_owner method' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'gitlab.com',
        root_url: '**************:group/subgroup/project.git',
        path_to_repo: 'group/subgroup/project',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitLab',
        repo_owner: 'group/subgroup',
        repo_name: 'project',
      )

      expect(git_provider.repo_owner).to eq('group/subgroup')
    end

    it 'normalizes SSH URL correctly with subgroups' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'gitlab.com',
        root_url: '**************:group/subgroup/project.git',
        path_to_repo: 'group/subgroup/project',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitLab',
        repo_owner: 'group/subgroup',
        repo_name: 'project',
      )

      expect(git_provider.normalize_ssh_url).to eq('ssh://**************/group/subgroup/project')
    end

    it 'normalizes to HTTPS correctly with subgroups' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'gitlab.com',
        root_url: '**************:group/subgroup/project.git',
        path_to_repo: 'group/subgroup/project',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitLab',
        repo_owner: 'group/subgroup',
        repo_name: 'project',
      )

      expect(git_provider.normalize_git_url_to_https).to eq('https://gitlab.com/group/subgroup/project')
    end

    it 'handles multiple levels of subgroups' do
      git_provider = AmlStudio::Values::GitProvider.new(
        domain: 'gitlab.com',
        root_url: '**************:group/subgroup1/subgroup2/project.git',
        path_to_repo: 'group/subgroup1/subgroup2/project',
        username: 'git',
        transport_protocol: :ssh,
        provider: 'GitLab',
        repo_owner: 'group/subgroup1/subgroup2',
        repo_name: 'project',
      )

      expect(git_provider.normalize_ssh_url).to eq('ssh://**************/group/subgroup1/subgroup2/project')
      expect(git_provider.normalize_git_url_to_https).to eq('https://gitlab.com/group/subgroup1/subgroup2/project')
    end
  end
end
