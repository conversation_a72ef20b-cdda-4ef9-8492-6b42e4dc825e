# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe AmlStudio::ProjectEvent do
  let(:tenant_id) { 1 }
  let(:project_id) { 1 }
  let(:branch_name) { 'develop' }
  let(:event_type) { AmlStudio::ProjectEvent::EventType::PullRequest }
  let(:data) { { 'pr_id' => 123, 'title' => 'Test PR' } }
  let(:timestamp) { Time.current }
  let(:unix_timestamp) { timestamp.to_i }

  let(:project_event) do
    described_class.new(
      tenant_id: tenant_id,
      project_id: project_id,
      branch_name: branch_name,
      event_type: event_type,
      data: data,
      created_at: timestamp,
      id: 'test-id-123',
    )
  end

  # Mock the belongs_to tenant association from TenantScope
  before do
    allow_any_instance_of(AmlStudio::ProjectEvent).to receive(:tenant).and_return(double('Tenant', id: tenant_id))
  end

  describe 'event types' do
    let(:pull_request_event) do
      described_class.new(
        tenant_id: tenant_id,
        project_id: project_id,
        branch_name: branch_name,
        event_type: AmlStudio::ProjectEvent::EventType::PullRequest,
        data: { 'pr_id' => 456, 'title' => 'Feature PR' },
        created_at: timestamp,
        id: 'pr-event-123',
      )
    end

    let(:deploy_event) do
      described_class.new(
        tenant_id: tenant_id,
        project_id: project_id,
        branch_name: branch_name,
        event_type: AmlStudio::ProjectEvent::EventType::Deploy,
        data: { 'deploy_id' => 456, 'environment' => 'production', status: 'success' },
        created_at: timestamp,
        id: 'deploy-event-123',
      )
    end

    it 'supports PullRequest event type' do
      expect(pull_request_event).to be_valid
      expect(pull_request_event.event_type).to eq(AmlStudio::ProjectEvent::EventType::PullRequest)
    end

    it 'supports Deploy event type' do
      expect(deploy_event).to be_valid
      expect(deploy_event.event_type).to eq(AmlStudio::ProjectEvent::EventType::Deploy)
    end
  end

  describe 'default scope' do
    it 'includes date filter in default scope' do
      sql = described_class.all.to_sql
      expect(sql).to include('aml_studio_project_events.created_at >=')
      expect(sql).to include('aml_studio_project_events.created_at <')
    end

    it 'filters events from DEFAULT_CUTOFF_UNIX_TIMESTAMP to tomorrow beginning of day' do
      allow(Time).to receive(:now).and_return(Time.new(2025, 4, 25, 9, 0, 0, '+07:00'))

      sql = described_class.all.to_sql
      cutoff_time = Time.at(described_class::DEFAULT_CUTOFF_UNIX_TIMESTAMP).utc
      tomorrow_beginning = Time.now.utc.tomorrow.beginning_of_day

      expect(sql).to include(ActiveRecord::Base.connection.quote(cutoff_time).to_s)
      expect(sql).to include(ActiveRecord::Base.connection.quote(tomorrow_beginning).to_s)
    end
  end

  describe '.fetch_latest_events_by_types' do
    let!(:events) do
      event_types = [
        AmlStudio::ProjectEvent::EventType::PullRequest,
        AmlStudio::ProjectEvent::EventType::Deploy,
      ]

      (1..10).map do |i|
        described_class.create!(
          id: i,
          tenant_id: tenant_id,
          project_id: project_id,
          branch_name: branch_name,
          event_type: event_types[i % event_types.length],
          data: { 'id' => i, 'title' => "Event #{i}" },
          created_at: (10 - i).hours.ago,
        )
      end
    end

    it 'fetches the latest event for given project and branch with specific event_type' do
      result = described_class.fetch_latest_events_by_types(
        project_id: project_id,
        branch_name: branch_name,
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )

      expect(result).to be_present
      expect(result.first.event_type).to eq(AmlStudio::ProjectEvent::EventType::PullRequest)
      expect(result.first.project_id).to eq(project_id)
      expect(result.first.branch_name).to eq(branch_name)
    end

    it 'filters by event_type when specified' do
      result = described_class.fetch_latest_events_by_types(
        project_id: project_id,
        branch_name: branch_name,
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )

      expect(result).to be_present
      expect(result.first.event_type).to eq(AmlStudio::ProjectEvent::EventType::PullRequest)
    end

    it 'returns empty array when no events match the criteria' do
      result = described_class.fetch_latest_events_by_types(
        project_id: 999,
        branch_name: 'non-existent',
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )

      expect(result).to eq([])
    end

    it 'fetches the latest events for multiple event types' do
      # Use a unique branch name to isolate this test from existing test data
      multi_test_branch = 'feature/multi-event-test'
      
      # Create events with different event types
      latest_pr_event = described_class.create!(
        id: 100, 
        tenant_id: tenant_id,
        project_id: project_id,
        branch_name: multi_test_branch,
        event_type: AmlStudio::ProjectEvent::EventType::PullRequest,
        data: { 'id' => 100, 'title' => 'Latest PR Event' },
        created_at: 1.hour.ago,
      )
      
      latest_deploy_event = described_class.create!(
        id: 101,
        tenant_id: tenant_id,
        project_id: project_id,
        branch_name: multi_test_branch,
        event_type: AmlStudio::ProjectEvent::EventType::Deploy,
        data: { 'id' => 101, 'title' => 'Latest Deploy Event' },
        created_at: 30.minutes.ago,
      )

      result = described_class.fetch_latest_events_by_types(
        project_id: project_id,
        branch_name: multi_test_branch,
        event_types: [
          AmlStudio::ProjectEvent::EventType::PullRequest,
          AmlStudio::ProjectEvent::EventType::Deploy,
        ],
      )

      expect(result.size).to eq(2)
      
      # Verify we have both types of events
      event_types = result.map(&:event_type)
      expect(event_types).to include(AmlStudio::ProjectEvent::EventType::PullRequest)
      expect(event_types).to include(AmlStudio::ProjectEvent::EventType::Deploy)
      
      # Verify we got the latest events
      pr_event = result.find { |e| e.event_type == AmlStudio::ProjectEvent::EventType::PullRequest }
      deploy_event = result.find { |e| e.event_type == AmlStudio::ProjectEvent::EventType::Deploy }
      
      expect(pr_event.id).to eq(latest_pr_event.id)
      expect(deploy_event.id).to eq(latest_deploy_event.id)
    end
  end
end
