operationId: GitWebhooks_GithubEvent
summary: Process GitHub webhook events
description: Endpoint for receiving GitHub webhook events related to pull requests and push events. Used for AmlStudio PR workflow integration.
tags:
  - Webhook
security: [] # No authentication required for this endpoint
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        description: GitHub webhook payload containing event data
parameters:
  - in: header
    name: X-GitHub-Event
    required: true
    schema:
      type: string
      enum: [pull_request, push, ping]
    description: The type of event that triggered the webhook
  - in: header
    name: X-GitHub-Hook-ID
    required: true
    schema:
      type: string
    description: The ID of the webhook that triggered the request
  - in: header
    name: X-Hub-Signature-256
    required: true
    schema:
      type: string
    description: The HMAC hex digest of the payload, used for verification
responses:
  "200":
    description: Event processed successfully
    content:
      $ref: ../../components/responses/MessageResponse.yml
  "422":
    $ref: ../../components/responses/errors/InvalidOperationError.yml
