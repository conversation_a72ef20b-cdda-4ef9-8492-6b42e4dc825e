{"dashboard": {"id": 1, "title": "MyString", "grid": {"cols": 15, "rows": 16, "cell_size": 80}}, "results": [{"widget": {"id": 1, "dashboard_id": 1, "tenant_id": 1, "source_type": "QueryReport", "source_id": 1, "title": "Report Widget", "data": {}, "grid": {"col": 2, "row": 4, "sizeX": 10, "sizeY": 12}, "created_at": "2018-12-03T08:12:00.000Z", "updated_at": "2018-12-03T08:12:00.000Z"}, "result": {"timezone": "", "vizInputData": {"generated": {"result": {"fields": ["nsm_n_2cb565"], "values": [["alice"]], "meta": {"sort": "-1_asc", "page": 1, "page_size": 25, "num_rows": 1, "cache_key": "explore:Modeling::Values::DataSource1:8df7d8e081c79a452ced7d6190013afd", "last_cache_updated": "2018-12-03T08:12:00.000Z"}, "column_types": ["string"], "aggregated": null}, "extra_details": {"fields": [{"name": "nsm_n_2cb565", "label": "Name", "allowed_values": null, "custom_label": "Name", "format": {"type": "string", "sub_type": "auto"}, "description": null, "type": "text", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsm_n_2cb565 }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "full_path": null, "path_hash": {"field_name": "nsm_n_2cb565"}, "original_path_hash": {"model_id": 1, "field_name": "name", "joins_path": []}, "transformation": null, "rendering_label": "Name"}], "chart_title": "Name", "week_start_day": "default", "sql": null}}, "error": null, "backtrace": null, "error_details": null}, "vizSetting": {"viz_type": "data_table", "fields": {"table_fields": [{"type": "string", "format": {"type": "string", "sub_type": "auto"}, "path_hash": {"field_name": "nsm_n_2cb565"}, "custom_label": "Name", "original_path_hash": {"model_id": 1, "field_name": "name", "joins_path": []}, "original_id": "1_n_92f0a0", "aggregation": null, "original_transformation": null, "original_aggregation": null, "transformation": null}]}, "settings": {"misc": {"pagination_size": 25, "show_row_number": true}, "sort": null, "aggregation": {"show_total": false, "show_average": false}, "conditional_formatting": {}}, "format": {"name": {"type": "string", "index": 0, "sub_type": "auto"}}, "filters": [], "source_type": "QueryReport", "source_id": 1, "adhoc_fields": [], "amql": {"adhoc_fields": [], "conditions": [], "filters": null}, "custom_chart_id": null, "hashid": null}, "dataModel": null, "dataSet": {"id": 1, "title": "test_data_set", "data_source_id": 11, "description": "test", "owner_id": 19, "tenant_id": 1, "root_model_id": null, "slug": "1-test-data-set", "category_id": 0, "data_models": [{"id": 1, "data_source_id": 11, "name": "new_sql_model", "label": "new_sql_model", "description": null, "fields": [{"aggregation_type": null, "custom_label": null, "field_format": null, "dbtype": null, "description": null, "designation": null, "is_custom": false, "is_custom_measure": false, "is_external": false, "is_hidden": false, "joins_path": [], "label": "Age", "model": {"id": 1, "name": "new_sql_model"}, "name": "age", "sql": "{{ #SOURCE.age }}", "transform_type": null, "type": "number", "syntax": "sql"}, {"aggregation_type": null, "custom_label": null, "field_format": null, "dbtype": null, "description": null, "designation": null, "is_custom": false, "is_custom_measure": false, "is_external": false, "is_hidden": false, "joins_path": [], "label": "Name", "model": {"id": 1, "name": "new_sql_model"}, "name": "name", "sql": "{{ #SOURCE.name }}", "transform_type": null, "type": "text", "syntax": "sql"}], "backend": null, "backend_type": "QueryModel", "backend_id": 1, "is_base": true, "category_id": 11, "source_type": null, "owner": null}], "related_joins": [], "join_configs": [], "owner": "admin", "permissions": {"can_crud": null, "can_explore": null, "can_export_data": null, "can_read": null}, "uname": "test_data_set", "from_aml": false, "project_id": null, "reports": null}}, "error": null}, {"widget": {"id": 2, "dashboard_id": 1, "tenant_id": 1, "source_type": "QueryMetric", "source_id": 1, "title": "Report Widget", "data": {}, "grid": {"col": 13, "row": 3, "sizeX": 2, "sizeY": 5}, "created_at": "2018-12-03T08:12:00.000Z", "updated_at": "2018-12-03T08:12:00.000Z"}, "result": {"current": {"value": 1.0, "beg_date": null, "end_date": null, "last_cache_updated": "2018-12-03T08:12:00.000Z", "settings": {"number_format": "auto"}}, "previous": null}, "error": null}]}