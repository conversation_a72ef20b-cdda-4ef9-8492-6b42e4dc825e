[{"widget": "v1", "result": {"vizInputData": {"generated": {"result": {"fields": ["nsmdi_ca_ea7992", "nsmdi_n_a192bb", "nsmdi_p_f5ea82"], "values": [["2019-08-09T00:00:00Z", "egg", "5"]], "meta": {"sort": "-1_asc", "page": 1, "page_size": 25, "num_rows": 1, "last_cache_updated": "2018-12-03T08:12:00.000Z", "cache_key": "explore:Modeling::Values::DataSource11:eea1b6e4338bb6ed5281f82c2d719122"}, "column_types": ["string", "string", "number"], "aggregated": null}, "extra_details": {"fields": [{"name": "nsmdi_ca_ea7992", "label": "Created At", "custom_label": "Created At", "format": {"type": "auto"}, "description": null, "type": "text", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsmdi_ca_ea7992 }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsmdi_ca_ea7992"}, "original_path_hash": {"model_id": 4, "field_name": "created_at"}, "transformation": null, "rendering_label": "Created At"}, {"name": "nsmdi_n_a192bb", "label": "Name", "custom_label": "Name", "format": {"type": "auto"}, "description": null, "type": "text", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsmdi_n_a192bb }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsmdi_n_a192bb"}, "original_path_hash": {"model_id": 4, "field_name": "name"}, "transformation": null, "rendering_label": "Name"}, {"name": "nsmdi_p_f5ea82", "label": "Price", "custom_label": "Price", "format": {"type": "auto"}, "description": null, "type": "number", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsmdi_p_f5ea82 }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsmdi_p_f5ea82"}, "original_path_hash": {"model_id": 4, "field_name": "price"}, "transformation": null, "rendering_label": "Price"}], "chart_title": "Created At, Name, and Price", "week_start_day": "default", "sql": null}}, "error": null, "backtrace": null, "error_details": null}, "vizSetting": {"viz_type": "data_table", "custom_chart_id": null, "fields": {"table_fields": [{"path_hash": {"model_id": 4, "field_name": "created_at"}}, {"path_hash": {"model_id": 4, "field_name": "name"}}, {"path_hash": {"model_id": 4, "field_name": "price"}}]}, "settings": {"misc": {"row_limit": -1, "pagination_size": 25, "show_row_number": true}, "aggregation": {"show_total": false, "show_average": false}, "conditional_formatting": []}, "format": {}, "filters": [{"operator": "greater_than", "modifier": null, "values": ["3"], "options": null, "path_hash": {"field_name": "price", "joins_path": null, "model_id": 4, "data_set_id": 15, "is_metric": false}, "aggregation": null}], "source_type": null, "source_id": null, "adhoc_fields": [], "hashid": null, "amql": {"adhoc_fields": [], "conditions": [], "filters": null}}, "dataModel": null, "dataSet": {"id": 15, "title": "test_data_set", "data_source_id": 11, "description": "test", "owner_id": 13, "tenant_id": 1, "root_model_id": null, "category_id": 0, "uname": "test_data_set2", "project_id": null, "slug": "15-test-data-set", "reports": null, "data_models": [{"id": 4, "data_source_id": 11, "name": "new_sql_model_data_image", "label": "new_sql_model_data_image", "description": null, "backend_type": "QueryModel", "backend_id": 2, "category_id": 8, "fields": [{"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Category Id", "name": "category_id", "sql": "{{ #SOURCE.category_id }}", "transform_type": null, "type": "number", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Created At", "name": "created_at", "sql": "{{ #SOURCE.created_at }}", "transform_type": null, "type": "text", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Merchant Id", "name": "merchant_id", "sql": "{{ #SOURCE.merchant_id }}", "transform_type": null, "type": "number", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Name", "name": "name", "sql": "{{ #SOURCE.name }}", "transform_type": null, "type": "text", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Price", "name": "price", "sql": "{{ #SOURCE.price }}", "transform_type": null, "type": "number", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Status", "name": "status", "sql": "{{ #SOURCE.status }}", "transform_type": null, "type": "text", "syntax": "sql", "joins_path": [], "model": {"id": 4, "name": "new_sql_model_data_image"}, "field_format": null, "is_custom": false, "is_custom_measure": false}], "is_base": true, "backend": null, "source_type": null, "owner": null}], "related_joins": [], "join_configs": [], "permissions": {"can_crud": null, "can_explore": null, "can_export_data": null, "can_read": null}, "from_aml": false, "owner": "admin"}, "timezone": ""}, "error": null}, {"widget": "text_filter", "result": "Filter", "error": null}, {"widget": "text_1", "result": "text_1", "error": null}]