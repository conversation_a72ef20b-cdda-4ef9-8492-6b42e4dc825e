{"vizInputData": {"generated": {"result": {"fields": ["nsm_dat_1a45df", "nsm_v_a38595"], "values": [["2020-02-03T01:02:04.696+00:00", "20"], ["2022-03-03T01:02:04.696+00:00", "30"], ["2020-04-03T01:02:04.696+00:00", "40"], ["2022-04-03T01:02:04.696+00:00", "40"], ["2020-03-03T01:02:04.696+00:00", "30"], ["2021-01-03T01:02:04.696+00:00", "10"], ["2021-02-03T01:02:04.696+00:00", "20"], ["2020-01-03T01:02:04.696+00:00", "10"]], "meta": {"sort": "-1_asc", "page": 1, "page_size": 25, "num_rows": 8, "last_cache_updated": "2018-12-03T08:12:00.000Z", "cache_key": "explore:Modeling::Values::DataSource5:efe375dd757e82949017243d25011fb8"}, "column_types": ["timestamp", "number"], "aggregated": null}, "extra_details": {"fields": [{"name": "nsm_dat_1a45df", "label": "Date And Time", "custom_label": "Date And Time", "format": {"type": "timestamp"}, "description": null, "type": "datetime", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsm_dat_1a45df }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsm_dat_1a45df"}, "original_path_hash": {"model_id": 3, "field_name": "date_and_time"}, "transformation": null, "rendering_label": "Date And Time"}, {"name": "nsm_v_a38595", "label": "Value", "custom_label": "Value", "format": {"type": "number", "format": {"pattern": "none"}}, "description": null, "type": "number", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsm_v_a38595 }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsm_v_a38595"}, "original_path_hash": {"model_id": 3, "field_name": "value"}, "transformation": null, "rendering_label": "Value"}], "chart_title": "Date And Time and Value", "week_start_day": "default", "sql": null}}, "error": null, "backtrace": null, "error_details": null}, "vizSetting": {"viz_type": "data_table", "custom_chart_id": null, "fields": {"table_fields": [{"type": "datetime", "format": {"type": "timestamp"}, "path_hash": {"field_name": "nsm_dat_1a45df"}, "original_path_hash": {"model_id": 3, "field_name": "date_and_time"}, "custom_label": "Date And Time", "original_id": "3_dat_c61856", "original_aggregation": null, "aggregation": null, "original_transformation": null, "transformation": null}, {"type": "number", "format": {"type": "number", "format": {"pattern": "inherited"}}, "path_hash": {"field_name": "nsm_v_a38595"}, "original_path_hash": {"model_id": 3, "field_name": "value"}, "custom_label": "Value", "original_id": "3_v_cc7d13", "original_aggregation": null, "aggregation": null, "original_transformation": null, "transformation": null}]}, "settings": {"misc": {"row_limit": -1, "pagination_size": 25, "show_row_number": true}, "aggregation": {"show_total": false, "show_average": false}, "conditional_formatting": []}, "format": {}, "filters": [], "source_type": null, "source_id": null, "adhoc_fields": [], "hashid": null, "amql": {"adhoc_fields": [], "conditions": [], "filters": null}}, "dataModel": null, "dataSet": {"id": 14, "title": "test_data_set", "data_source_id": 5, "description": "test", "owner_id": 5, "tenant_id": 1, "root_model_id": null, "category_id": 0, "uname": "test_data_set", "project_id": null, "slug": "14-test-data-set", "reports": null, "data_models": [{"id": 3, "data_source_id": 5, "name": "new_sql_model", "label": "new_sql_model", "description": null, "backend_type": "QueryModel", "backend_id": 1, "category_id": 4, "fields": [{"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Date And Time", "name": "date_and_time", "sql": "{{ #SOURCE.date_and_time }}", "transform_type": null, "type": "datetime", "syntax": "sql", "joins_path": [], "model": {"id": 3, "name": "new_sql_model"}, "field_format": null, "is_custom": false, "is_custom_measure": false}, {"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Value", "name": "value", "sql": "{{ #SOURCE.value }}", "transform_type": null, "type": "number", "syntax": "sql", "joins_path": [], "model": {"id": 3, "name": "new_sql_model"}, "field_format": null, "is_custom": false, "is_custom_measure": false}], "is_base": true, "backend": null, "source_type": null, "owner": null}], "related_joins": [], "join_configs": [], "permissions": {"can_crud": null, "can_explore": null, "can_export_data": null, "can_read": null}, "from_aml": false, "owner": "admin"}, "timezone": ""}