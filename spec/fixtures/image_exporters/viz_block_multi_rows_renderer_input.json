{"vizInputData": {"generated": {"result": {"fields": ["nsm_v_a38595"], "values": [["0"], ["1"], ["2"], ["3"], ["4"], ["5"], ["6"], ["7"], ["8"], ["9"], ["10"], ["11"], ["12"], ["13"], ["14"], ["15"], ["16"], ["17"], ["18"], ["19"], ["20"], ["21"], ["22"], ["23"], ["24"], ["25"], ["26"], ["27"], ["28"], ["29"], ["30"], ["31"], ["32"], ["33"], ["34"], ["35"], ["36"], ["37"], ["38"], ["39"], ["40"], ["41"], ["42"], ["43"], ["44"], ["45"], ["46"], ["47"], ["48"], ["49"], ["50"], ["51"], ["52"], ["53"], ["54"], ["55"], ["56"], ["57"], ["58"], ["59"], ["60"], ["61"], ["62"], ["63"], ["64"], ["65"], ["66"], ["67"], ["68"], ["69"], ["70"], ["71"], ["72"], ["73"], ["74"], ["75"], ["76"], ["77"], ["78"], ["79"], ["80"], ["81"], ["82"], ["83"], ["84"], ["85"], ["86"], ["87"], ["88"], ["89"], ["90"], ["91"], ["92"], ["93"], ["94"], ["95"], ["96"], ["97"], ["98"], ["99"], ["100"], ["101"], ["102"], ["103"], ["104"], ["105"], ["106"], ["107"], ["108"], ["109"], ["110"], ["111"], ["112"], ["113"], ["114"], ["115"], ["116"], ["117"], ["118"], ["119"], ["120"], ["121"], ["122"], ["123"], ["124"], ["125"], ["126"], ["127"], ["128"], ["129"], ["130"], ["131"], ["132"], ["133"], ["134"], ["135"], ["136"], ["137"], ["138"], ["139"], ["140"], ["141"], ["142"], ["143"], ["144"], ["145"], ["146"], ["147"], ["148"], ["149"], ["150"], ["151"], ["152"], ["153"], ["154"], ["155"], ["156"], ["157"], ["158"], ["159"], ["160"], ["161"], ["162"], ["163"], ["164"], ["165"], ["166"], ["167"], ["168"], ["169"], ["170"], ["171"], ["172"], ["173"], ["174"], ["175"], ["176"], ["177"], ["178"], ["179"], ["180"], ["181"], ["182"], ["183"], ["184"], ["185"], ["186"], ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], ["194"], ["195"], ["196"], ["197"], ["198"], ["199"], ["200"], ["201"], ["202"], ["203"], ["204"], ["205"], ["206"], ["207"], ["208"], ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], ["215"], ["216"], ["217"], ["218"], ["219"], ["220"], ["221"], ["222"], ["223"], ["224"], ["225"], ["226"], ["227"], ["228"], ["229"], ["230"], ["231"], ["232"], ["233"], ["234"], ["235"], ["236"], ["237"], ["238"], ["239"], ["240"], ["241"], ["242"], ["243"], ["244"], ["245"], ["246"], ["247"], ["248"], ["249"], ["250"], ["251"], ["252"], ["253"], ["254"], ["255"], ["256"], ["257"], ["258"], ["259"], ["260"], ["261"], ["262"], ["263"], ["264"], ["265"], ["266"], ["267"], ["268"], ["269"], ["270"], ["271"], ["272"], ["273"], ["274"], ["275"], ["276"], ["277"], ["278"], ["279"], ["280"], ["281"], ["282"], ["283"], ["284"], ["285"], ["286"], ["287"], ["288"], ["289"], ["290"], ["291"], ["292"], ["293"], ["294"], ["295"], ["296"], ["297"], ["298"], ["299"]], "meta": {"sort": "0_asc", "page": 1, "page_size": 5000, "num_rows": 300, "last_cache_updated": "2018-12-03T08:12:00.000Z", "cache_key": "explore:Modeling::Values::DataSource11:22b503b11159fe876aa68721b8168951"}, "column_types": ["number"], "aggregated": null}, "extra_details": {"fields": [{"name": "nsm_v_a38595", "label": "Value", "custom_label": "Value", "format": {"type": "number", "format": {"pattern": "none"}}, "description": null, "type": "number", "dbtype": null, "is_hidden": false, "sql": "{{ #SOURCE.nsm_v_a38595 }}", "syntax": "sql", "tags": [], "order": 9999, "transform_type": null, "aggregation_type": null, "analytic": null, "original_aggregation": null, "is_literal": false, "type_cast": null, "_external_field": false, "allowed_values": null, "full_path": null, "path_hash": {"field_name": "nsm_v_a38595"}, "original_path_hash": {"model_id": 3, "field_name": "value"}, "transformation": null, "rendering_label": "Value"}], "chart_title": "Value", "week_start_day": "default", "sql": null}}, "error": null, "backtrace": null, "error_details": null}, "vizSetting": {"viz_type": "data_table", "custom_chart_id": null, "fields": {"table_fields": [{"type": "number", "format": {"type": "number", "format": {"pattern": "inherited"}}, "path_hash": {"field_name": "nsm_v_a38595"}, "original_path_hash": {"model_id": 3, "field_name": "value"}, "custom_label": "Value", "original_id": "3_v_cc7d13", "original_aggregation": null, "aggregation": null, "original_transformation": null, "transformation": null}]}, "settings": {"misc": {"row_limit": -1, "pagination_size": 25, "show_row_number": true}, "aggregation": {"show_total": false, "show_average": false}, "conditional_formatting": [], "sort": [{"column": 0, "sortOrder": true}]}, "format": {}, "filters": [], "source_type": null, "source_id": null, "adhoc_fields": [], "hashid": null, "amql": {"filters": null, "conditions": [], "adhoc_fields": []}}, "dataModel": null, "dataSet": {"id": 14, "title": "test_data_set", "data_source_id": 11, "description": "test", "owner_id": 13, "tenant_id": 1, "root_model_id": null, "category_id": 0, "uname": "test_data_set", "project_id": null, "slug": "14-test-data-set", "reports": null, "data_models": [{"id": 3, "data_source_id": 11, "name": "new_sql_model", "label": "new_sql_model", "description": null, "backend_type": "QueryModel", "backend_id": 1, "category_id": 8, "fields": [{"aggregation_type": null, "custom_label": null, "dbtype": null, "description": null, "designation": null, "is_external": false, "is_hidden": false, "label": "Value", "name": "value", "sql": "{{ #SOURCE.value }}", "transform_type": null, "type": "number", "syntax": "sql", "joins_path": [], "model": {"id": 3, "name": "new_sql_model"}, "field_format": null, "is_custom": false, "is_custom_measure": false}], "is_base": true, "backend": null, "source_type": null, "owner": null}], "related_joins": [], "join_configs": [], "permissions": {"can_crud": null, "can_explore": null, "can_export_data": null, "can_read": null}, "from_aml": false, "owner": "admin"}, "timezone": ""}