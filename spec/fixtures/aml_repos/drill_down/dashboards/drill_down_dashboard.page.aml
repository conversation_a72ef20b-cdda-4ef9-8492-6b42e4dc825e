Dashboard drill_down_dashboard {
  title: 'Drill down test dashboard'
  view: TabLayout {
    label: 'View 1'
    tab tab1: LinearLayout {
      label: 'Tab 1'
      block pie_chart {
      }
      block table {
      }
      block column_chart {
      }
    }
    tab tab2: LinearLayout {
      label: 'Pivot table'
      block pivot_table {
      }
    }
  }
  block pie_chart: VizBlock {
    label: 'Count of Id by Name'
    viz: PieChart {
      dataset: ecommerce
      legend: VizFieldFull {
        ref: ref('data_modeling_products', 'name')
        format {
          type: 'text'
        }
      }
      series {
        field: VizFieldFull {
          ref: ref('data_modeling_products', 'id')
          aggregation: 'count'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
        settings {
          color_palette: -2
        }
      }
    }
  }
  block table: VizBlock {
    label: 'Table'
    viz: DataTable {
      dataset: ecommerce
      fields: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'name')
          format {
            type: 'text'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'status')
          format {
            type: 'text'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_number: true
      }
    }
  }
  block column_chart: VizBlock {
    label: 'Sum of Price by Products Name'
    viz: ColumnChart {
      dataset: ecommerce
      x_axis: VizFieldFull {
        ref: ref('data_modeling_products', 'name')
        format {
          type: 'text'
        }
      }
      y_axis {
        series {
          field: VizFieldFull {
            ref: ref('data_modeling_products', 'price')
            aggregation: 'sum'
            format {
              type: 'number'
              pattern: 'inherited'
            }
          }
          settings {
            color_palette: -2
          }
        }
      }
      settings {
        x_axis_show_null_datetime: false
      }
    }
  }
  block pivot_table: VizBlock {
    label: 'pivot'
    viz: PivotTable {
      dataset: ecommerce
      rows: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'created_at')
          transformation: 'datetrunc month'
          format {
            type: 'date'
            pattern: 'LLL yyyy'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'status')
          format {
            type: 'text'
          }
        }
      ]
      columns: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'id')
          format {
            type: 'text'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'merchant_id')
          format {
            type: 'text'
          }
        }
      ]
      values: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_total: true
        show_column_total: true
        show_sub_total: true
        row_limit: 50000
        aggregate_awareness {
          enabled: true
          debug_comments: true
        }
      }
    }
  }
}
