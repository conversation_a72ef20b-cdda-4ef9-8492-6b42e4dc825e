# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Dbt integration', js: true do
  include_context 'aml_studio_dataset'
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:token) { 'foobar' }
  let(:account_id) { 118469 }
  let(:ds) { get_test_ds }

  before do
    studio_url = "/studio/projects/#{project.id}/explore"
    safe_login(admin, studio_url)

    sleep 2
  end

  shared_examples 'CRUD dbt credentials' do

    it 'can add new and update and remove credentials' do
      job_id = '123456'
      token = 'abc'
      account_id = '123'

      add_dbt_integration(job_id, token, account_id)

      check_credentials_in_db('abc', '123')

      open_update_modal

      expect(get_token_input_elem[:placeholder]).to eq(Utils.mask('abc', 4))
      expect(get_account_id_input_elem[:placeholder]).to eq('123')

      close_modal

      click_remove_credentials

      sleep 1

      check_credentials_in_db(nil, nil)
    end

    it 'can add multiple dbt jobs' do
      job_id_1 = '123456'
      job_id_2 = '123457'
      token = 'abc'
      account_id = '123'

      add_dbt_integration(job_id_1, token, account_id)
      add_dbt_integration_after_have_credentials(job_id_2)

      check_dbt_job_in_db(ds.id, job_id_1)
      check_dbt_job_in_db(ds.id, job_id_2)
    end

    it 'can delete dbt integration' do
      job_id = '123456'
      token = 'abc'
      account_id = '123'

      add_dbt_integration(job_id, token, account_id)

      check_credentials_in_db('abc', '123')

      # Find and click delete button
      find('[data-ci="ci-dbt-delete-integration-button"]').click

      wait_for_element_load('.confirm-modal')
      safe_click('.ci-confirm')

      sleep 1

      expect(DbtIntegration.find_by(data_source_id: ds.id, dbt_job_id: job_id)).to be_nil
    end

    it 'can edit existing integration with new dbt job' do
      job_id_1 = '123456'
      job_id_2 = '123457'
      token = 'abc'
      account_id = '123'

      add_dbt_integration(job_id_1, token, account_id)

      check_dbt_job_in_db(ds.id, job_id_1)

      edit_dbt_integration(job_id_1, job_id_2)

      expect(DbtIntegration.count).to eq(1)
      expect(DbtIntegration.first.dbt_job_id.to_i).to eq(job_id_2.to_i)
    end

    it 'can upload duplicated dbt job and not modifying existing integration records' do
      job_id_1 = '123456'
      token = 'abc'
      account_id = '123'

      add_dbt_integration(job_id_1, token, account_id)

      check_dbt_job_in_db(ds.id, job_id_1)

      add_dbt_integration_after_have_credentials(job_id_1)

      expect(DbtIntegration.count).to eq(1)
      expect(DbtIntegration.first.dbt_job_id.to_i).to eq(job_id_1.to_i)
    end

  end

  context 'dbt stored in tenant' do
    before do
      FeatureToggle.toggle_tenant(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, tenant.id, false)
      url = '/manage/integrations'
      safe_login(admin, url)
    end

    def check_credentials_in_db(token, account_id)
      tenant.reload
      expect(tenant.dbt_token).to eq(token)
      expect(tenant.dbt_account_id.to_s).to eq(account_id.to_s)
    end

    it_behaves_like 'CRUD dbt credentials'

    after do
      FeatureToggle.toggle_tenant(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, tenant.id, false)
    end
  end

  context 'dbt stored in project' do
    before do
      FeatureToggle.toggle_tenant(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, tenant.id, true)
      url = '/manage/integrations'
      safe_login(admin, url)
    end

    def check_credentials_in_db(token, account_id)
      project.reload
      expect(project.dbt_token).to eq(token)
      expect(project.dbt_account_id.to_s).to eq(account_id.to_s)

      tenant.reload
      expect(tenant.dbt_token).to eq(token)
      expect(tenant.dbt_account_id.to_s).to eq(account_id.to_s)
    end

    it_behaves_like 'CRUD dbt credentials'

    after do
      FeatureToggle.toggle_tenant(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, tenant.id, false)
    end
  end

  def edit_dbt_integration(job_id, new_job_id)
    wait_for_element_load('[data-ci="ci-dbt-edit-button"]')
    safe_click('[data-ci="ci-dbt-edit-button"]')

    wait_for_element_load('[data-ci="ci-dbt-job-id-input"]')
    job_id_input_elem = get_job_id_input_elem

    get_job_id_input_elem.set new_job_id

    safe_click('.ci-modal-submit')
  end

  def add_dbt_integration(job_id, token, account_id)
    allow_any_instance_of(WebApis::Dbt::DbtApi).to receive(:get_job).and_return({ :status => { :is_success => true } })
    allow_any_instance_of(Dbt::ValidateCredentials).to receive(:call).and_return({ :status => 'OK' })
    allow_any_instance_of(WebApis::Dbt::DbtApi).to receive(:test_connection).and_return({ :status => { :is_success => true } })

    open_connect_modal
    token_input_elem = get_token_input_elem
    account_id_input_elem = get_account_id_input_elem
    job_id_input_elem = get_job_id_input_elem

    expect(token_input_elem.value).to eq('')
    expect(account_id_input_elem.value).to eq('')
    expect(job_id_input_elem.value).to eq('')

    token_input_elem.set token
    account_id_input_elem.set account_id.to_s
    job_id_input_elem.set '123456'

    safe_click('.ci-modal-submit')

    sleep 1

    check_credentials_in_db(token, account_id.to_s)

    open_update_modal
    token_input_elem = get_token_input_elem
    account_id_input_elem = get_account_id_input_elem

    expect(token_input_elem.value).to eq('')
    expect(account_id_input_elem.value).to eq('')

    token_input_elem.set 'abc'
    account_id_input_elem.set '123'

    submit_update

    sleep 1
  end

  def add_dbt_integration_after_have_credentials(job_id)
    allow_any_instance_of(WebApis::Dbt::DbtApi).to receive(:get_job).and_return({ :status => { :is_success => true } })
    allow_any_instance_of(Dbt::ValidateCredentials).to receive(:call).and_return({ :status => 'OK' })
    allow_any_instance_of(WebApis::Dbt::DbtApi).to receive(:test_connection).and_return({ :status => { :is_success => true } })

    open_connect_modal
    job_id_input_elem = get_job_id_input_elem

    expect(job_id_input_elem.value).to eq('')

    job_id_input_elem.set job_id

    safe_click('.ci-modal-submit')

    sleep 1
  end

  def check_dbt_job_in_db(data_source_id, job_id)
      dbt_job = DbtIntegration.find_by(data_source_id: data_source_id.to_i, dbt_job_id: job_id.to_i)
      expect(dbt_job).to be_present
    end

  def click_remove_credentials
    wait_for_element_load('[data-ci="ci-dbt-connection"]')
    safe_click('[data-ci="ci-dbt-remove-credentials-button"]')
    safe_click('.unlink-btn')

    wait_for_element_load('.confirm-modal')
    safe_click('.ci-confirm')
  end

  def close_modal
    page.send_keys :escape
  end

  def open_connect_modal
    wait_for_element_load('[data-ci="ci-dbt-connection"]')
    safe_click('[data-ci="ci-dbt-connect-button"]')
  end

  def open_update_modal
    wait_for_element_load('[data-ci="ci-dbt-connection"]')
    safe_click('[data-ci="ci-dbt-update-credentials-button"]')
  end

  def submit_update
    safe_click('.ci-modal-submit')
    wait_for_element_load('.confirm-modal')
    safe_click('.ci-confirm')
  end

  def get_token_input_elem
    page.first('[data-ci="ci-dbt-token-input"]')
  end

  def get_account_id_input_elem
    page.first('[data-ci="ci-dbt-account-id-input"]')
  end

  def get_job_id_input_elem
    page.first('[data-ci="ci-dbt-job-id-input"]')
  end

  def fill_in(selector, text)
    all(selector, visible: false).first.set text
  end
end
