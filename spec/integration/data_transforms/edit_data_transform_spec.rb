# typed: false
require 'rails_helper'
require 'action_view'

describe 'Editing', js: true, legacy: true do
  let(:admin) {get_test_admin}
  let!(:data_transform) {
    FactoryBot.create :data_transform, tenant_id: admin.tenant_id
  }

  def ace_set_text(text)
    text_escaped = text.gsub("\n", "\\n")
    js = <<-JS.strip_heredoc
      var editor = window.ace.edit('data-transform-editor');
      editor.setValue("#{text_escaped}")
    JS
    page.execute_script(js)
  end

  it 'updates a data transform' do
    config = {
      :transform_type => 'Incremental Transform',
      :query => 'select id,name,email from users',
      :title => 'Edit data transforms',
      :data_source_id => 3,
      :dest_schema_name => 'public',
      :dest_table_name => 'tenant',
      :repeat => "0,15,30,45 * * * *",
      :dist_key => "id",
      :dist_style => "KEY",
      :sort_keys => "id",
      :sort_style => "SINGLE"
    }

    safe_login(admin, "/data_transforms/#{data_transform.id}/edit")
    wait_for_element_load('#ci-data-transforms-title')
    fill_text('#ci-data-transforms-title', config[:title])
    ace_set_text(config[:query])

    h_radio_check(label: config[:transform_type])
    select_h_select_option('.ci-data-source', label: 'pg')
    select_h_select_option('.ci-select-schema', value: 'public')
    fill_text('.ci-table-name', config[:dest_table_name])

    page.find('#ci-add-schedule').click
    wait_for_element_load('.ci-ss-repeatby')
    sleep 1
    select_h_select_option('.ci-ss-repeatby', value: 'Hourly')
    sleep 1

    page.find('.ci-schedule-done').click()
    sleep 1

    page.find('#ci-validate-query').click()
    sleep 1.5

    page.find('#ci-preview-auto-fill').click()
    sleep 1

    page.find('#ci-validate-table').click()
    page.find('#ci-advanced-options').click()
    page.find('#ci-save').click()

    wait_for { page.current_path == '/data_transforms' }
    data_transform.reload

    expect(data_transform[:query]).to eq config[:query]
    expect(data_transform[:title]).to eq config[:title]
    expect(data_transform[:dest_schema_name]).to eq config[:dest_schema_name]
    expect(data_transform[:dest_table_name]).to eq config[:dest_table_name]
  end

end
