# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'embed_portal_with_ssbi', :js do
  include_context 'embed_portal_with_ssbi'

  def dashboard_node_selector(dashboard_id)
    "[data-hui-key=\"Dashboard-#{dashboard_id}\"]"
  end

  it 'can create dashboard in org workspace' do
    visit embed_url

    safe_click('[data-ci="button-create-org-workspace-dashboard"]')
    wait_expect(true) { page.has_css?('[data-hui-key="new-org-dashboard"') }
    sleep 1

    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'New test org dashboard')
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    new_dashboard = Dashboard.find_by(title: 'New test org dashboard')

    expect(new_dashboard.external_user_item.is_personal).to eq(false)
    # should remove the temporary creation node
    wait_expect(true) { page.has_no_css?('[data-hui-key="new-org-dashboard"') }
    # navigate to new node and highlight it
    wait_expect(true) { page.has_css?("#{dashboard_node_selector(new_dashboard.id)} .bg-blue-50") }
  end

  it 'can create dashboard in team workspace and edit' do
    visit embed_url

    safe_click('[data-ci="button-create-personal-workspace-dashboard"]')
    wait_expect(true) { page.has_css?('[data-hui-key="new-personal-dashboard"') }
    sleep 1

    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'New test personal dashboard')
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    new_dashboard = Dashboard.find_by(title: 'New test personal dashboard')
    expect(new_dashboard.external_user_item.is_personal).to eq(true)
    # should remove the temporary creation node
    wait_expect(true) { page.has_no_css?('[data-hui-key="new-personal-dashboard"') }
    # navigate to new node and highlight it
    wait_expect(true) { page.has_css?("#{dashboard_node_selector(new_dashboard.id)} .bg-blue-50") }

    # edit dashboard
    safe_click('[data-ci="ci-edit-canvas-dashboard"]')

    safe_click('[data-ci="edit-dashboard-title"]')
    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'Edit dashboard title')
    page.send_keys(:enter)
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    wait_expect('Edit dashboard title') { page.find(dashboard_node_selector(new_dashboard.id)).text }
    new_dashboard.reload
    expect(new_dashboard.title).to eq('Edit dashboard title')
  end

  it 'can delete dashboard' do
    org_dashboard
    visit embed_url

    dashboard_node_css = dashboard_node_selector(org_dashboard.id)
    wait_for_element_load(dashboard_node_css)
    page.find(dashboard_node_css).hover
    safe_click('[data-ci="more-embed-portal-node-action"]')
    safe_click('[data-ci="delete-embed-portal-node"]')
    safe_click('.ci-confirm-delete')
    expect(page).to have_css('[data-ci="embed-portal-loading-node"]')

    wait_for_all_ajax_requests
    expect(Dashboard.where(id: org_dashboard.id).exists?).to be(false)
  end
end
