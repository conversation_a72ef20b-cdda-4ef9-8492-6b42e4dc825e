# typed: false

require 'rails_helper'

describe 'embed portal', :js do
  before do
    FeatureToggle.toggle_global(EmbedPortal::FT_EMBED_PORTAL_SSBI_ENABLED, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EXPLICIT_GIT, true)
  end

  context 'default embed portal' do
    include_context 'preview_embed_portal_context' do
      let(:embed_payload) do
        {
          object_name: embed_portal.uname,
          object_type: 'EmbedPortal',
          embed_user_id: 'test_embed_user',
          settings: {
            allow_to_change_dashboard_timezone: true,
            default_timezone: 'Asia/Singapore',
          },
        }
      end
    end

    it 'renders embed portal with timezone settings and handle navigation' do
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')

      safe_click("[data-value='Dashboard-#{embed_dashboard.id}']")
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal-dashboard"]')

      safe_click("[data-value='Dataset-#{dataset.id}']")
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal-dataset"]')
      expect(page).to have_no_css('[data-ci="ci-allow-change-timezone"]')
      expect(page.find('[data-ci="ci-query-processing-timezone"]').text).to eq('Asia/Singapore')
    end

    it 'renders embed portal dashboard' do
      visit embed_dashboard_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal-dashboard"]')
    end

    it 'renders embed portal dataset' do
      visit embed_dataset_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal-dataset"]')
    end
  end

  context 'with personal workspace enabled' do
    include_context 'preview_embed_portal_context' do
      let(:embed_payload) do
        {
          object_name: embed_portal.uname,
          object_type: 'EmbedPortal',
          embed_user_id: 'test_embed_user',
          permissions: {
            enable_personal_workspace: true,
          },
        }
      end
    end

    it 'enables personal workspace in left explorer' do
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_text('Personal workspace')
      expect(page).to have_css('[data-ci="button-create-personal-workspace-dashboard"]')
    end
  end

  context 'with organization workspace enabled, role = viewer' do
    include_context 'preview_embed_portal_context' do
      let(:embed_payload) do
        {
          object_name: embed_portal.uname,
          object_type: 'EmbedPortal',
          embed_user_id: 'test_embed_user',
          embed_org_id: 'test_embed_org',
          permissions: {
            org_workspace_role: 'viewer',
          },
        }
      end
    end

    it 'enables organization workspace in left explorer' do
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_text('Shared workspace')
      expect(page).to have_no_css('[data-ci="button-create-org-workspace-dashboard"]')
    end
  end

  context 'with organization workspace enabled, role = editor' do
    include_context 'preview_embed_portal_context' do
      let(:embed_payload) do
        {
          object_name: embed_portal.uname,
          object_type: 'EmbedPortal',
          embed_user_id: 'test_embed_user',
          embed_org_id: 'test_embed_org',
          permissions: {
            org_workspace_role: 'editor',
          },
        }
      end
    end

    it 'enables organization workspace in left explorer' do
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_text('Shared workspace')
      expect(page).to have_css('[data-ci="button-create-org-workspace-dashboard"]')
    end
  end

  context 'with organization workspace and personal workspace enabled' do
    include_context 'preview_embed_portal_context' do
      let(:embed_payload) do
        {
          object_name: embed_portal.uname,
          object_type: 'EmbedPortal',
          embed_user_id: 'test_embed_user',
          embed_org_id: 'test_embed_org',
          permissions: {
            enable_personal_workspace: true,
            org_workspace_role: 'editor',
          },
        }
      end
    end

    it 'enables organization workspace and personal workspace in left explorer' do
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_text('Shared workspace')
      expect(page).to have_text('Personal workspace')
      expect(page).to have_css('[data-ci="button-create-org-workspace-dashboard"]')
      expect(page).to have_css('[data-ci="button-create-personal-workspace-dashboard"]')
    end
  end

  describe 'with user attribute' do
    include_context 'preview_embed_portal_context'
    include_context 'aml_studio_embed_row_level_permissions'

    let(:embed_portal) do
      EmbedPortal.find_by!(uname: 'app')
    end

    def build_embed_url(user_attributes)
      embed_payload = {
        object_name: embed_portal.uname,
        object_type: 'EmbedPortal',
      }.merge(user_attributes: user_attributes)
      embed_token = EmbedLinks::JsonWebToken.encode(embed_payload, embed_link.secret_key)
      "/embed/#{embed_link.hash_code}?_token=#{embed_token}"
    end

    it 'missing user attribute' do
      embed_url = build_embed_url({ vendor_id: 1 })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_text("Missing user attributes: 'country_name'")
    end

    it 'invalid user attribute type' do
      embed_url = build_embed_url({ vendor_id: 'A', country_name: 'Vietnam' })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_text('Attribute "vendor_id": Invalid value for type number: "A"')
    end
  end

  describe 'with general settings' do
    include_context 'preview_embed_portal_context'

    def build_embed_url(object_type, object_id, settings)
      embed_payload = {
        object_name: embed_portal.uname,
        object_type: 'EmbedPortal',
      }.merge(settings: settings)
      embed_token = EmbedLinks::JsonWebToken.encode(embed_payload, embed_link.secret_key)
      "/embed/#{embed_link.hash_code}/#{object_type}s/#{object_id}?_token=#{embed_token}"
    end

    it 'can export dashboard data' do
      embed_url = build_embed_url('dashboard', embed_dashboard.id, { allow_to_export_raw_data: true })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_css('[data-ci="ci-export-dropdown"]')
      safe_click('[data-ci="ci-export-dropdown"]')
      expect(page).to have_text('Excel')
    end

    it 'cannot export dashboard data' do
      embed_url = build_embed_url('dashboard', embed_dashboard.id, { allow_to_export_raw_data: false })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_css('[data-ci="ci-export-dropdown"]')
      safe_click('[data-ci="ci-export-dropdown"]')
      expect(page).to have_no_text('Excel')
    end

    it 'can export dataset data' do
      embed_url = build_embed_url('dataset', dataset.id, { allow_to_export_raw_data: true })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_css('[data-ci="ci-export-dropdown"]')
      safe_click('[data-ci="ci-export-dropdown"]')
      expect(page).to have_text('Excel')
    end

    it 'cannot export dataset data' do
      embed_url = build_embed_url('dataset', dataset.id, { allow_to_export_raw_data: false })
      visit embed_url
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="embed-portal"]')
      expect(page).to have_no_css('[data-ci="ci-export-dropdown"]')
    end
  end

  context 'create external user' do
    include_context 'embed_portal_with_ssbi'

    it 'auto create external user when first visit' do
      visit embed_url

      expect(ExternalUser.exists?(client_user_id: client_user_id)).to be(true)
    end
  end
end
