# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'embed portal preview', :js do
  include_context 'aml_studio_embed_portal'

  let!(:tenant) { get_test_tenant }
  let!(:embed_portal) { EmbedPortal.find_by(uname: 'test_portal') }
  let!(:embed_credentials) do
    embed_link = create(:embed_link, source: project, tenant: tenant, version: 4)
    embed_link.set_public_user
    embed_link
  end

  before do
    deploy_result
  end

  def visit_embed_management_page
    qlogin(admin, '/tools/embed')
    wait_for_all_ajax_requests
  end

  context 'it works' do
    before do
      FeatureToggle.toggle_global(EmbedLink::FT_NEW_EMBED_ANALYTICS_UI, true)
      FeatureToggle.toggle_global(EmbedPortal::FT_EMBED_PORTAL_SSBI_ENABLED, true)
    end

    it 'can preview the embed portal' do
      visit_embed_management_page
      expect(page).to have_css('[data-ci="embed-portal-list"]')
      expect(page.first('[data-ci="embed-portal-preview-button"]')).not_to be_disabled
      safe_click('[data-ci="embed-portal-preview-button"]')
      expect(page).to have_css('[data-ci="preview-embed-portal-modal"]')

      expect(page.find('[data-ci="preview-embed-portal-modal-preview-button"]')).not_to be_disabled
      safe_click('[data-ci="preview-embed-portal-modal-preview-button"]')

      within_frame find('iframe[data-ci="preview-embed-portal-modal-iframe"]') do
        wait_for_element_load '[data-ci="embed-portal"]'
        expect(page).to have_css('[data-ci="embed-portal"]')
      end
    end

    it 'shows expired message when the token is expired' do
      visit_embed_management_page
      expect(page).to have_css('[data-ci="embed-portal-list"]')
      expect(page.first('[data-ci="embed-portal-preview-button"]')).not_to be_disabled
      safe_click('[data-ci="embed-portal-preview-button"]')
      expect(page).to have_css('[data-ci="preview-embed-portal-modal"]')

      safe_click('[data-ci="embed-portal-expiry-duration-select"]')
      safe_click('[data-value="900"]') # 15 minutes

      expect(page.find('[data-ci="preview-embed-portal-modal-preview-button"]')).not_to be_disabled
      safe_click('[data-ci="preview-embed-portal-modal-preview-button"]')

      within_frame find('iframe[data-ci="preview-embed-portal-modal-iframe"]') do
        wait_for_element_load '[data-ci="embed-portal"]'
      end

      # Set browser time 16 minutes into the future
      page.execute_script('
        const now = new Date();
        now.setMinutes(now.getMinutes() + 16);
        const futureTime = now.getTime();
        Date = class extends Date {
          now() { return futureTime; }
          getTime() { return futureTime; }
        };
      '.strip)
      wait_expect(true) { page.has_css?('[data-ci="embed-portal-expired-banner"]') }
    end
  end
end
