# typed: false
require 'rails_helper'

describe 'search user', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:new_group) { {name: 'test group'} }
  let!(:group1) {FactoryBot.create :group, name: 'Group1', tenant_id: get_test_tenant.id}

  it 'should edit first group' do
    qlogin :admin, '/manage/groups'
    wait_for_element_load('.ci-group-table')
    wait_for_element_load('.ci-edit-group')
    page.first('.ci-edit-group').click
    fill_in 'group_name', with: 'New name New name'
    page.find('.ci-submit').click
    expect(page.has_content?("New name New name"))
  end

  it 'check whether an empty name' do
    qlogin :admin, '/manage/groups'
    wait_for_element_load('.ci-group-table')
    wait_for_element_load('.ci-edit-group')
    page.first('.ci-edit-group').click
    fill_in 'group_name', with: ''
    page.find('.ci-submit').click
    expect(page.has_content?("Name is required"))
  end

end
