# typed: false
require 'rails_helper'

describe 'search user', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:new_group) { {name: 'test group'} }

  def create_new_group(group)
    page.find('.ci-new-groups').click
    wait_for_element_load('.ci-new-group')
    page.find('.ci-new-group-input').set(group[:name])
    page.find('.ci-new-group-btn').click
    sleep 0.5
  end

  it 'should render only one group on table' do
    qlogin :admin, '/manage/groups'
    wait_for_element_load('.ci-group-table')
    create_new_group new_group
    expect(page.all('.ci-group-table tbody tr').length).to eq 1
  end
end
