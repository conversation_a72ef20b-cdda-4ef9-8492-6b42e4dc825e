# frozen_string_literal: true
# typed: false

require 'rails_helper'

EXPORT_SELECTOR = '[data-ci="ci-export-dropdown"]'

describe 'Embed Dashboard Export Setting', :js do
  include_context 'canvas_dashboard' # Provides analyst, admin, data_set etc.

  let(:dashboard_definition) do
    {
      title: 'Export Test Dashboard V4',
      uname: 'export_test_dashboard',
      blocks: [
        { type: 'TextBlock', uname: 'txt1', content: 'Hello' }, # Add a simple block
      ],
      views: [{ type: 'LinearLayout', uname: 'linear' }], # Add a view
      interactions: [],
      settings: { autorun: true },
    }
  end

  let(:dashboard) do
    create(:dashboard,
           version: 4,
           owner: analyst,
           tenant: analyst.tenant,
           definition: dashboard_definition,)
  end

  let(:embed_link) do
    el = create(
      :embed_link,
      source: dashboard,
      tenant: analyst.tenant,
      version: 4,
      user: analyst,
    )
    el.set_public_user
    el.share_source
    el
  end

  let(:secret_key) { embed_link.secret_key }
  let(:base_url) { "/embed/#{embed_link.hash_code}" }

  # Helper to generate token and URL
  def generate_embed_url(payload)
    token = jwt_encode(secret_key, payload, Time.now.to_i + 1000)
    "#{base_url}?_token=#{token}"
  end

  context 'when FT "embed:enable_dashboard_export_setting" is enabled' do
    before do
      FeatureToggle.toggle_global(Tenant::FT_EMBED_ENABLE_DASHBOARD_EXPORT_SETTING, true)
    end

    it 'shows export button when enable_dashboard_export is explicitly true' do
      payload = { settings: { enable_dashboard_export: true } }
      url = generate_embed_url(payload)
      visit(url)
      wait_for_element_load('.holistics.embed')
      expect(page).to have_selector(EXPORT_SELECTOR)
    end

    it 'hides export button when enable_dashboard_export is explicitly false' do
      payload = { settings: { enable_dashboard_export: false } }
      url = generate_embed_url(payload)
      visit(url)
      wait_for_element_load('.holistics.embed')
      expect(page).to have_no_selector(EXPORT_SELECTOR)
    end

    it 'hides export button when enable_dashboard_export is not specified (defaults to false)' do
      payload = { settings: {} } # Empty settings hash
      url = generate_embed_url(payload)
      visit(url)
      wait_for_element_load('.holistics.embed')
      expect(page).to have_no_selector(EXPORT_SELECTOR)
    end

    it 'hides export button when enable_dashboard_export is not specified, regardless of hide_header_panel' do
      payload = { settings: { hide_header_panel: false } }
      url = generate_embed_url(payload)
      visit(url)
      wait_for_element_load('.holistics.embed')
      expect(page).to have_no_selector(EXPORT_SELECTOR)
    end
  end

  context 'when FT "embed:enable_dashboard_export_setting" is disabled' do
    before do
      FeatureToggle.toggle_global(Tenant::FT_EMBED_ENABLE_DASHBOARD_EXPORT_SETTING, false)
    end

    it 'shows export button regardless of enable_dashboard_export setting' do
      [true, false, nil].each do |value|
        payload = {
          settings: value.nil? ? {} : { enable_dashboard_export: value },
        }
        url = generate_embed_url(payload)
        visit(url)
        wait_for_element_load('.holistics.embed')
        expect(page).to have_selector(EXPORT_SELECTOR)
      end
    end
  end

  context 'when live edit' do
    include_context 'dashboard_v4_timezone_context'
    include_context 'timezone_dynamic_dashboard'

    let(:tz) { 'Asia/Bangkok' }
    let(:timezone_canvas_dashboard) do
      create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, tz)
    end

    it 'does not show export button' do
      safe_login(admin, dashboard_path(timezone_canvas_dashboard))
      safe_click('[data-ci="ci-edit-canvas-dashboard"]')
      wait_for_widget_load

      expect(page).to have_no_selector(EXPORT_SELECTOR)
    end
  end
end
