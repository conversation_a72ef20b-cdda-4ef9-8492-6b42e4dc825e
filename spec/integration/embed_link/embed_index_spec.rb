# typed: false
require 'rails_helper'

describe 'Embed management', js: true do
  let(:admin) { get_test_admin }
  let!(:embed_link) { create :embed_link, filter_ownerships: [] }
  let!(:tsub) { create :tenant_subscription, embed_workers: 2 }

  before do
    embed_link.set_public_user
  end

  def open_embed_links
    safe_login admin, '/tools/embed'
    wait_for_element_load('.embed-table')
  end

  def set_up_tsub(plan_name: nil, period_type: '', customer_id: '', add_users: 0, add_objects: 0,
    status: 'trial.active', group_version: 2, max_restriction_level: 'none')
    tsub.zoho[:customer_id] = customer_id
    tsub.plan = Plan.find_by_name_and_version(plan_name, group_version)
    tsub.period_type = period_type
    tsub.add_users = add_users
    tsub.add_objects = add_objects
    tsub.status = status
    tsub.max_restriction_level = max_restriction_level

    tsub.save!
  end

  context 'embed management' do
    let(:usage_rule) { FactoryBot.create :usage_rule, name: 'embed_worker' }
    let(:pg_3) { FactoryBot.create :pricing_group, name: 'v7g', id: 3 }
    before do
      FactoryBot.create :plan, name: 'standard', pricing_group: pg_3, visible: true
      set_up_tsub(plan_name: 'standard', period_type: 'monthly', group_version: 3, add_objects: 1000, status: 'active', max_restriction_level: 'hard')
      FactoryBot.create :plan_usage_rule, plan: tsub.plan, usage_rule: usage_rule, per_additional_usage: 100
    end

    shared_examples 'toggle embed workers' do
      it 'enable / disable embed worker' do
        open_embed_links
        safe_click '.ci-toggle-embed-workers'
        wait_for_element_load('.ci-submit-toggle-embed-link')
        safe_click '.ci-submit-toggle-embed-link'
        expect_notifier_content /successfully/
        click_sel '[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])' # close notification

        safe_click '.ci-toggle-embed-workers'
        wait_for_element_load('.ci-submit-toggle-embed-link')
        safe_click '.ci-submit-toggle-embed-link'
        expect_notifier_content /successfully/
      end
    end

    it_behaves_like 'toggle embed workers'

    it 'increase / reduce embed workers' do
      open_embed_links
      select_h_select_option('.ci-embed-worker-options', value: 5)
      wait_for_element_load('.ci-submit-update-embed-workers')
      wait_expect(true) { !!page.find('.ci-updating-embed-workers-alert', text: /increasing the number/) }
      safe_click '.ci-submit-update-embed-workers'
      expect_notifier_content /updated number of workers/

      select_h_select_option('.ci-embed-worker-options', value: 4)
      wait_for_element_load('.ci-submit-update-embed-workers')
      wait_expect(true) { !!page.find('.ci-updating-embed-workers-alert', text: /reducing the number/) }
      safe_click '.ci-submit-update-embed-workers'
      expect_notifier_content /updated number of workers/

      select_h_select_option('.ci-embed-worker-options', value: 20, scroll_to_find: true)
      wait_for_element_load('.ci-submit-update-embed-workers')
      wait_expect(true) { !!page.find('.ci-updating-embed-workers-alert', text: /increasing the number/) }
      safe_click '.ci-submit-update-embed-workers'
      expect_notifier_content /can't update number of embed workers.\ninsufficient available objects/
    end

    context 'with tenant subscription is active' do
      before do
        set_up_tsub(plan_name: 'standard', period_type: 'monthly', group_version: 3, add_objects: 1000, status: 'cancelling', max_restriction_level: 'hard')
      end
      it_behaves_like 'toggle embed workers'
    end

    context 'with tenant subscription is defunct' do
      before do
        set_up_tsub(plan_name: 'standard', period_type: 'monthly', group_version: 3, add_objects: 1000, status: 'dunned', max_restriction_level: 'hard')
      end
      it 'don\'t access embed links' do
        safe_login admin, '/embed'
        expect(page).to have_selector('.dunned-reminder-wrapper')
      end
    end
  end

  # it 'create new link' do
  #   open_embed_links
  #   safe_click '.ci-new-embed-link'
  #   safe_click '.dropdown-dashboard .dropdown-menu .ci-dashboard'
  #   wait_expect(true) { page.has_css?('.ci-edit-embed-link-modal') }
  # end

  it 'edit existed link' do
    open_embed_links
    open_modal_by_click '.ci-edit-embed-link'
    safe_click '.ci-save-embed-link'
    expect_notifier_content /updated successfully/
  end

  it 'delete link' do
    open_embed_links
    safe_click '.ci-remove-embed-link'
    safe_click('button', text: 'Yes, delete')
    expect_notifier_content /deleted successfully/
  end
end
