# typed: false

require 'rails_helper'

describe 'data sources new', :js, stable: true do
  let(:admin) { get_test_admin }

  it 'can create new ds' do
    test_dbconfig = dbconfig_rails_test_env
    fill_ds_information('')

    wait_expect('connected successfully') do
      page.find('[data-ci="ci-test-message"]').text.downcase
    end

    safe_click('.ci-submit')

    ds = DataSource.last
    wait_expect(admin.tenant_id) { ds.reload.tenant_id }

    [:host, :user, :dbname, :password, :port].each do |k|
      expect(ds.dbconfig[k].to_s).to eq test_dbconfig[k].to_s
    end
  end

  context 'when user input malicious host on production' do
    before do
      Rails.env.stub(production?: true)
    end

    it 'raises host prevented error' do
      fill_ds_information('localhost')
      wait_for_element_load('[data-ci="ci-test-message"]')

      expect(page.find('[data-ci="ci-test-message"]').text).to match(/You are not allowed.*Holistics' local hosts.*tunnel connection/)
    end

    # it 'should raise url schemes error' do
    #   fill_ds_information('file://yasuo')
    #   wait_for_element_load('.ci-test-message')

    #   expect(page.find('.ci-test-message').text).to match(/Invalid URI scheme/)
    # end
  end

  context 'with databricks form' do
    before do
      FeatureToggle.toggle_global(DataSource::FT_DATABRICKS_ENABLED, true)
    end

    it 'can show databricks config fields' do
      safe_login(admin, '/data_sources/new')
      fill_text('.ci-name', 'new-ds')
      search_h_select('.ci-dbtype', text: 'Databricks')
      select_h_select_option('.ci-dbtype', value: 'databricks')
      expect(page).to have_css('.h-checkbox.checkbox-checked', text: 'Require SSL')
      expect(page).to have_css 'input[name="HTTP Path"]'
      expect(page).to have_css 'input[name="Catalog"]'

      safe_click('[data-ci="ci-ds-advanced-settings"]', text: 'Advanced Databricks Settings')
      expect(page.find('input[name="Availability Retry Timeout"]').value).to eq('90')

      safe_click('[data-ci="ci-ds-advanced-settings"]', text: 'Advanced Holistics Settings')
      safe_click('.h-checkbox', text: 'Query Timeout')
      expect(page).to have_css 'input[name="query-timeout"]'
    end
  end

  context 'with clickhouse form' do
    before do
      FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, true)
    end

    it 'has correct default port' do
      safe_login(admin, '/data_sources/new')
      fill_text('.ci-name', 'new-ds')
      search_h_select('.ci-dbtype', text: 'Clickhouse')
      select_h_select_option('.ci-dbtype', value: 'clickhouse')
      wait_for_element_load('.ci-port')
      expect(page.find('.ci-port').value).to eq('8123')
      # click enable canal
      safe_click('[data-ci="ci-ds-advanced-settings"]')
      safe_click('[data-ci="enable-canal"]')
      wait_for_element_load('[data-ci="canal-native-port"]')
      expect(page.find('[data-ci="canal-native-port"]').value).to eq '9000'
    end
  end

  context 'with Canal available' do
    before do
      FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, true)
    end

    describe 'Force enable by default' do
      context 'enable FT_FORCE_ENABLE_BY_DEFAULT' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_BY_DEFAULT, true)
          GlobalConfig.set(Canal::Constants::GC_STABLE_CANAL_DBTYPES, [DataSource::DBTYPE_POSTGRESQL])
        end

        it 'shows Canal always enabled for stable dbtype', otel: true do
          fill_ds_information('localhost', save_ds: false)

          safe_click('[data-ci="ci-ds-advanced-settings"]')
          wait_for_element_load('[data-ci="enable-canal"]')
          expect(page.find('[data-ci="enable-canal"]')[:class]).to include('checkbox-checked')
          expect(page.find('[data-ci="enable-canal"]')[:class]).to include('disabled')
          wait_expect('Fast Query Streaming Engine. Click to learn more. (NOTE: Canal is always enabled for new PostgreSQL data sources).') do
            reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
            page.find('.hui-tooltip-floating').text
          end
          wait_for_element_load('[data-ci="canal-connection-pooling"]')
          expect(page.find('[data-ci="canal-connection-pooling"]')[:class]).to include('checkbox-checked')

          expect(page.evaluate_script('H.globalConfigs.canalStableDbtypes[0] === "postgresql"')).to eq true
          # test_connection must running Canal

          otel_reset
          safe_click('.ci-test')
          wait_for_element_load('[data-ci="ci-test-message"]')
          expect(otel_finished_spans.filter { |s| s.name == 'Canal::QueryApi#call_api(post /query)' }.length).to eq 1
        end

        it 'allow toggle Canal for non-stable dbtype' do
          safe_login(admin, '/data_sources/new')
          fill_text('.ci-name', 'new-ds')
          search_h_select('.ci-dbtype', text: 'MySQL')
          select_h_select_option('.ci-dbtype', value: 'mysql')

          safe_click('[data-ci="ci-ds-advanced-settings"]')
          wait_for_element_load('[data-ci="enable-canal"]')
          expect(page.find('[data-ci="enable-canal"]')[:class]).not_to include('disabled')
          wait_expect('Fast Query Streaming Engine. Click to learn more') do
            reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
            page.find('.hui-tooltip-floating').text
          end
        end

        describe 'Clickhouse form' do
          before do
            GlobalConfig.set(Canal::Constants::GC_STABLE_CANAL_DBTYPES, [DataSource::DBTYPE_CLICKHOURSE])
          end

          it 'shows native port config along side http port' do
            safe_login(admin, '/data_sources/new')
            fill_text('.ci-name', 'new-ds')
            search_h_select('.ci-dbtype', text: 'Clickhouse')
            select_h_select_option('.ci-dbtype', value: 'clickhouse')
            wait_for_element_load('.ci-port')
            expect(page.find('.ci-port').value).to eq('8123')

            expect(page.has_no_css?('[data-ci="ci-ds-advanced-settings-content"]', { wait: 0 })).to eq true

            wait_for_element_load('[data-ci="canal-native-port"]')
            expect(page.find('[data-ci="canal-native-port"]').value).to eq '9000'
          end
        end
      end

      context 'disable FT_FORCE_ENABLE_BY_DEFAULT' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_BY_DEFAULT, false)
          GlobalConfig.set(Canal::Constants::GC_STABLE_CANAL_DBTYPES, [DataSource::DBTYPE_POSTGRESQL])
        end

        it 'allow toggle Canal' do
          safe_login(admin, '/data_sources/new')
          fill_text('.ci-name', 'new-ds')
          search_h_select('.ci-dbtype', text: 'Postgres')
          select_h_select_option('.ci-dbtype', value: 'postgresql')

          safe_click('[data-ci="ci-ds-advanced-settings"]')
          wait_for_element_load('[data-ci="enable-canal"]')
          expect(page.find('[data-ci="enable-canal"]')[:class]).not_to include('disabled')
          wait_expect('Fast Query Streaming Engine. Click to learn more') do
            reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
            page.find('.hui-tooltip-floating').text
          end
        end
      end
    end
  end
end

def fill_ds_information(host_name, save_ds: true)
  safe_login(admin, '/data_sources/new')
  test_dbconfig = dbconfig_rails_test_env
  fill_text('.ci-name', 'new-ds')
  select_h_select_option('.ci-dbtype', value: 'postgresql')

  fill_text('.ci-host', host_name.presence || test_dbconfig[:host])
  fill_text('.ci-port', test_dbconfig[:port])
  fill_text('.ci-username', test_dbconfig[:user])
  fill_text('.ci-password', test_dbconfig[:password])
  fill_text('.ci-dbname', test_dbconfig[:dbname])

  safe_click('.ci-test') if save_ds
end
