# frozen_string_literal: true
# typed: false

require 'rails_helper'

describe 'help center', :js, stable: true do
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:header_help_center_btn) { page.find('.header-help-center') }
  let(:sidebar_selector) { '.help-center-sidebar' }
  let(:sidebar_sliding_duration) { 0.15 + 0.1 }

  before do
    safe_login(admin, '/home')
  end

  it 'shows when user clicks `Help Center` icon in header' do
    header_help_center_btn.click
    sleep(sidebar_sliding_duration)

    page.find(sidebar_selector)
  end

  context 'when sidebar shown' do
    let(:sidebar) { page.find(sidebar_selector) }

    before do
      header_help_center_btn.click
      sleep(sidebar_sliding_duration)
      sidebar
    end

    describe 'hiding logic' do
      it 'hides when user clicks icon in header' do
        header_help_center_btn.click
        sleep(sidebar_sliding_duration)

        expect { page.find(sidebar_selector) }.to raise_error Capybara::ElementNotFound
      end

      it 'hides when user clicks close button' do
        sidebar.click_button('Close')
        sleep(sidebar_sliding_duration)

        expect { page.find(sidebar_selector) }.to raise_error Capybara::ElementNotFound
      end
    end

    describe 'inside sidebar' do
      describe 'styles' do
        it 'has dark background and light foreground' do
          expect(sidebar.style('background-color', 'color')).to match(
            'background-color' => 'rgba(19, 21, 26, 1)',
            'color' => 'rgba(176, 182, 191, 1)',
          )
        end
      end

      describe 'inside sidebar content' do
        before do
          # Async component
          wait_for { sidebar.find('.help-center-content__body') }
        end

        activable_tutorials_tab_labels =
          [
            '4-step Setup',
            'Create data model and dataset',
            'Build a report',
            'Build a dashboard',
            'Find a report / dashboard',
            'Interact with data',
            'Share data with others',
          ]

        # TODO: Refactor into unit test
        # shared_examples '`Tutorials` section' do |activable_tutorials_tab_labels|
        #   let(:tutorials_modal_selector) { '.onboarding-tutorials-modal' }
        #   let(:tutorials_modal) { page.find(tutorials_modal_selector) }

        #   def assert_tutorials_tab_active(label)
        #     tabs = tutorials_modal.find('.tutorials-body__vertical-tabs')
        #     tab_items = tabs.find_all('.vertical-tab--child')

        #     tab_items.each_with_index do |tab_item, _index|
        #       styles = tab_item.style(
        #         'background-color', 'font-weight',
        #       )

        #       if tab_item.has_content?(label)
        #         expect(styles).to match(
        #           'background-color' => 'rgba(232, 242, 253, 1)',
        #           'font-weight' => '600',
        #         )
        #       else
        #         expect(styles).to match(
        #           'background-color' => 'rgba(0, 0, 0, 0)',
        #           'font-weight' => '400',
        #         )
        #       end
        #     end
        #   end

        #   it 'has correct header text' do
        #     text = sidebar.find('span', text: 'Tutorials')

        #     expect(text.style('font-weight')).to match(
        #       'font-weight' => '600',
        #     )
        #   end

        #   see_tutorials_button_label = 'See all'
        #   it "has `#{see_tutorials_button_label}` button" do
        #     sidebar.find('[role=button]', text: see_tutorials_button_label)
        #   end

        #   context "when click `#{see_tutorials_button_label}` button" do
        #     before do
        #       sidebar.find('[role=button]', text: see_tutorials_button_label).click
        #     end

        #     it 'shows onboarding tutorials modal' do
        #       wait_for_element_load(tutorials_modal_selector)
        #     end

        #     context 'when onboarding tutorials modal shown' do
        #       before do
        #         wait_for_element_load(tutorials_modal_selector)
        #       end

        #       it "has `#{activable_tutorials_tab_labels[0]}` tab active" do
        #         assert_tutorials_tab_active(activable_tutorials_tab_labels[0])
        #       end
        #     end
        #   end

        #   describe 'carousel' do
        #     it 'has all activable tabs as carousel items' do
        #       # Since this test only validates structure of elements, we need to find all (hidden) elements
        #       Capybara.ignore_hidden_elements = false

        #       activable_tutorials_tab_labels.each do |label|
        #         item = sidebar.find('[role=button]') do |button|
        #           button.has_content?(label)
        #         end

        #         thumbnail = item.find('.help-center-tutorials-carousel__video-thumbnail')
        #         expect(thumbnail.style('width', 'height', 'border-width', 'border-color', 'border-radius')).to match(
        #           'width' => '192px',
        #           'height' => '108px',
        #           'border-width' => '1px',
        #           'border-color' => 'rgb(232, 232, 232)',
        #           'border-radius' => '2px',
        #         )

        #         img = thumbnail.find('img')
        #         expect(img['alt']).to eq(label)
        #       end
        #     end

        #     describe 'navigating logic' do
        #       # Assert item correctly shown and slid.
        #       # Return `true` if the last item is also being shown.
        #       def assert_item_shown_and_slid(label)
        #         item = sidebar.find('[role=button]') { |button| button.has_content?(label) }

        #         # x + left-padding
        #         container_x = sidebar.rect.x + 20
        #         # width - horizontal paddings
        #         container_width = sidebar.rect.width - (20 * 2)
        #         item_x = item.rect.x
        #         delta_x = item_x - container_x

        #         # Current item is shown and slid regularly
        #         if delta_x == 0
        #           assert(true)

        #           false
        #         # Current item is shown alongside the last item
        #         elsif delta_x < container_width
        #           item_width = 192
        #           item_gap = 8
        #           # Because there could be more than 1 last item shown, we have following equation:
        #           # item_width * N + item_gap * (N - 1) = container_width - delta_x
        #           # <=> N = (container_width - delta_x + item_gap) / (item_width + item_gap)
        #           expect((container_width - delta_x + item_gap) % (item_width + item_gap)).to eq(0)

        #           true
        #         # Current item is not shown
        #         else
        #           assert(false)
        #         end
        #       end

        #       def navigate(button, sliding_duration = 0.5)
        #         button.click
        #         sleep(sliding_duration)
        #       end

        #       next_button_title = 'Next'
        #       prev_button_title = 'Prev'
        #       it "conditionallies show and able to slide to correct item via #{next_button_title}/#{prev_button_title} buttons" do
        #         carousel = sidebar.find('.help-center-tutorials-carousel')
        #         carousel.hover

        #         # Next
        #         activable_tutorials_tab_labels.each_with_index do |label, index|
        #           last_item_shown = assert_item_shown_and_slid(label)

        #           if index < activable_tutorials_tab_labels.size - 1 && last_item_shown == false
        #             navigate(carousel.find_button(next_button_title))
        #           else
        #             expect { carousel.find_button(next_button_title) }.to raise_error Capybara::ElementNotFound
        #           end
        #         end

        #         # TODO: Handle the case where we are checking some last items but `Previous` is binded to another previous item
        #         # Previous (after navigating to the end via `Next`)
        #         # activable_tutorials_tab_labels.reverse.each_with_index do |label, index|
        #         #   last_item_shown = assert_item_shown_and_slid(label)

        #         #   next if last_item_shown

        #         #   if index < activable_tutorials_tab_labels.size - 1
        #         #     navigate(carousel.find_button(prev_button_title))
        #         #   else
        #         #     expect { carousel.find_button(prev_button_title) }.to raise_error Capybara::ElementNotFound
        #         #   end
        #         # end
        #       end
        #     end
        #   end
        # end

        # describe 'should show `Tutorials` section when app version is 3.0' do
        #   include_examples '`Tutorials` section', activable_tutorials_tab_labels
        # end

        describe '`Recommendation 🌟` section' do
          it 'has correct header text' do
            text = sidebar.find('div', exact_text: 'Recommendation 🌟')

            expect(text.style('font-weight')).to match(
              'font-weight' => '500',
            )
          end

          it 'has link to Holistics docs' do
            ftg_info = FeatureToggleGroup.group_info_for_tenant(tenant.id)

            sidebar.find_link('Read Holistics Docs', href: ftg_info[:docs_url])
          end

          info_by_version = {
            '3.0' => {
              demoUrl: 'https://demo.holistics.io/demo',
            },
            '4.0' => {
              demoUrl: 'https://demo4.holistics.io/demo',
            },
          }
          it 'has button to open Demo Environment modals' do
            # TODO: Init correct versions to tests all possible cases
            ['3.0'].each do |version|
              info = info_by_version[version]

              trigger = sidebar.find('[role=button]', text: "View Demo Dashboard #{version}")
              trigger.click

              modal_selector = '.demo-environment-modal'
              wait_for_element_load(modal_selector)
              modal = page.find(modal_selector)

              title = "Play with our #{version} Demo environment"
              description = 'Experience Holistics without connecting to your database! Enjoy the full range of app functionalities at your fingertips.'
              note = 'Notes: All objects not created by Holistics team will be removed every Monday at 12:00 AM (UTC).'

              expect(modal.has_content?(title)).to be_truthy
              expect(modal.has_content?(description)).to be_truthy
              expect(modal.has_content?(note)).to be_truthy

              expect(modal).to have_link('Open Demo Site', href: info[:demoUrl])

              modal.find('.close-btn').click
            end
          end
        end

        describe '`Additional Resources` section' do
          it 'has correct header text' do
            text = sidebar.find('div', exact_text: 'Additional Resources')

            expect(text.style('font-weight')).to match(
              'font-weight' => '500',
            )
          end

          it 'has correct external links' do
            label_to_link =
              {
                'Learn Tips' => 'https://community.holistics.io/c/tips-hacks/22',
                'Ask Community' => 'https://community.holistics.io',
                'Suggest a Feature' => 'https://community.holistics.io/c/feature-suggestions/13',
              }

            label_to_link.each do |label, link|
              sidebar.find_link(label, href: link)
            end
          end

          it 'has button to open `In-app Feedback` form' do
            button = sidebar.find('[role=button]', text: 'Send Feedback')

            form_window = window_opened_by { button.click }

            assert_jotform_opened(
              id: '203308784415456',
              window: form_window,
              user: admin,
              additional_data: { feature: 'General Feedback' },
            )
          end

          it 'has button to open `Submit Support Ticket` form' do
            button = sidebar.find('[role=button]', text: 'Contact Support')

            form_window = window_opened_by { button.click }

            assert_support_jotform_opened(window: form_window, user: admin)
          end
        end

        describe 'footer' do
          it 'shows correct region name' do
            expect(sidebar.has_content?('Region: Asia-Pacific')).to be_truthy
          end
        end
      end
    end
  end
end
