# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Data Explorer with Advanced analytics functions', :js do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }

  before do
    ThreadContext.set(:current_user, admin)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global('data_models:explore_controls', true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global(Viz::Data::V3::VizModelTransformers::Pop::FT_POP_ENABLED, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_IMPLICIT_PLACEHOLDER_DATA_TYPE, true)
    FeatureToggle.toggle_global(Viz::Data::V3::RegressionModel::FT_REGRESSION_ENABLED, true)
    FeatureToggle.toggle_global(Viz::Data::V3::ReferenceModel::FT_REFERENCE_ENABLED, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_PIVOT_V2, true)
  end

  context 'with trend and reference line' do
    let(:query_model) do
      sql = <<~SQL
        select
          i,
          now () + (ceil(random() * 90) || ' day')::interval as order_time,
          ('{"Male", "Female"}'::text[])[ceil(random() * 2)] as gender,
          ('{"Kotex", "Laurier", "<PERSON>", "Heineken", "Tiger"}'::text[])[ceil(random() * 5)] as product,
          ('{"Feather", "Fresh & Free", "Micro Thin", "Thunderstorm", "No Regret", "BFF"}'::text[])[ceil(random() * 6)] as model,
          round((random() * 99)::numeric, 2) as price,
          ceil(random() * 5) as rating
        from (select generate_series(1, 100, 1) as i) s
      SQL

      create_query_data_model(admin, ds, 'new_sql_model', sql)
    end

    let(:dataset) do
      dataset = create(:data_set, root_model_id: nil)
      dataset.data_models << query_model
      dataset
    end

    let(:viz_setting) do
      create(
        :viz_setting,
        viz_type: 'column_chart',
        source: dataset,
        fields: {
          series: {
            custom_label: nil,
            format: nil,
            path_hash: nil,
          },
          x_axis: {
            custom_label: nil,
            format: {
              sub_type: 'mmm yyyy',
              type: 'date',
            },
            path_hash: {
              field_name: 'order_time',
              model_id: 1,
            },
            transformation: 'datetrunc month',
            type: 'datetime',
            uuid: 'e15764b4-23f0-4482-a42c-c5215bf303a8',
          },
          y_axes: [
            {
              columns: [
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: nil,
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: 'a51a799c-7d15-4a63-a007-dd19a4c208c0',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Trend line 1',
                  analytic: {
                    regression: 'linear',
                    type: 'trend',
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: '22974098-ebde-471c-be48-6942861e7506',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Trend line 2',
                  analytic: {
                    regression: 'exponential',
                    type: 'trend',
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: '15d4732-f350-409c-93f8-5e6ca70f8379',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Ref line 1',
                  analytic: {
                    reference: 'constant',
                    type: 'reference',
                    reference_value: 1000,
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'THIS_IS_NOT_A_REAL_FIELD',
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: 'bc7e7047-8021-4ad2-9e15-f3f723ef74bf',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Ref line 2',
                  analytic: {
                    reference: 'avg',
                    type: 'reference',
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: '789df8f5-8698-4808-a781-51ab31a96d6e',
                },
              ],
              label: 'Y Axis',
            },
          ],
          tooltips: [],
        },
        settings: {
          sort: {
            isAscending: false,
            option: {
              color: 'black',
              idx: 0,
              label: 'X-Axis',
              type: 'xaxis',
            },
          },
        },
        filters: [],
      )
    end

    let(:viz_setting_pop) do
      viz_setting.settings[:pop_settings] =
        {
          condition: {
            operator: 'between',
            values: [],
          },
          field: {
            path_hash: {
              field_name: 'order_time',
              model_id: 1,
            },
          },
          offset: 1,
          period: 'month',
          show_growth_rate: true,
          type: 'relative',
        }
      viz_setting.save
      viz_setting
    end

    let(:vs_with_legend) do
      create(
        :viz_setting,
        viz_type: 'column_chart',
        source: dataset,
        fields: {
          series: {
            custom_label: nil,
            format: {
              type: 'string',
            },
            path_hash: {
              field_name: 'gender',
              model_id: 1,
            },
          },
          x_axis: {
            custom_label: nil,
            format: {
              sub_type: 'mmm yyyy',
              type: 'date',
            },
            path_hash: {
              field_name: 'order_time',
              model_id: 1,
            },
            transformation: 'datetrunc month',
            type: 'datetime',
            uuid: 'e15764b4-23f0-4482-a42c-c5215bf303a8',
          },
          y_axes: [
            {
              columns: [
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: nil,
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: 'a51a799c-7d15-4a63-a007-dd19a4c208c0',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Ref line 1',
                  analytic: {
                    reference: 'constant',
                    type: 'reference',
                    reference_value: 1000,
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'THIS_IS_NOT_A_REAL_FIELD',
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: 'bc7e7047-8021-4ad2-9e15-f3f723ef74bf',
                },
                {
                  aggregation: 'sum',
                  color: 'auto',
                  custom_label: 'Ref line 2',
                  analytic: {
                    reference: 'avg',
                    type: 'reference',
                  },
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: 1,
                  },
                  series_settings: {
                    color: 'auto',
                    palette_id: -2,
                    series_hash: {},
                    series_type: 'auto',
                  },
                  type: 'number',
                  uuid: '789df8f5-8698-4808-a781-51ab31a96d6e',
                },
              ],
              label: 'Y Axis',
            },
          ],
          tooltips: [],
        },
        settings: {
          sort: {
            isAscending: false,
            option: {
              color: 'black',
              idx: 0,
              label: 'X-Axis',
              type: 'xaxis',
            },
          },
        },
        filters: [],
      )
    end

    it 'shows measure, trend lines and ref lines in tooltip correctly' do
      safe_login admin, "#{data_set_path(dataset)}/#{viz_setting.hashid}"

      safe_click '.ci-explorer-control-get-results'

      wait_for_viz_load

      expect_highcharts_tooltip(/^(?=.*Sum of Price:)(?=.*Trend line 1:)(?=.*Trend line 2:)(?=.*Ref line 1:)(?=.*Ref line 2:).*$/)
    end

    it 'shows pop of trend lines, ref lines in tooltip correctly' do
      safe_login admin, "#{data_set_path(dataset)}/#{viz_setting_pop.hashid}"

      safe_click '.ci-explorer-control-get-results'

      wait_for_viz_load

      tooltip_regex = [
        /(?=.*Sum of Price:)(?=.*Sum of Price \(Previous 1 month\):)/,
        /(?=.*Trend line 1:)(?=.*Trend line 1 \(Previous 1 month\):)/,
        /(?=.*Trend line 2:)(?=.*Trend line 2 \(Previous 1 month\):)/,
        /(?=.*Ref line 1:)(?=.*Ref line 1 \(Previous 1 month\):)/,
        /(?=.*Ref line 2:)(?=.*Ref line 2 \(Previous 1 month\):)/,
      ].join('')

      expect_highcharts_tooltip(/^#{tooltip_regex}.*$/)
    end

    it 'shows only 1 constant reference line (despite legend)' do
      safe_login admin, "#{data_set_path(dataset)}/#{vs_with_legend.hashid}"

      safe_click '.ci-explorer-control-get-results'

      wait_for_viz_load

      tooltip_regex = [
        /(?=.*Sum of Price:)(?=.*Female Sum of Price:)(?=.*Male Sum of Price:)/,
        /(?=.*Ref line 1:)/,
        /(?=.*Ref line 2:)(?=.*Female Ref line 2:)(?=.*Male Ref line 2:)/,
      ].join('')

      expect_highcharts_tooltip(/^#{tooltip_regex}.*$/)
    end

    context 'with aql dataset' do
      include_context 'data_explore_ctx'
      include_context 'aml_studio_dataset'

      let(:viz_setting_aql) do
        create(
          :viz_setting,
          viz_type: 'column_chart',
          source: aml_ecommerce_aql_data_set_record,
          fields: {
            series: {
              custom_label: nil,
              format: nil,
              path_hash: nil,
            },
            x_axis: {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_date',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            y_axes: [
              {
                columns: [
                  {
                    aggregation: 'sum',
                    color: 'auto',
                    custom_label: nil,
                    format: {
                      format: {
                        pattern: 'inherited',
                      },
                      type: 'number',
                    },
                    path_hash: {
                      field_name: 'quantity',
                      model_id: 'data_modeling_orders',
                    },
                    series_settings: {
                      color: 'auto',
                      palette_id: -2,
                      series_hash: {},
                      series_type: 'auto',
                    },
                    type: 'number',
                    uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
                  },
                  {
                    aggregation: 'sum',
                    color: 'auto',
                    custom_label: 'Trend line 1',
                    analytic: {
                      regression: 'linear',
                      type: 'trend',
                    },
                    format: {
                      format: {
                        pattern: 'inherited',
                      },
                      type: 'number',
                    },
                    path_hash: {
                      field_name: 'quantity',
                      model_id: 'data_modeling_orders',
                    },
                    series_settings: {
                      color: 'auto',
                      palette_id: -2,
                      series_hash: {},
                      series_type: 'auto',
                    },
                    type: 'number',
                    uuid: 'bc34a79b-eb67-43fb-8ead-8577bbffc864',
                  },
                  {
                    aggregation: 'sum',
                    color: 'auto',
                    custom_label: 'Trend line 2',
                    analytic: {
                      regression: 'exponential',
                      type: 'trend',
                    },
                    format: {
                      format: {
                        pattern: 'inherited',
                      },
                      type: 'number',
                    },
                    path_hash: {
                      field_name: 'quantity',
                      model_id: 'data_modeling_orders',
                    },
                    series_settings: {
                      color: 'auto',
                      palette_id: -2,
                      series_hash: {},
                      series_type: 'auto',
                    },
                    type: 'number',
                    uuid: '03dcc8a2-5a12-4e51-94f9-a1dbd2de97ce',
                  },
                  {
                    aggregation: 'sum',
                    color: 'auto',
                    custom_label: 'Ref line 1',
                    analytic: {
                      reference: 'constant',
                      type: 'reference',
                      reference_value: 1000,
                    },
                    format: {
                      format: {
                        pattern: 'inherited',
                      },
                      type: 'number',
                    },
                    path_hash: {
                      field_name: 'THIS_IS_NOT_A_REAL_FIELD',
                    },
                    series_settings: {
                      color: 'auto',
                      palette_id: -2,
                      series_hash: {},
                      series_type: 'auto',
                    },
                    type: 'number',
                    uuid: 'a643fbd6-8557-4ae2-b483-b1056caf7715',
                  },
                  {
                    aggregation: 'sum',
                    color: 'auto',
                    custom_label: 'Ref line 2',
                    analytic: {
                      reference: 'sum',
                      type: 'reference',
                    },
                    format: {
                      format: {
                        pattern: 'inherited',
                      },
                      type: 'number',
                    },
                    path_hash: {
                      field_name: 'quantity',
                      model_id: 'data_modeling_orders',
                    },
                    series_settings: {
                      color: 'auto',
                      palette_id: -2,
                      series_hash: {},
                      series_type: 'auto',
                    },
                    type: 'number',
                    uuid: 'f4939845-ee54-4cc6-911a-ba1041788c99',
                  },
                ],
                label: 'Y Axis',
              },
            ],
            tooltips: [],
          },
          settings: {
            sort: {
              isAscending: false,
              option: {
                color: 'black',
                idx: 0,
                label: 'X-Axis',
                type: 'xaxis',
              },
            },
          },
          filters: [],
        )
      end

      let(:viz_setting_aql_pop) do
        viz_setting_aql.settings[:pop_settings] =
          {
            condition: {
              operator: 'between',
              values: [],
            },
            field: {
              path_hash: {
                field_name: 'created_date',
                model_id: 'data_modeling_orders',
              },
              type: 'date',
              uuid: '43e79016-eea0-49a8-ab4d-f4c48e2983d2',
            },
            offset: 1,
            period: 'month',
            show_growth_rate: true,
            type: 'relative',
          }

        viz_setting_aql.save
        viz_setting_aql
      end

      before do
        FeatureToggle.toggle_global(DataModel::FT_AQL, true)

        DataSource.first.synchronize_schema
        connector = Connectors.from_ds(get_test_ds)

        insert_orders = <<~SQL
          truncate data_modeling.orders;
          insert into data_modeling.orders values
          (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40'),
          (2, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-02-16 20:38:40'),
          (3, 1, 1, 20, 500, 'cancelled', TIMESTAMP '2021-02-16 20:38:40'),
          (4, 1, 1, 20, 500, 'cancelled', TIMESTAMP '2021-03-16 20:38:40'),
          (5, 1, 2, 20, 2, 'delivered', TIMESTAMP '2021-04-16 20:38:40'),
          (6, 1, 2, 0, 600, 'cancelled', TIMESTAMP '2021-04-16 20:38:40'),
          (7, 1, 3, 0, 2, 'delivered', TIMESTAMP '2021-04-16 20:38:40')
          ;
        SQL

        connector.exec_sql(insert_orders)
      end

      it 'shows measure with its trend lines and ref lines in tooltip correctly' do
        safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

        safe_click '.ci-explorer-control-get-results'

        wait_for_viz_load

        expect_highcharts_tooltip(/^(?=.*Sum of Quantity:)(?=.*Trend line 1:)(?=.*Trend line 2:)(?=.*Ref line 1:)(?=.*Ref line 2:).*$/)
      end

      it 'shows pop of trend lines, ref lines in tooltip correctly' do
        safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql_pop.hashid}"

        safe_click '.ci-explorer-control-get-results'

        wait_for_viz_load

        tooltip_regex = [
          /(?=.*Sum of Quantity:)(?=.*Sum of Quantity \(Previous 1 month\):)/,
          /(?=.*Trend line 1:)(?=.*Trend line 1 \(Previous 1 month\):)/,
          /(?=.*Trend line 2:)(?=.*Trend line 2 \(Previous 1 month\):)/,
          /(?=.*Ref line 1:)(?=.*Ref line 1 \(Previous 1 month\):)/,
          /(?=.*Ref line 2:)(?=.*Ref line 2 \(Previous 1 month\):)/,
        ].join('')

        expect_highcharts_tooltip(/^#{tooltip_regex}.*$/)
      end

      context 'with break down and merge legend' do
        let(:vs_legend) do
          create(
            :viz_setting,
            viz_type: 'column_chart',
            source: aml_ecommerce_aql_data_set_record,
            fields: {
              series: {
                custom_label: nil,
                format: {
                  type: 'string',
                  format: {},
                },
                path_hash: {
                  field_name: 'status',
                  model_id: 'data_modeling_orders',
                },
                type: 'text',
                transformation: nil,
                uuid: '02e85a83-ad4a-4cad-b607-b429b4926f14',
              },
              x_axis: {
                custom_label: nil,
                format: {
                  sub_type: 'mmm yyyy',
                  type: 'date',
                },
                path_hash: {
                  field_name: 'created_date',
                  model_id: 'data_modeling_orders',
                },
                transformation: 'datetrunc month',
                type: 'date',
                uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
              },
              y_axes: [
                {
                  columns: [
                    {
                      aggregation: 'sum',
                      color: 'auto',
                      custom_label: 'Trend line 1',
                      analytic: {
                        regression: 'linear',
                        type: 'trend',
                      },
                      format: {
                        format: {
                          pattern: 'inherited',
                        },
                        type: 'number',
                      },
                      path_hash: {
                        field_name: 'quantity',
                        model_id: 'data_modeling_orders',
                      },
                      series_settings: {
                        color: 'auto',
                        palette_id: -2,
                        series_hash: {},
                        series_type: 'auto',
                      },
                      type: 'number',
                      uuid: 'bc34a79b-eb67-43fb-8ead-8577bbffc864',
                    },
                    {
                      aggregation: 'sum',
                      color: 'auto',
                      custom_label: 'sum of quantity',
                      format: {
                        format: {
                          pattern: 'inherited',
                        },
                        type: 'number',
                      },
                      path_hash: {
                        field_name: 'quantity',
                        model_id: 'data_modeling_orders',
                      },
                      series_settings: {
                        color: 'auto',
                        palette_id: -2,
                        series_hash: {},
                        series_type: 'auto',
                      },
                      type: 'number',
                      uuid: 'bc34a79b-eb67-43fb-8ead-8577bbffc864',
                    },
                  ],
                  label: 'Y Axis',
                },
              ],
              tooltips: [],
            },
            settings: {
              sort: {
                isAscending: false,
                option: {
                  color: 'black',
                  idx: 0,
                  label: 'X-Axis',
                  type: 'xaxis',
                },
              },
            },
            filters: [],
          )
        end

        def expect_custom_legend_settings(break_by_legend:, merge_legend:)
          wait_expect(true) do
            expect(page).to have_css("[data-hui-comp=\"switch\"][data-hui-checked=\"#{break_by_legend}\"]",
                                     exact_text: 'Break down by legend', wait: 0.5,)
            expect(page).to have_css("[data-hui-comp=\"switch\"][data-hui-checked=\"#{merge_legend}\"]",
                                     exact_text: 'Merge into one line', wait: 0.5,)
          end
        end

        it 'works correctly' do
          safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{vs_legend.hashid}"

          safe_click '.ci-explorer-control-get-results'
          wait_for_viz_load
          expect_highcharts_tooltip(/^(?=.*cancelled Trend line 1:)(?=.*cancelled sum of quantity:).*$/)

          safe_click '[data-ci="ci-brush-settings"]'
          expect_custom_legend_settings(break_by_legend: true, merge_legend: false)

          safe_click('[data-hui-comp="switch"]', text: 'Merge into one line') # toggle on merge legend
          expect_custom_legend_settings(break_by_legend: true, merge_legend: true)

          safe_click '.ci-explorer-control-get-results'
          wait_for_viz_load
          expect_highcharts_tooltip('Mar 2021 Trend line 1: 461.2669791780225 cancelled sum of quantity: 500')

          safe_click '[data-ci="ci-brush-settings"]'
          safe_click('[data-hui-comp="switch"]', text: 'Break down by legend') # toggle off break by legend
          expect_custom_legend_settings(break_by_legend: false, merge_legend: false)

          safe_click '.ci-explorer-control-get-results'
          wait_for_viz_load
          expect_highcharts_tooltip('Mar 2021 Trend line 1: 707.8660543436662 cancelled sum of quantity: 500')
        end

        context 'with series settings' do
          let(:vs_with_break_by_legend) do
            new_vs_legend = vs_legend.dup
            new_vs_legend.viz_type = 'line_chart'
            new_vs_legend.fields[:y_axes][0][:columns] = [vs_legend.fields[:y_axes][0][:columns][1]]
            new_vs_legend.fields[:y_axes][0][:columns][0][:break_by_legend] = false
            new_vs_legend.save!
            new_vs_legend
          end

          it 'uses whole series settings instead of series hash (per-legend) settings' do
            safe_login admin,
                       "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{vs_with_break_by_legend.hashid}"

            safe_click '.ci-explorer-control-get-results'
            wait_for_viz_load

            safe_click '[data-ci="ci-brush-settings"]'
            select_h_select_option('[data-ci="ci-series_style"]', label: 'Dotted')
            select_h_select_option('[data-ci="ci-series_interpolation"]', label: 'Smooth')

            safe_click '[data-ci="ci-color-picker"]'
            page.all('.color').first.click

            wait_for_viz_load

            expected_series_settings = {
              'color' => '#255DD4',
              'palette_id' => -2,
              'series_hash' => { 'cancelled' => { 'color' => 'auto', 'hidden' => false, 'series_style' => 'solid', 'series_interpolation' => 'linear' }, 'delivered' => { 'color' => 'auto', 'hidden' => false, 'series_style' => 'solid', 'series_interpolation' => 'linear' } },
              'series_type' => 'auto', 'series_style' => 'dotted', 'series_interpolation' => 'smooth',
            }

            wait_expect(expected_series_settings) do
              vs_result = VizSetting.last
              vs_result.fields[:y_axes][0][:columns][0][:series_settings]
            end
          end
        end
      end
    end
  end
end
