# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Admin manages Feature Toggles', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:tsub) { create(:tenant_subscription, status: 'active', tenant: tenant) }

  let!(:product_ft) { create(:feature_toggle, key: 'product_ft', used_for: 'product') }
  let!(:billing_ft) { create(:feature_toggle, key: 'billing_ft', used_for: 'billing') }

  before do
    admin.update(email: User::TEST_SUPER_ADMIN_EMAIL) # Make super admin
    qlogin(admin, '/admin')
  end

  it 'updates used_for from product to billing correctly' do
    visit("/admin/feature_toggles/#{product_ft.id}/edit")

    used_for_select_element = find('#feature_toggle_used_for')

    expect(used_for_select_element.value).to eql('product')

    select_dropdown(used_for_select_element, 'billing')
    click_button('Update Feature toggle')

    expect(page).to have_content('Feature toggle was successfully updated.')
  end

  it 'updates used_for from billing to product correctly' do
    visit("/admin/feature_toggles/#{billing_ft.id}/edit")

    used_for_select_element = find('#feature_toggle_used_for')

    expect(used_for_select_element.value).to eql('billing')

    select_dropdown(used_for_select_element, 'product')
    click_button('Update Feature toggle')

    expect(page).to have_content('Feature toggle was successfully updated.')
  end
end
