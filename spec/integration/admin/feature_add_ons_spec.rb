# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Admin manage Feature Add-ons', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:tsub) { create(:tenant_subscription, status: 'active', tenant: tenant) }
  let!(:ft) { create(:feature_toggle, key: 'ft_key', toggle_mode: 'disabled', used_for: 'billing') }

  before do
    admin.update(email: User::TEST_SUPER_ADMIN_EMAIL) # Make super admin
    qlogin(admin, '/admin')
  end

  it 'adds a Feature Add-on to a Tenant Subscription and toggle Feature Toggles' do
    expect(ft.active_for(tenant.id)).to be(false)

    visit("/admin/tenant_subscriptions/#{tsub.id}/edit")
    click_link('New Add-on')

    select_dropdown(find('#tenant_subscription_feature_add_ons_attributes_0_feature_toggle_id'), ft.id)
    select_dropdown(find('#tenant_subscription_feature_add_ons_attributes_0_desired_toggle_mode_input'), 'enabled')
    click_button('Update Tenant subscription')

    expect(page).to have_content('Tenant subscription was successfully updated.')
    expect(tsub.reload.feature_add_ons.length).to be(1)
  end
end
