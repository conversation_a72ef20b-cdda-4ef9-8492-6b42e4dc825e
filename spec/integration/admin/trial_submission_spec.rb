# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Admin::TrialSubmissionsController, js: true, stable: true do
  let(:admin) { get_test_admin }
  before do
    admin.update(role: User::ROLE_GROWTH_ADMIN)
    sign_in admin
  end

  describe "GET index" do
    let!(:tenant1) { FactoryBot.create :tenant, name: 'ahihi' }
    let!(:trial1) { create :trial_submission, tenant: tenant1 }
    let!(:tenant2) { FactoryBot.create :tenant, name: 'bhuhu' }
    let!(:trial2) { create :trial_submission, tenant: tenant2 }

    it 'lists all' do
      qlogin(admin, '/admin/trial_submissions')
      tenant_names = page.all('#index_table_trial_submissions tbody .col.col-tenant').map(&:text)
      expect(tenant_names).to match_array([tenant1.name, tenant2.name])
    end

    context 'with tenant_name filter param' do
      it 'filters correctly' do
        qlogin(admin, '/admin/trial_submissions?q[tenant_name_cont]=hu')
        tenant_names = page.all('#index_table_trial_submissions tbody .col.col-tenant').map(&:text)
        expect(tenant_names).to match_array([tenant2.name])
      end
    end
  end

  describe 'GET new' do
    it 'returns http success' do
      qlogin(admin, '/admin/trial_submissions/new')
      expect(page.find('h2[id="page_title"]').text).to eq 'New Trial Submission'
    end
  end

  describe 'Pass url params to input' do
    let!(:tenant1) { FactoryBot.create(:tenant, name: 'ahihi') }
    let!(:trial1) { create(:trial_submission, tenant: tenant1) }
    let(:new_path) do
      '/admin/trial_submissions/new?email=<EMAIL>&first_name=Huy&last_name=Nguyen&company_name=Holistics'
    end
    let(:edit_path) do
      "/admin/trial_submissions/#{trial1.id}/edit?email=<EMAIL>&first_name=Huy&last_name=Nguyen&company_name=Holistics"
    end

    it 'when add new trial' do
      qlogin(admin, new_path)

      expect(page.find('input[id="trial_submission_email"]').value).to eq '<EMAIL>'
      expect(page.find('input[id="trial_submission_first_name"]').value).to eq 'Huy'
      expect(page.find('input[id="trial_submission_last_name"]').value).to eq 'Nguyen'
      expect(page.find('input[id="trial_submission_company_name"]').value).to eq 'Holistics'
    end

    it 'when edit trial' do
      qlogin(admin, edit_path)

      expect(page.find('input[id="trial_submission_email"]').value).to eq '<EMAIL>'
      expect(page.find('input[id="trial_submission_first_name"]').value).to eq 'Uncle'
      expect(page.find('input[id="trial_submission_last_name"]').value).to eq 'Sam'
      expect(page.find('input[id="trial_submission_company_name"]').value).to eq 'USA'
    end
  end

  describe 'Create new trial submission' do
    context 'when existed tenant' do
      let!(:tenant) { FactoryBot.create(:tenant, uname: 'usa.com', name: 'USA') }
      let!(:ts) do
        FactoryBot.create(:tenant_subscription, tenant: tenant, status: 'active', expired_at: Time.now + 10.days)
      end

      before do
        admin.tenant = Tenant.find_by(uname: 'holistics')
        admin.save

        qlogin(admin,
               '/admin/trial_submissions/new?email=<EMAIL>&first_name=Uncle&last_name=Sam&company_name=USA',)
        set_text('textarea[name="trial_submission[confirm_email_template]"]', '{activation_link}')
      end

      it 'create success when skip validate existed tenant' do
        # click skip validate
        safe_click('input[id="trial_submission_skip_validate_existed_tenant_true"]')

        safe_click('button[id="previewBtn"]')

        accept_confirm do
          safe_click('input[type="submit"]')
        end

        wait_for_element_load('.flash')

        wait_expect(true, 5) do
          page.has_css?('.flash', text: 'Trial submission was successfully created.')
        end
      end

      it 'raise error when dont skip validate' do
        safe_click('input[id="trial_submission_skip_validate_existed_tenant_false"]')

        safe_click('button[id="previewBtn"]')

        accept_confirm do
          safe_click('input[type="submit"]')
        end

        wait_for_element_load('.description')

        wait_expect(true, 5) do
          expect(page.find('.description').text).to match(/Your company is already a Holistics' customer/)
        end
      end
    end
  end
end
