# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Admin::TenantsController, js: true, stable: true do
  let(:admin) { get_test_admin }
  before do
    admin.update(email: User::TEST_SUPER_ADMIN_EMAIL) # Make super admin
  end

  let(:tenant) { FactoryBot.create :tenant }

  let!(:ft) do
    FactoryBot.create(
      :feature_toggle,
      key: 'test',
      toggle_mode: 'disabled',
      except_tenant_ids: [],
    )
  end
  let!(:ft2) do
    FactoryBot.create(
      :feature_toggle,
      key: 'test2',
      toggle_mode: 'enabled',
      except_tenant_ids: [tenant.id],
    )
  end
  let!(:ft3) do
    FactoryBot.create(
      :feature_toggle,
      key: 'test3',
      toggle_mode: 'enabled',
      except_tenant_ids: [],
    )
  end
  let!(:ft4) do
    FactoryBot.create(
      :feature_toggle,
      key: 'test4',
      toggle_mode: 'enabled',
      except_tenant_ids: [],
    )
  end

  it 'works' do
    qlogin(admin, "/admin/tenants/#{tenant.id}")
    click_link('Export/Import FTs')

    table = page.find('#current-fts')
    rows = table.text.split("\n").map(&:strip)
    expect(rows).to include(
      'test NO',
      'test2 NO',
      'test3 YES',
      'test4 YES',
    )

    input = page.find('#new-fts-input')
    fts = Oj.load(input.value, mode: :json)
    expect(fts['test']).to be(false)
    expect(fts['test2']).to be(false)
    expect(fts['test3']).to be(true)
    expect(fts['test4']).to be(true)

    input.native.clear
    input.fill_in(
      with: Oj.dump(
        {
          test: false,
          test2: true,
          test3: false,
        },
        mode: :json,
      ),
    )

    changes_table = page.find('#changes tbody')
    changes_rows = changes_table.text.split("\n").map(&:strip)
    expect(changes_rows.sort).to eq(
      [
        'test2 No -> YES',
        'test3 Yes -> NO',
      ],
    )

    accept_confirm do
      safe_click('#submit-btn')
    end

    notice = page.find('.flash_notice')
    expect(notice.text).to include('Updated 2 FTs')

    expect(ft.reload.active_for(tenant.id)).to be(false)
    expect(ft.except_tenant_ids).to eq([])
    expect(ft2.reload.active_for(tenant.id)).to be(true)
    expect(ft2.except_tenant_ids).to eq([])
    expect(ft3.reload.active_for(tenant.id)).to be(false)
    expect(ft3.except_tenant_ids).to eq([tenant.id])
    expect(ft4.reload.active_for(tenant.id)).to be(true)
    expect(ft4.except_tenant_ids).to eq([])

    table = page.find('#current-fts')
    rows = table.text.split("\n").map(&:strip)
    expect(rows).to include(
      'test NO',
      'test2 YES',
      'test3 NO',
      'test4 YES',
    )

    version = tenant.last_version
    expect(version.data[:action_data].deep_symbolize_keys).to eq(
      feature_toggle_diffs: [
        { key: 'test2', newValue: true },
        { key: 'test3', newValue: false },
      ],
    )

    visit "/admin/tenants/#{tenant.id}"
    table = page.find('[data-ci="versions-table"]')
    rows = table.text.split("\n").map(&:strip)
    expect(rows[1]).to match(/Import Feature Toggles #{Regexp.escape('{"test2":true,"test3":false}')}/)
  end
end
