# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Admin manage feature-based pricing plans', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:plan) { FactoryBot.create(:plan, name: 'Professional Plan') }
  let!(:tsub) { FactoryBot.create(:tenant_subscription, status: 'active', tenant: tenant, plan: plan) }
  let!(:ft) { FactoryBot.create(:feature_toggle, key: 'ft_key', toggle_mode: 'disabled', used_for: 'billing') }

  before do
    admin.update(email: User::TEST_SUPER_ADMIN_EMAIL) # Make super admin
    qlogin(admin, '/admin')
  end

  it 'should create billing Feature Toggle' do
    ft_key = 'ft_billing_feature_001'

    visit('/admin/feature_toggles')
    click_link('New Feature Toggle')

    select_dropdown(find('#feature_toggle_used_for'), 'billing')
    fill_text('#feature_toggle_key', ft_key)
    select_dropdown(find('#feature_toggle_toggle_mode'), 'disabled')
    click_button('Create Feature toggle')

    expect(page).to have_content('Feature toggle was successfully created.')
  end

  it 'should add a Feature to a Plan' do
    expect(ft.active_for(tenant.id)).to eql(false)

    visit('/admin/plans')
    find('td', text: 'Professional Plan').sibling('.col-actions').click_link('Edit')
    click_link('Add New Feature')

    select_dropdown(find('#plan_feature_plan_rules_attributes_0_feature_toggle_id'), ft.id)
    select_dropdown(find('#plan_feature_plan_rules_attributes_0_desired_toggle_mode'), 'enabled')
    click_button('Update Plan')

    expect(page).to have_content('Plan was successfully updated.')
    expect(ft.reload.active_for(tenant.id)).to eql(true)
  end

  it 'should update Feature Toggles when admins update subscribed Plan of Tenant' do
    ft2 = FactoryBot.create(:feature_toggle, key: 'FT2', used_for: 'billing')
    fr = FactoryBot.create(:feature_plan_rule, feature_toggle: ft2, desired_toggle_mode: 'enabled')
    plan2 = FactoryBot.create(:plan, name: 'Entry Plan', feature_plan_rules: [fr])

    expect(ft2.reload.active_for(tenant.id)).to eql(false)

    visit("admin/tenant_subscriptions/#{tsub.id}/edit")
    fill_text('#tenant_subscription_plan_id', plan2.id)
    click_button('Update Tenant subscription')

    expect(page).to have_content('Tenant subscription was successfully updated.')
    expect(ft2.reload.active_for(tenant.id)).to eql(true)
  end
end
