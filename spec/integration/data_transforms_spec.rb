# typed: false
require 'rails_helper'
require 'action_view'

describe 'data-transforms timestamp format', legacy: true do
  include ActionView::Helpers::DateHelper

  context 'create job with fix timestamp', js: true do
    let (:timestamp) { "2016-01-03T13:49:26.247Z" }
    let(:tenant) { get_test_tenant }
    let (:transform) { FactoryBot.create :data_transform, tenant: tenant }

    let (:user) { FactoryBot.create :user,
                                     tenant_id: tenant.id,
                                     name: 'testtest',
                                     email: '<EMAIL>',
                                     password: 'testtest',
                                     role: 'admin',
                                     settings: '{"time_zone": "Hanoi"}'
    }

    let! (:job1) { FactoryBot.create :job,
                                      source_type: transform.class.name,
                                      source_id: transform.id,
                                      tenant_id: tenant.id,
                                      start_time: timestamp,
                                      end_time: timestamp
    }

    before do
      Timecop.travel(Time.parse('2016-05-15 10:00:00 UTC'))
      get_test_ds
    end
    after { Timecop.return }

    let(:admin) { get_test_admin }

    xit 'check if timestamp is rendered correctly' do
      qlogin(user, '/data_transforms')
      sleep 0.1
      expect(page.all('.table-list-jobs .cell-start-time').first.text).to eq("Jan 03, 2016 8:49 PM (UTC+0700)")
      expect(page.all('.table-list-jobs .cell-end-time').first.text).to eq("Jan 03, 2016 8:49 PM (UTC+0700)")
      expect(page.all('.table-list-transforms .cell-last-run').first.text).to match /ago/
    end
  end
end
