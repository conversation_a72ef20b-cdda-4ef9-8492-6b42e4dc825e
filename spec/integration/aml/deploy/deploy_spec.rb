require 'rails_helper'

describe 'deploy', :js do
  include_context 'aml_studio_dataset'
  include_context 'aml_studio_dev_mode'

  let(:user) { get_test_admin }
  let(:ds) do
    FactoryBot.create(:data_source, dbtype: 'postgresql', name: 'htest', dbconfig: dbconfig_rails_test_env.to_json)
  end

  def click_deploy
    safe_login user, '/studio/projects/1/explore'
    safe_click('[data-ci="deploy-action"]')

    wait_for_element_load('.ci-deployment-panel')
  end

  it 'deploys successfully' do
    # just make changes to trigger deployment button
    proj_work_flow.remove('README.md')

    click_deploy

    wait_for_all_ajax_requests
    expect(page).to have_css 'div', text: 'Publish successfully! Go to Reporting to start exploring.'
  end

  it 'requests dataset mapping' do
    proj_work_flow.remove('datasets/ecommerce_2.dataset.aml')

    click_deploy
    sleep 1

    expect(page).to have_css 'div', text: 'Failed to publish: Need to map production dataset to publishing dataset'
    expect(page).to have_css 'div', text: 'Dataset mapping: Running'
    expect(page).to have_css 'p', text: "Can't find dataset ecommerce_2"
  end

  describe 'show object deployment result' do
    include_context 'aml_studio_dashboard'

    it 'show deployment result correctly' do
      # just make changes to trigger deployment button
      proj_work_flow.remove('README.md')

      click_deploy
      sleep 1

      wait_for_all_ajax_requests

      expect(page).to have_css '[data-ci="deployed-object-reporting-url-test_dashboard"]'
      expect(page).to have_css '[data-ci="deployed-object-reporting-url-empty_dashboard"]'
      safe_click('[data-ci="deployed-object-file-url-empty_dashboard"]')
      url = URI.parse(current_url)
      expect(url.path).to eq "/studio/projects/1/explore/empty#{dashboard_extension}"

      new_window = page.window_opened_by { safe_click('[data-ci="deployed-object-reporting-url-empty_dashboard"]') }
      within_window new_window do
        wait_for_element_load('.h-dashboard-container')
        expect(page).to have_content('Empty dashboard 4.0')
      end
    end
  end

  # context 'deploy dashboard' do
  #   include_context 'aml_studio_dashboard'
  #   include_context 'aml_studio_dev_mode'

  #   it 'should show error in dashboard deployment' do
  #     # just make changes to trigger deployment button
  #     proj_work_flow.remove('README.md')

  #     FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)

  #     allow(Dashboard).to receive(:import!) do
  #       raise StandardError, 'Error in dashboard'
  #     end

  #     safe_login user, '/studio/projects/1/explore'
  #     wait_for_element_load('[data-ci="deploy-action"]')
  #     safe_click('[data-ci="deploy-action"]')

  #     wait_for_element_load('.ci-deployment-panel')
  #     expect(page).to have_selector 'div', text: 'Error in dashboard'
  #   end
  # end
end
