# typed: false

require 'rails_helper'

describe 'recent edited file', :js do
  let(:admin) { get_test_admin }
  let!(:first_data_set_survey_answer) do
    FactoryBot.create(:survey_answer, tenant: tenant, question_key: 'onboarding:first_data_set',
                                      data: { created: true },)
  end

  before do
    FeatureToggle.toggle_global('aml_studio_recent_edited_files', true)
  end

  def access_aml_studio_page(url = '/studio/projects/1/explore/datasets/test.dataset.aml')
    safe_login admin, url

    safe_click('.ci-dev-mode-toggle .ci-toggle-dev-mode')
    wait_for_element_load('.ci-aml-add')
  end

  def click_on_modeling_tab
    page.find('.ci-js-main-features', text: 'Development').click
    wait_for_element_load('.ci-dev-mode-toggle')
  end

  def click_on_aml_file
    wait_for_element_load('.ci-navigation-node', exact_text: 'index.aml')
    page.find('.ci-navigation-node', exact_text: 'index.aml').click
    wait_for_element_load('[data-ci="aml-editor"]')
  end

  def click_on_setting_tab
    wait_and_click('[data-ci="Settings"]')
    wait_for_element_load('[data-ci="aml-settings-page"]')
  end

  def click_on_explore_tab
    wait_and_click('[data-ci="Explore"]')
    wait_for_element_load('[data-ci="aml-explore-page"]')
  end

  describe 'show default page' do
    include_context 'aml_studio_basic'

    it 'when click on modeling tab from everywhere' do
      admin.update(dev_mode_enabled: true)
      safe_login admin, '/home'

      click_on_modeling_tab
      expect(page).to have_css('[data-ci="default-explore"]')

      # select a AML file, click modeling tab => open default page
      click_on_aml_file
      click_on_modeling_tab
      expect(page).to have_css('[data-ci="default-explore"]')

      # go to setting tab, click on modeling tab => open default page
      click_on_aml_file
      click_on_setting_tab
      click_on_modeling_tab
      expect(page).to have_css('[data-ci="default-explore"]')

      # in a AML File, go to reporting and then back to modeling => should open default page
      click_on_aml_file
      page.find('.ci-js-main-features', text: 'Reporting').click
      sleep 0.5
      click_on_modeling_tab
      expect(page).to have_css('[data-ci="default-explore"]')
    end

    it 'when click on explore tab from explore tab itself' do
      access_aml_studio_page('/studio')
      click_on_aml_file

      # 19/02/2025: disable for now based on new product behavior
      # We might not need to open default page when click on explore tab
      # ToDo: clarify with product team

      # click_on_explore_tab
      # expect(page).to have_css('[data-ci="default-explore"]')
    end
  end

  describe 'show last opened file' do
    include_context 'aml_studio_basic'

    it 'when click on explore tab from other AMLStudio tab' do
      access_aml_studio_page('/studio')
      click_on_aml_file

      last_url = page.current_url
      click_on_setting_tab
      click_on_explore_tab
      new_url = page.current_url

      expect(CGI.unescape(last_url)).to eq(CGI.unescape(new_url))
    end
  end

  describe 'recent edited files' do
    def open_default_page
      page.find('.ci-js-main-features', text: 'Reporting').click
      sleep 0.5
      page.find('.ci-js-main-features', text: 'Development').click
    end
    shared_examples 'recent edited file' do
      it 'shows empty recent edited files' do
        access_aml_studio_page('/studio')
        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        content = page.first('[data-ci="recent-edited-files-container"]').text(normalize_ws: true)
        expect(content).to eq "You haven't modified any files in this branch recently."
      end

      it 'show list after edit files' do
        access_aml_studio_page
        # edit file
        safe_click('.ci-toggle-CODE-mode')
        fill_in_monaco('.aml-editor', '123')
        # save file
        safe_click('.ci-save')
        wait_for_element_load('.ci-is-saved')
        # open default page
        sleep 0.5
        open_default_page

        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        expect(page.all('[data-ci="recent-file-item"]').length).to eq 1
        expect(page.first('[data-ci="recent-file-item"] button').text).to eq 'test.dataset.aml'
        expect(page.first('[data-ci="recent-file-path"]').text).to eq 'datasets'
      end

      it 'show list recent files after create new files' do
        access_aml_studio_page
        # create empty file
        safe_click('.ci-aml-add')
        safe_click('.hui-dropdown-floating span[data-icon="aml-file-icon"]')
        wait_for_element_load('.ci-title-input')
        page.first('.ci-title-input').set 'new_file.aml'
        page.first('.ci-title-input').send_keys(:enter)

        wait_for do
          current_url.include? 'new_file.aml'
        end
        # open default page
        sleep 0.5
        open_default_page

        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        expect(page.all('[data-ci="recent-file-item"]').length).to eq 1
        expect(page.first('[data-ci="recent-file-item"] button').text).to eq 'new_file.aml'
      end

      it 'does not show list when switch to production' do
        access_aml_studio_page
        # create empty file
        safe_click('.ci-aml-add')
        safe_click('.hui-dropdown-floating span[data-icon="aml-file-icon"]')
        wait_for_element_load('.ci-title-input')
        page.first('.ci-title-input').set 'new_file.aml'
        page.first('.ci-title-input').send_keys(:enter)

        wait_for do
          current_url.include? 'new_file.aml'
        end

        # open default page
        sleep 0.5
        open_default_page

        # switch to prod mode
        safe_click('.ci-dev-mode-toggle .ci-toggle-dev-mode')

        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        expect(page.all('[data-ci="recent-file-item"]').length).to eq 0
      end

      it 'updates recent file path when rename file' do
        access_aml_studio_page
        # edit file
        safe_click('.ci-toggle-CODE-mode')
        fill_in_monaco('.aml-editor', '123')
        # save file
        safe_click('.ci-save')
        wait_for_element_load('.ci-is-saved')
        # rename file
        page.find('.ci-navigation-node .active').hover
        safe_click('.ci-navigation-node .active .ci-navigation-node-action')
        safe_click('.ci-rename')
        wait_for_element_load('.ci-title-input')
        page.find('.ci-title-input').send_keys('new_test')
        safe_click('.aml-editor')
        # open default page
        sleep 0.5
        open_default_page

        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        expect(page.all('[data-ci="recent-file-item"]').length).to eq 1
        expect(page.first('[data-ci="recent-file-item"] button').text).to eq 'new_test.dataset.aml'
        expect(page.first('[data-ci="recent-file-path"]').text).to eq 'datasets'
      end

      it 'updates recent file path when rename folder' do
        access_aml_studio_page
        # edit file
        safe_click('.ci-toggle-CODE-mode')
        fill_in_monaco('.aml-editor', '123')
        # save file
        safe_click('.ci-save')
        wait_for_element_load('.ci-is-saved')
        # rename folder
        page.find('[title="datasets"]').hover
        safe_click('[title="datasets"] .ci-navigation-node-action')
        safe_click('.ci-rename')
        wait_for_element_load('.ci-title-input')
        page.find('.ci-title-input').send_keys('new_folder')
        safe_click('.aml-editor')
        # open default page
        sleep 0.5
        open_default_page

        wait_for_element_load('[data-ci="recent-edited-files-container"]')
        expect(page.all('[data-ci="recent-file-item"]').length).to eq 1
        expect(page.first('[data-ci="recent-file-path"]').text).to eq 'new_folder'
      end
    end

    context 'with explicit git' do
      include_context 'aml_studio_explicit'
      include_examples 'recent edited file'
    end

    # TODO: investigate the flaky test https://app.asana.com/0/76997687380943/1204961400377355/f
    xcontext 'with implicit git' do
      include_context 'aml_studio_basic'
      include_examples 'recent edited file'
    end
  end
end
