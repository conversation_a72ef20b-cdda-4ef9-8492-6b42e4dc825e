require 'rails_helper'

describe 'status panel spec', :js do
  include_context 'aml_studio_explicit'
  include_context 'aml_studio_dev_mode'

  let(:user) { get_test_admin }
  let(:ds) do
    FactoryBot.create(:data_source, dbtype: 'postgresql', name: 'htest', dbconfig: dbconfig_rails_test_env.to_json)
  end

  before do
    FeatureToggle.toggle_global('aml_studio:status_panel', true)
  end

  after do
    FeatureToggle.toggle_global('aml_studio:status_panel', false)
  end

  it 'shows only bottom bar at first opening and show status panel when clicked on the icon' do
    safe_login user, '/studio/projects/1/explore'
    wait_for_element_load('.bottom-bar-container')
    wait_expect(false) { page.has_css?('.bottom-bar-container > div.item-group.active') }
    safe_click('[data-ci="status-panel-btn"]')
    wait_expect(true) { page.has_css?('[data-ci="status-panel"]') }
  end

  it 'show status panel when deploy' do
    safe_login user, '/studio/projects/1/explore'
    sleep 5

    wait_expect(false) { page.has_css?('[data-ci="status-panel"]') }
    safe_click('[data-ci="deploy-action"]')
    wait_expect(true) { page.has_css?('[data-ci="status-panel"]') }
  end

  it 'show correct status for commit & deploy' do
    user_repo.work_flow(user).write_file('test.txt', 'abc')
    safe_login user, '/studio/projects/1/explore'
    safe_click('[data-ci="status-panel-btn"]')

    # Commit
    wait_expect(false) { page.find('[data-ci="commit-action"]').disabled? }
    safe_click('[data-ci="commit-action"]')
    wait_for_element_load('[data-ci="commit-message"]')
    safe_click('[data-ci="commit-button"]')

    # Display commit row
    wait_expect(true) { page.find('[data-ci="history-row-commit"]').visible? }

    # Deploy
    wait_expect(false) { page.find('[data-ci="deploy-action"]').disabled? }
    sleep 5
    safe_click('[data-ci="deploy-action"]')
    # Validate project line
    wait_expect(true) { page.find('[data-ci="history-row-validate-project"]').visible? }
    # Publish project line
    wait_expect(true) { page.find('[data-ci="history-row-publish"]').visible? }
  end

  it 'can check commit job status after page reload' do
    user_repo.work_flow(admin).write_file('test.aml', 'Test content')
    qlogin(:admin, "/studio/projects/#{project.id}/explore")

    sleep 5

    # Click the commit button
    wait_for_element_load('[data-ci="commit-action"]')
    safe_click('[data-ci="commit-action"]')

    # Wait for the commit modal to appear
    wait_for_element_load('[data-ci="commit-message"]')

    # Submit the commit
    safe_click('[data-ci="commit-button"]')

    # Reload the page immediately
    page.evaluate_script('window.location.reload()')

    safe_click('[data-ci="status-panel-btn"]')

    wait_for_all_ajax_requests
    wait_expect(true, 10) { page.has_content?('Committed successfully') }
  end
end
