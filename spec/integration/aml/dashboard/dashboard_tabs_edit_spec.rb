require 'rails_helper'

describe 'dashboard tabs edit', :js do
  let!(:ds) do
    ds = get_test_ds.dup
    ds.name = 'test_ds'
    ds.save!
    ds
  end
  let(:user) { get_test_admin }
  let(:dashboard_url) { '/studio/projects/1/explore/dashboards/tabs.page.aml' }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global('dashboards_v4:auto_placement_for_new_block', true)
    FeatureToggle.toggle_global('dashboard_v4:use_dashboard_fe_viz_cache', true)
    ThreadContext.set(:current_user, user)
    Capybara.current_window.resize_to 1600, 1000
  end

  include_context 'aml_studio_basic' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/beers'
    end
  end
  include_context 'aml_studio_dev_mode'

  def load_dashboard
    qlogin(user, dashboard_url)
    wait_for_element_load '.dac-body'
    safe_click('.left-sidebar-container .btn-toggle-icon') # close the sidebar for more space
  end

  def get_tabs
    page.all('[data-ci="ci-tab-selector"]').map(&:text)
  end

  def get_interactions
    raw = page.evaluate_script("JSON.stringify(document.querySelector('#vm-app').__vue_app__.config.globalProperties.$store.getters['aml/files/currentFile'].previewModel.definition.interactions)")
    JSON.parse(raw)
  end

  def get_block_interactions(uname)
    interactions = get_interactions
    to = interactions.filter { |i| i['from'] === uname }.map { |i| i['to'] }.uniq.sort
    from = interactions.filter { |i| i['to'] === uname }.map { |i| i['from'] }.uniq.sort
    {
      from: from,
      to: to
    }
  end

  it 'CRUD tabs' do
    load_dashboard

    # initially has 2 tabs
    wait_expect(['Tab 1', 'Tab 2']) { get_tabs }

    # add new tab
    safe_click('[data-ci="ci-new-tab"]')
    wait_expect(['Tab 1', 'Tab 2', 'Untitled']) { get_tabs }

    # rename tab
    safe_click('#h-tab-tab3 [data-ci="ci-tab-actions"]')
    safe_click('.ci-rename-tab')
    page.send_keys('Tab 3')
    page.send_keys(:enter)
    wait_expect(['Tab 1', 'Tab 2', 'Tab 3']) { get_tabs }

    # move tab left
    safe_click('#h-tab-tab3 [data-ci="ci-tab-actions"]')
    safe_click('.ci-move-tab-left')
    wait_expect(['Tab 1', 'Tab 3', 'Tab 2']) { get_tabs }

    # move tab right
    safe_click('#h-tab-tab3 [data-ci="ci-tab-actions"]')
    safe_click('.ci-move-tab-right')
    wait_expect(['Tab 1', 'Tab 2', 'Tab 3']) { get_tabs }

    # delete tab
    safe_click('#h-tab-tab3 [data-ci="ci-tab-actions"]')
    safe_click('.ci-remove-tab')
    wait_expect(['Tab 1', 'Tab 2']) { get_tabs }
  end

  it 'copy block to tab (sync)' do
    load_dashboard

    wait_expect({
      from: ['f1', 'f2'],
      to: []
    }) { get_block_interactions('v1') }

    safe_click('#block-v1')
    page.send_keys(:control, 'c')

    safe_click('#h-tab-tab2')
    page.send_keys(:control, 'v')

    safe_click('[data-ci="ci-paste-sync"]')

    # auto map v1 to blocks in tab 2
    wait_expect({
      from: ['f1', 'f2', 'f3', 'v2'],
      to: ['v2']
    }) { get_block_interactions('v1') }
  end

  it 'copy block to tab (clone)' do
    load_dashboard

    wait_expect({
      from: ['f1', 'f2'],
      to: []
    }) { get_block_interactions('v1') }

    safe_click('#block-v1')
    page.send_keys(:control, 'c')

    safe_click('#h-tab-tab2')
    page.send_keys(:control, 'v')

    safe_click('[data-ci="ci-paste-copy"]')

    # nothing changed for v1
    wait_expect({
      from: ['f1', 'f2'],
      to: []
    }) { get_block_interactions('v1') }
    # auto map for the new block
    wait_expect({
      from: ['f1', 'f3', 'v2'],
      to: ['v2']
    }) { get_block_interactions('v3') }
  end

  it 'cache viz result when switching tabs' do
    load_dashboard

    expect_ajax_requests({ url: /\/submit_generate/ }, count: 1, wait: true) # the first tab has 1 viz => 1 request

    # switch tab
    safe_click('#h-tab-tab2')
    expect_ajax_requests({ url: /\/submit_generate/ }, count: 2, wait: true) # the second tab has 1 viz => total 2 requests

    # switch back
    safe_click('#h-tab-tab1')
    expect_ajax_requests({ url: /\/submit_generate/ }, count: 2, wait: true) # reuse cache => no new submit_generate request
  end
end
