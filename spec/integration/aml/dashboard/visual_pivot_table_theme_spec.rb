# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Pivot Table theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global('viz:pivot_v2', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
    FeatureToggle.toggle_global('pivot:freeze_columns', true)
  end

  let(:pivot_column_styles) do
    <<~STYLES
      column_styles: [
        ColumnStyle {
          key: '3_category'
          width: 90
        },
        ColumnStyle {
          key: '3_region'
          width: 67
        },
        ColumnStyle {
          key: 'sum_value'
          width: 67
        },
        ColumnStyle {
          key: 'sum_quantity'
          width: 76
        }
      ]
    STYLES
  end

  let(:test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            show_row_total: true
            show_column_total: true
            #{pivot_column_styles}
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'sub-total table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            show_sub_total: true
            show_row_total: true
            show_column_total: true
            #{pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'freeze column table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            frozen_columns: 1
            show_row_total: true
            show_column_total: true
            #{pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'CF table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            empty_cell_as_zero: true
            conditional_formats: [
              ConditionalFormat {
                key: 'sum_value'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: 'sum_quantity'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#BF0E08'
                  background_color: '#FAD2D1'
                }
              }
            ]
            show_row_total: true
            show_column_total: true
            #{pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'range selection table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            empty_cell_as_zero: true
            conditional_formats: [
              ConditionalFormat {
                key: 'sum_value'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: 'sum_quantity'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#BF0E08'
                  background_color: '#FAD2D1'
                }
              }
            ]
            show_row_total: true
            show_column_total: true
            #{pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
        additional_steps: lambda {
          first_header = page.find('.ag-header-cell[col-id="3_category"]')
          data_header = page.find('.ag-header-cell[col-id="_holistics_total_->->sum_quantity"]')
          first_header.drag_to(data_header)
          sleep 1
        },
      },
    ]
  end

  # Individual test cases for all scenarios with unified theme approach
  # This enables line-based test execution (e.g., rspec file_spec.rb:123) and debugging breakpoints
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic table' do
      scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'sub-total table' do
      scenario = test_scenarios.find { |s| s[:name] == 'sub-total table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'freeze column table' do
      scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'CF table' do
      scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'range selection table' do
      scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
      run_test_scenario(scenario, with_theme: false)
    end

    # Scenarios with colorful theme
    context 'with colorful theme' do
      it 'basic table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'sub-total table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sub-total table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'freeze column table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'CF table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'range selection table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end
    end

    # Scenarios with oasis theme
    context 'with oasis theme' do
      it 'basic table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'sub-total table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sub-total table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'freeze column table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'CF table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'range selection table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end
    end

    # Scenarios with minimal theme
    context 'with minimal theme' do
      it 'basic table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'sub-total table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sub-total table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'freeze column table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'CF table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'range selection table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end
    end
  end
end
