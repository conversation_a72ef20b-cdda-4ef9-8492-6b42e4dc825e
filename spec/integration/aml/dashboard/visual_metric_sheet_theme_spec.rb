# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Metric Sheet theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:metric-sheet', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
  end

  let(:test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :metric_sheet,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
      {
        name: 'metric sheet with custom rows',
        viz_type: :metric_sheet,
        aml_params: {
          rows_aml: <<~ROWS,
            MetricHeading {
              label: 'Sales Performance'
              settings {
                background_color: '#e1e3ea'
                text_color: '#31353f'
              }
            },
            MetricSeries {
              field: VizFieldFull {
                ref: ref('new_sql_model', 'value')
                aggregation: 'sum'
                format {
                  type: 'number'
                  pattern: 'inherited'
                }
                uname: 'sum_value'
              }
              settings {
                mark_type: 'column'
              }
            },
            MetricSeries {
              field: VizFieldFull {
                ref: ref('new_sql_model', 'quantity')
                aggregation: 'avg'
                format {
                  type: 'number'
                  pattern: 'inherited'
                }
                uname: 'avg_quantity'
              }
              settings {
                mark_type: 'line'
              }
            }
          ROWS
        },
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
      {
        name: 'metric sheet with settings',
        viz_type: :metric_sheet,
        aml_params: {
          settings_aml: <<~SETTINGS,
            max_columns: 3
            show_sparkline: true
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
    ]
  end

  # Individual test cases for all scenarios with unified theme approach
  # This enables line-based test execution (e.g., rspec file_spec.rb:123) and debugging breakpoints
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic table' do
      scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'metric sheet with custom rows' do
      scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with custom rows' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'metric sheet with settings' do
      scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with settings' }
      run_test_scenario(scenario, with_theme: false)
    end

    # Scenarios with colorful theme
    context 'with colorful theme' do
      it 'basic table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'metric sheet with custom rows with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with custom rows' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'metric sheet with settings with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with settings' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end
    end

    # Scenarios with oasis theme
    context 'with oasis theme' do
      it 'basic table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'metric sheet with custom rows with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with custom rows' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'metric sheet with settings with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with settings' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end
    end

    # Scenarios with minimal theme
    context 'with minimal theme' do
      it 'basic table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'metric sheet with custom rows with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with custom rows' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'metric sheet with settings with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'metric sheet with settings' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end
    end
  end
end
