# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Metric Sheet theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:metric-sheet', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
  end

  let(:metric_sheet_test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :metric_sheet,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
      {
        name: 'metric sheet with custom rows',
        viz_type: :metric_sheet,
        aml_params: {
          rows_aml: <<~ROWS,
            MetricHeading {
              label: 'Sales Performance'
              settings {
                background_color: '#e1e3ea'
                text_color: '#31353f'
              }
            },
            MetricSeries {
              field: VizFieldFull {
                ref: ref('new_sql_model', 'value')
                aggregation: 'sum'
                format {
                  type: 'number'
                  pattern: 'inherited'
                }
                uname: 'sum_value'
              }
              settings {
                mark_type: 'column'
              }
            },
            MetricSeries {
              field: VizFieldFull {
                ref: ref('new_sql_model', 'quantity')
                aggregation: 'avg'
                format {
                  type: 'number'
                  pattern: 'inherited'
                }
                uname: 'avg_quantity'
              }
              settings {
                mark_type: 'line'
              }
            }
          ROWS
        },
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
      {
        name: 'metric sheet with settings',
        viz_type: :metric_sheet,
        aml_params: {
          settings_aml: <<~SETTINGS,
            max_columns: 3
            show_sparkline: true
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      },
    ]
  end

  # Run all test scenarios both with and without themes
  define_theme_test_scenarios(metric_sheet_test_scenarios)
end
