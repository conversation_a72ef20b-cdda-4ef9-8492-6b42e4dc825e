# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Transpose PivotTable theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global('viz:pivot_v2', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
    FeatureToggle.toggle_global('pivot:freeze_columns', true)
    FeatureToggle.toggle_global('pivot:transpose', true)
  end

  # Define common column styles for transpose pivot tables
  let(:transpose_pivot_column_styles) do
    <<~STYLES
      column_styles: [
        ColumnStyle {
          key: '3_category'
          width: 90
        },
        ColumnStyle {
          key: '3_region'
          width: 67
        },
        ColumnStyle {
          key: 'sum_value'
          width: 67
        },
        ColumnStyle {
          key: 'sum_quantity'
          width: 76
        }
      ]
    STYLES
  end

  # Define test scenarios for Transpose PivotTable
  let(:test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            value_labels_position {
              placement: 'rows'
            }
            show_row_total: true
            show_column_total: true
            #{transpose_pivot_column_styles}
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'sub-total table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            value_labels_position {
              placement: 'rows'
            }
            show_sub_total: true
            show_row_total: true
            show_column_total: true
            #{transpose_pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'freeze column table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            value_labels_position {
              placement: 'rows'
            }
            frozen_columns: 1
            show_row_total: true
            show_column_total: true
            #{transpose_pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'CF table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            value_labels_position {
              placement: 'rows'
            }
            empty_cell_as_zero: true
            conditional_formats: [
              ConditionalFormat {
                key: 'sum_value'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: 'sum_quantity'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#BF0E08'
                  background_color: '#FAD2D1'
                }
              }
            ]
            show_row_total: true
            show_column_total: true
            #{transpose_pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
      },
      {
        name: 'range selection table',
        viz_type: :pivot_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            value_labels_position {
              placement: 'rows'
            }
            empty_cell_as_zero: true
            conditional_formats: [
              ConditionalFormat {
                key: 'sum_value'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: 'sum_quantity'
                aggregation: 'sum'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 0
                  }
                  text_color: '#BF0E08'
                  background_color: '#FAD2D1'
                }
              }
            ]
            show_row_total: true
            show_column_total: true
            #{transpose_pivot_column_styles}
          SETTINGS
          position: 'pos(20, 20, 1160, 500)',
        },
        table_selector: '[data-uname="v1"] .h-pivot',
        additional_steps: lambda {
          # performing range selection
          first_header = page.find('.ag-header-cell[col-id="3_category"]')
          data_header = page.find('.ag-header-cell[col-id="_holistics_total_->->sum_value"]')
          first_header.drag_to(data_header)
          sleep 1
        },
      },
    ]
  end

  # Run all test scenarios both with and without themes
  include_examples 'theme test scenarios'
end
