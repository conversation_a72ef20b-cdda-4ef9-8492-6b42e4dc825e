require 'rails_helper'

describe 'dashboard canvas mobile edit', :js do
  let(:user) { get_test_admin }
  let(:dashboard_url) { '/studio/projects/1/explore/dashboards/test_mobile.page.aml' }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    ThreadContext.set(:current_user, user)
    Capybara.current_window.resize_to 1600, 1000
    user.nux[:seen_features] = ['dac-controls-panel']
    user.save!
  end

  include_context 'aml_studio_basic' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/beers'
    end
  end
  include_context 'aml_studio_dev_mode'

  def load_dashboard
    qlogin(user, dashboard_url)
    wait_for_element_load '.dac-body'
    safe_click('.left-sidebar-container .btn-toggle-icon') # close the sidebar for more space
    safe_click('.ci-toggle-mobile-mode') # open mobile edit
    wait_for_element_load('[data-ci="ci-canvas-mobile-editor"]')
  end

  it 'can edit mobile layout' do
    load_dashboard

    # hide block
    safe_click('#visible-block-p1 [data-ci="ci-hide-block"]')
    safe_click('[data-ci="confirm-modal"] [data-hui-section="resolve-button"]')
    wait_for_element_load('#hidden-block-p1')
    safe_click('#visible-block-t1 [data-ci="ci-hide-block"]')
    wait_for_element_load('#hidden-block-t1')
    wait_expect(5) { page.all('.dac-mobile-canvas-layout .dac-block').count }

    # show block
    safe_click('#hidden-block-t1 [data-ci="ci-show-block"]')
    wait_for_element_load('#visible-block-t1')
    wait_expect(6) { page.all('.dac-mobile-canvas-layout .dac-block').count }

    # disable mobile view
    select_h_select_option('[data-ci="ci-mobile-mode-select"]', value: 'none')
    wait_expect(0) { page.all('.dac-mobile-canvas-layout').count }
  end
end
