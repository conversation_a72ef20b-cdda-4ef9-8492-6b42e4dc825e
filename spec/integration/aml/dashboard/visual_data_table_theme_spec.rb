# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Data Table theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('viz:table_v2', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('data_table:bottom_total_average_rows', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
    FeatureToggle.toggle_global('table:freeze_columns', true)
  end

  # Define test scenarios for DataTable
  let(:data_table_test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :data_table,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'num column table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'show_row_number: true' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'sum row table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'show_sum_row: true' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'freeze column table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'frozen_columns: 2' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'text wrap table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            column_styles: [
              ColumnStyle {
                key: '3_date_and_time'
                width: 60
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'CF table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            conditional_formats: [
              ConditionalFormat {
                key: '3_value'
                format: SingleFormat {
                  condition {
                    operator: 'greater_than'
                    value: 20
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: '3_customer_type'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 'Premium'
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'range selection table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            conditional_formats: [
              ConditionalFormat {
                key: '3_value'
                format: SingleFormat {
                  condition {
                    operator: 'greater_than'
                    value: 20
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: '3_customer_type'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 'Premium'
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
        additional_steps: lambda {
          # performing range selection
          first_header = page.find('.ag-header-cell[col-id="3_date_and_time"]') # Use uname as colId
          data_header = page.find('.ag-header-cell[col-id="3_customer_type"]')
          first_header.drag_to(data_header)
          sleep 1
        },
      },
    ]
  end

  # Run all test scenarios both with and without themes
  define_theme_test_scenarios(data_table_test_scenarios)
end
