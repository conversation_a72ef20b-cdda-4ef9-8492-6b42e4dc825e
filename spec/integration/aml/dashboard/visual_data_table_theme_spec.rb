# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Data Table theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('viz:table_v2', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('data_table:bottom_total_average_rows', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
    FeatureToggle.toggle_global('table:freeze_columns', true)
  end

  # Define test scenarios for DataTable
  let(:test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :data_table,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'num column table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'show_row_number: true' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'sum row table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'show_sum_row: true' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'freeze column table',
        viz_type: :data_table,
        aml_params: { settings_aml: 'frozen_columns: 2' },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'text wrap table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            column_styles: [
              ColumnStyle {
                key: '3_date_and_time'
                width: 60
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'CF table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            conditional_formats: [
              ConditionalFormat {
                key: '3_value'
                format: SingleFormat {
                  condition {
                    operator: 'greater_than'
                    value: 20
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: '3_customer_type'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 'Premium'
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
      },
      {
        name: 'range selection table',
        viz_type: :data_table,
        aml_params: {
          settings_aml: <<~SETTINGS,
            conditional_formats: [
              ConditionalFormat {
                key: '3_value'
                format: SingleFormat {
                  condition {
                    operator: 'greater_than'
                    value: 20
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              },
              ConditionalFormat {
                key: '3_customer_type'
                format: SingleFormat {
                  condition {
                    operator: 'is'
                    value: 'Premium'
                  }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table',
        additional_steps: lambda {
          # performing range selection
          first_header = page.find('.ag-header-cell[col-id="3_date_and_time"]') # Use uname as colId
          data_header = page.find('.ag-header-cell[col-id="3_customer_type"]')
          first_header.drag_to(data_header)
          sleep 1
        },
      },
    ]
  end

  # Individual test cases for all scenarios with unified theme approach
  # This enables line-based test execution (e.g., rspec file_spec.rb:123) and debugging breakpoints
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic table' do
      scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'num column table' do
      scenario = test_scenarios.find { |s| s[:name] == 'num column table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'sum row table' do
      scenario = test_scenarios.find { |s| s[:name] == 'sum row table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'freeze column table' do
      scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'text wrap table' do
      scenario = test_scenarios.find { |s| s[:name] == 'text wrap table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'CF table' do
      scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'range selection table' do
      scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
      run_test_scenario(scenario, with_theme: false)
    end

    # Scenarios with colorful theme
    context 'with colorful theme' do
      it 'basic table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'num column table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'num column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'sum row table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sum row table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'freeze column table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'text wrap table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'text wrap table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'CF table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'range selection table with colorful_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end
    end

    # Scenarios with oasis theme
    context 'with oasis theme' do
      it 'basic table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'num column table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'num column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'sum row table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sum row table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'freeze column table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'text wrap table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'text wrap table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'CF table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'range selection table with oasis_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end
    end

    # Scenarios with minimal theme
    context 'with minimal theme' do
      it 'basic table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'basic table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'num column table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'num column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'sum row table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'sum row table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'freeze column table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'freeze column table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'text wrap table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'text wrap table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'CF table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'CF table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'range selection table with minimal_table_theme' do
        scenario = test_scenarios.find { |s| s[:name] == 'range selection table' }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end
    end
  end
end
