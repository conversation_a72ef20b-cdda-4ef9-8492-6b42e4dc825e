require 'rails_helper'

describe 'reused blocks', :js do
  let!(:ds) do
    ds = get_test_ds.dup
    ds.name = 'test_ds'
    ds.save!
    ds
  end
  let(:user) { get_test_admin }
  let(:dashboard_url) { '/studio/projects/1/explore/dashboards/reused_blocks.page.aml' }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    ThreadContext.set(:current_user, user)
    user.nux[:seen_features] = ['dac-controls-panel']
    user.save!
  end

  include_context 'aml_studio_basic' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/beers'
    end
  end
  include_context 'aml_studio_dev_mode'

  def load_dashboard
    qlogin(user, dashboard_url)
    wait_for_element_load '.dac-body'
    wait_for_viz_load
  end

  def open_block_preference_modal
    safe_click('[data-ci="ci-more-dropdown"]')
    safe_click('.ci-block-preferences')
    wait_for_element_load('[data-ci="ci-block-preferences"]')
  end

  it 'prevents UI editor for reused blocks (full)' do
    load_dashboard

    # block v1: fully reused => block preferences too
    safe_click('#block-v1')
    expect(page).to have_css('[data-ci="ci-reused-block-indicator"]')
    open_block_preference_modal
    expect(page).to have_css('[data-ci="ci-reused-block-metadata-notice"]')
  end

  it 'prevents UI editor for reused blocks (partial)' do
    load_dashboard

    # block v2: only reuse viz => can edit preferences
    safe_click('#block-v2')
    expect(page).to have_css('[data-ci="ci-reused-block-indicator"]')
    open_block_preference_modal
    expect(page).to have_no_css('[data-ci="ci-reused-block-metadata-notice"]')
  end

  it 'prevents UI actions for reused position' do
    load_dashboard

    safe_click('#block-v1')
    expect(page).to have_css('[data-ci="ci-reused-position-indicator"]')
    safe_click('[data-ci="ci-more-dropdown"]')
    wait_for_element_load('.ci-arrange-block')
    page.find('.ci-arrange-block').hover
    expect(page).to have_content('Layout actions are disabled on blocks having code-controlled position')
  end

  it 'prevents UI actions for selected group with reused position' do
    load_dashboard

    safe_click('#block-v2')
    expect(page).to have_no_css('[data-ci="ci-reused-position-indicator"]')
    v1_el = page.find_by_id('block-v1')
    v1_el.click(:shift)
    expect(page).to have_css('[data-ci="ci-reused-position-indicator"]')
  end
end
