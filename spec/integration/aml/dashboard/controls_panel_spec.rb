require 'rails_helper'

describe 'controls panel', :js do
  let!(:ds) do
    ds = get_test_ds.dup
    ds.name = 'test_ds'
    ds.save!
    ds
  end
  let(:user) { get_test_admin }
  let(:dashboard_url) { '/studio/projects/1/explore/dashboards/tabs.page.aml' }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global('dashboards_v4:auto_placement_for_new_block', true)
    ThreadContext.set(:current_user, user)
    Capybara.current_window.resize_to 1600, 1000
  end

  include_context 'aml_studio_basic' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/beers'
    end
  end
  include_context 'aml_studio_dev_mode'

  def load_dashboard
    qlogin(user, dashboard_url)
    wait_for_element_load '.dac-body'
    safe_click('.left-sidebar-container .btn-toggle-icon') # close the sidebar for more space
  end

  it 'work' do
    load_dashboard
    # open
    safe_click("[data-ci='ci-controls-panel']")
    wait_expect(1) { page.find_all("[data-ci='ci-controls-panel-content']").size }

    if page.has_text?('Got it')
      find('button.hui-btn-primary-highlight', text: 'Got it').click
    end

    # close by clicking the button again
    safe_click("[data-ci='ci-controls-panel']")
    wait_expect(0) { page.find_all("[data-ci='ci-controls-panel-content']").size }

    # open again
    safe_click("[data-ci='ci-controls-panel']")
    wait_expect(1) { page.find_all("[data-ci='ci-controls-panel-content']").size }

    # it should has 2 controls
    wait_expect(2) { page.find_all("[data-ci='ci-control-item']").size }

    # can change filter value from here
    select_h_select_option("[data-ci='ci-control-item'] .ci-operator-select", label: 'contains')
    wait_expect(1) { page.find_all('.dac-submit-filters').size }

    # close by clicking the close button
    safe_click("[data-ci='ci-close-controls-panel']")
    wait_expect(0) { page.find_all("[data-ci='ci-controls-panel-content']").size }
  end
end
