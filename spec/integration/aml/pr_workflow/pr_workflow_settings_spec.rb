# typed: false

# @tag: #aml_studio

require 'rails_helper'

describe 'PR Workflow Settings', :js do
  include_context 'aml_studio_explicit'
  let(:admin) { get_test_admin }
  let(:admin2) { get_test_admin2 }
  let(:remote_project) { AmlStudio::Project.create!(name: 'Remote', tenant_id: admin2.tenant_id) }
  let(:remote_repo) do
    remote_project.repositories.create!(
      is_production: true,
      owner_id: admin2.id,
      tenant_id: remote_project.tenant_id,
    )
  end
  let(:client) { proj_repo.source }
  let(:remote_client) { remote_repo.source }

  before do
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_AUTOSAVE, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)

    AmlStudio::Values::GitProvider.any_instance.stub(:normalize_ssh_url).and_return(remote_repo.storage_path)

    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:token_expiration_date).and_return(Time.zone.parse('2030-01-01T00:00:00Z'))
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_return(123_456)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:webhook_exists?).and_return(true)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:validate_read_pr_permission!).and_return(true)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:delete_webhook).and_return(true)
    # Initially mock to return null pull request when API is called
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:fetch_latest_open_pr).and_return(nil)

    remote_client.init(bare: true)
    AmlStudio::ExternalGit::SetupConnection
      .new(project: project)
      .call('ssh://**************/owner/repo')
    admin.update!(dev_mode_enabled: true)
  end

  after do
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, false)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_AUTOSAVE, false)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
  end

  it 'completes the full PR workflow lifecycle' do
    qlogin(:admin, "/studio/projects/#{project.id}/settings")
    sleep 1
    wait_for_all_ajax_requests

    # 1. Enable PR workflow
    expect(page).to have_text('Enable PR Workflow')

    wait_for_element_load('[data-ci="enable-pr-workflow-switch"]')
    safe_click('[data-ci="enable-pr-workflow-switch"]')

    wait_for_element_load('[data-ci="pr-workflow-token-input"]')
    expect(page).to have_text('Provide Access Token')

    find('[data-ci="pr-workflow-token-input"]').send_keys('abc')
    safe_click('[data-ci="resolve-pr-workflow-settings"]')

    # Add a small sleep to ensure the modal is fully closed and the success message appears
    sleep 1
    wait_for_all_ajax_requests

    expect(page).to have_text('Enable PR Workflow successfully')

    # Mock a PR with number 12345 and verify it's displayed
    mock_pr = AmlStudio::Values::PullRequest.new(
      merge_commit_sha: 'sha123',
      state: AmlStudio::Values::PrStatus::Open,
      title: 'Test PR',
      merged_at: nil,
      number: 12_345,
      updated_at: Time.current,
      head_branch: 'feature_branch',
      base_branch: 'main',
      url: 'https://github.com/test/test/pull/12345',
    )
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:fetch_latest_open_pr).and_return(mock_pr)

    # Refresh the page to see the PR number
    visit current_path
    wait_for_all_ajax_requests

    # Verify PR number is displayed
    expect(page).to have_text('#12345')
    expect(page).to have_no_text('Publish')

    # 2. Update token
    wait_for_element_load('[data-ci="update-token-btn"]')
    safe_click('[data-ci="update-token-btn"]')

    wait_for_element_load('[data-ci="pr-workflow-token-input"]')
    expect(page).to have_text('Update Access Token')

    find('[data-ci="pr-workflow-token-input"]').send_keys('updated_token')
    safe_click('[data-ci="resolve-pr-workflow-settings"]')

    # Add a small sleep to ensure the modal is fully closed and the success message appears
    wait_for_all_ajax_requests

    expect(page).to have_text('Update token successfully')

    # 3. Disable PR workflow (but keep the token)
    wait_for_element_load('[data-ci="enable-pr-workflow-switch"]')
    safe_click('[data-ci="enable-pr-workflow-switch"]')

    # Add a longer sleep to ensure the toggle action completes
    wait_for_all_ajax_requests

    expect(page).to have_text('Disable PR Workflow successfully')
    expect(page).to have_no_text('Create PR')

    # Verify PR number is no longer displayed and Publish button is shown
    expect(page).to have_no_text('#12345')
    expect(page).to have_text('Publish')

    # 3.1 Update token while PR workflow is disabled
    wait_for_element_load('[data-ci="update-token-btn"]')
    safe_click('[data-ci="update-token-btn"]')

    wait_for_element_load('[data-ci="pr-workflow-token-input"]')
    expect(page).to have_text('Update Access Token')

    find('[data-ci="pr-workflow-token-input"]').send_keys('token_updated_while_disabled')
    safe_click('[data-ci="resolve-pr-workflow-settings"]')

    wait_for_all_ajax_requests

    expect(page).to have_text('Update token successfully')

    # Mock a new PR to confirm the PR workflow is working
    new_mock_pr = AmlStudio::Values::PullRequest.new(
      merge_commit_sha: 'sha456',
      state: AmlStudio::Values::PrStatus::Open,
      title: 'New Test PR',
      merged_at: nil,
      number: 54_321,
      updated_at: Time.current,
      head_branch: 'feature_branch',
      base_branch: 'main',
      url: 'https://github.com/test/test/pull/54321',
    )
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:fetch_latest_open_pr).and_return(new_mock_pr)

    # Refresh the page to see the new PR number
    visit current_path
    wait_for_all_ajax_requests

    # Verify new PR number is displayed and Publish button is hidden
    expect(page).to have_text('#54321')
    expect(page).to have_no_text('Publish')

    # 4. Disable and then re-enable PR workflow (to test re-enabling separately)
    wait_for_element_load('[data-ci="enable-pr-workflow-switch"]')
    safe_click('[data-ci="enable-pr-workflow-switch"]')

    wait_for_all_ajax_requests
    expect(page).to have_text('Disable PR Workflow successfully')

    # Re-enable PR workflow (without being prompted for token)
    wait_for_element_load('[data-ci="enable-pr-workflow-switch"]')
    safe_click('[data-ci="enable-pr-workflow-switch"]')

    wait_for_all_ajax_requests

    # Should not show token input again since we already have a token
    # Wait for the success message to appear
    expect(page).to have_text('Enable PR Workflow successfully')
    # Then verify that the token input is not shown
    expect(page).to have_no_text('Provide Access Token')

    # Refresh the page to see the PR number again
    visit current_path
    wait_for_all_ajax_requests

    # Verify PR number is displayed again and Publish button is hidden
    expect(page).to have_text('#54321')
    expect(page).to have_no_text('Publish')

    # 5. Delete token
    wait_for_element_load('[data-ci="delete-token-btn"]')
    safe_click('[data-ci="delete-token-btn"]')

    # Confirm deletion in the modal
    # Wait for the modal to appear and then click the "Delete token" button
    expect(page).to have_text('Delete Access Token')
    find('button', text: 'Delete token').click

    # Add a longer sleep to ensure the deletion completes and UI updates
    wait_for_all_ajax_requests

    expect(page).to have_text('Disable PR Workflow successfully')
    expect(page).to have_no_text('Create PR')

    # Verify PR number is no longer displayed and Publish button is shown again
    expect(page).to have_no_text('#54321')
    expect(page).to have_text('Publish')

    # Verify that enabling PR workflow now requires token input again
    wait_for_element_load('[data-ci="enable-pr-workflow-switch"]')
    safe_click('[data-ci="enable-pr-workflow-switch"]')

    wait_for_element_load('[data-ci="pr-workflow-token-input"]')
    expect(page).to have_text('Provide Access Token')
  end
end
