# typed: false

# @tag: #aml_studio

require 'rails_helper'

describe 'PR Workflow Auto Deployment', :js do
  include_context 'aml_studio_explicit'
  let(:admin) { get_test_admin }
  let(:admin_work_flow) { current_repo.work_flow(admin) }

  let(:local_client) { current_repo.source }

  let(:analyst) { get_test_analyst }
  let(:remote_project) { AmlStudio::Project.create!(name: 'Remote', tenant_id: 2) }
  let(:remote_repo) do
    remote_project.repositories.create!(
      is_production: true,
      owner_id: analyst.id,
      tenant_id: 2,
    )
  end
  let(:client) { proj_repo.source }
  let(:remote_client) { remote_repo.source }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_AUTOSAVE, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_REPO_STATE_V2, true)
    FeatureToggle.toggle_global('aml_studio:status_panel', true)

    AmlStudio::Values::GitProvider.any_instance.stub(:normalize_ssh_url).and_return(remote_repo.storage_path)
    remote_client.init(bare: true)

    AmlStudio::ExternalGit::SetupConnection
      .new(project: project)
      .call('ssh://**************/owner/repo')
    deploy_flow.call

    external_git_integration = AmlStudio::ExternalGitIntegration.create!(
      webhook_secret: 'abcxyz',
      webhook_id: 1,
      repo_url: remote_repo.storage_path,
      provider: 'GitHub',
      token: SourceControl::Backend.passphrase_encryptor.encrypt('abcxyz'),
      token_expiration_date: Time.current + 1.day,
    )

    project.update!(
      external_git_integration_id: external_git_integration.id,
    )

    project.settings = project.settings.merge(enabled_pr_workflow: true)
    project.save!
    admin.update!(dev_mode_enabled: true)
  end

  after do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_AUTOSAVE, false)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, false)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_REPO_STATE_V2, false)
    FeatureToggle.toggle_global('aml_studio:status_panel', false)
  end

  it 'automatically deploys when PR is merged' do
    qlogin(:admin, "/studio/projects/#{project.id}/settings")
    sleep 1
    wait_for_all_ajax_requests

    # create a new develop1 from the develop
    # this branch will be the remote master branch in the future
    local_client.new_branch('develop1', 'develop')
    local_client.checkout_branch('develop1')

    # checkout again to the develop to commit something
    local_client.checkout_branch('develop')
    local_client.write_file('test_from_develop.aml', 'develop')
    local_client.commit_from_wd([], 'Test Commit From Develop')

    admin_work_flow.push

    local_client.checkout_branch('develop1')

    # after merge, develop1 will contain the merge commit of develop1 + develop
    local_client.merge_branch_with_options('develop', { no_ff: true })

    # push that merge commit to remote master branch
    local_client.push_origin(from_branch: 'develop1', to_branch: 'master')
    local_client.checkout_branch('develop')

    pull_request = AmlStudio::Values::PullRequest.new(
      base_branch: project.production_branch,
      head_branch: 'develop',
      merge_commit_sha: remote_client.current_commit_hash,
      updated_at: Time.current,
      url: 'https://github.com/owner/repo/pull/1',
      title: 'Test PR',
      state: AmlStudio::Values::PrStatus::Closed,
      number: 1,
    )
    AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
      project: project,
      parsed_data: {
        pr_data: pull_request,
        merged_pr_event: true,
        action_type: 'closed',
        open_pr_related_event: false,
      },
    ).call

    expect(page).to have_text('Publishing')
    wait_expect(true) { page.text.include?('was merged into master and is being published to production') }
    wait_for_all_ajax_requests
    wait_expect(true) do
      page.text.include?('was merged into master and published to production. Go to Reporting to start exploring')
    end

    # should include the up-to-date since it has run sync remote
    wait_expect(true) do
      page.text.include?('Up-to-date')
    end
  end
end
