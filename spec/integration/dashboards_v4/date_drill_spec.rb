# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'DAC', js: true do
  include_context 'canvas_dashboard'
  before do
    FeatureToggle.toggle_global('interactive_control:date_drill', true)
    FeatureToggle.toggle_global('interactive_control:pop', true)
  end
  context 'Date Drill interactions' do
    let(:params) do
      definition_aml = <<~STR
        Dashboard empty_dashboard {
          title: 'abc'
          block v2: VizBlock {
            label: 'Report'
            viz: DataTable {
              dataset: 'new_dataset'
              fields: [
                VizFieldFull {
                  ref: ref('data_modeling_products', 'name')
                  format {
                    type: 'text'
                  }
                },
                VizFieldFull {
                  ref: ref('data_modeling_products', 'price')
                  format {
                    type: 'number'
                  }
                },
                VizFieldFull {
                  ref: ref('data_modeling_products', 'status')
                  format {
                    type: 'text'
                  }
                },
                VizFieldFull {
                  ref: ref('data_modeling_products', 'created_at')
                  format {
                    type: 'datetime'
                  }
                }
              ]
              settings {
                show_row_number: true
              }
            }
          }
          block d1: DateDrillBlock {
            label: 'Date Drill'
            default: 'year'
          }
          interactions: [
            DateDrillInteraction {
              from: 'd1'
              to: [
                CustomMapping {
                  block: 'v2'
                  field: ref('data_modeling_products', 'created_at')
                }
              ]
            }
          ]
        }
      STR
      blocks = [
        {
          "type"=>"DateDrillBlock",
          "label"=>"Date Drill 1",
          "uname"=>"d1",
          "date_drill"=>
          {
            "label"=>"Date Drill 1",
            "contraints"=>{},
            "input_type"=>nil,
            "filter_type"=>"date_drill",
            "is_shareable"=>false,
            "filter_source"=>
            {
              "manual_options" => [],
              "source_type"=>"ManualFilterSource"
            },
            "default_condition"=> { 'operator' => 'transform_date_drill', 'values' => ['year'] },
          },
        },
        {
            "type"=> "VizBlock",
            "label"=> "",
            "uname"=> "v1",
            "description"=>nil,
            "settings"=>{
              "hide_controls"=>false,
              "hide_labels"=>false,
            },
            "viz"=> {
              "dataset_id"=>data_set.id,
              "viz_setting"=> {
                "fields"=> {
                  "table_fields" => [
                    {"path_hash"=>{"model_id"=> products_model.id, "field_name"=>"name"}},
                    {"path_hash"=>{"model_id"=> products_model.id, "field_name"=>"status"}},
                    {"path_hash"=>{"model_id"=> products_model.id, "field_name"=>"created_at"}},
                  ],
                },
                "filters"=> [],
                "viz_type"=>"data_table",
                "adhoc_fields"=>[],
              },
              "settings"=> {},
            }
          },
      ]

      interactions = [
        {
          "from"=>"d1",
          "to"=>"v1",
          "type"=>"DateDrillInteraction",
          "field_path"=>{ "model_id"=> products_model.id, "field_name"=> "created_at", "data_set_id"=> data_set.id}
        }
      ]

      super().merge({
        definition: super()[:definition].merge({
          blocks: blocks,
          interactions: interactions,
        }),
        definition_aml: definition_aml,
      })
    end
    let!(:canvas_dashboard) do
      create(
        :dashboard,
        **params,
      )
    end
    it 'should not allow map 2 date drill to 1 viz' do
      safe_login(admin, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      safe_click('[data-ci="ci-edit-canvas-dashboard"]')
      wait_for_element_load('#block-v1')
      safe_click('[data-ci="add-others"]')
      safe_click('.ci-add-date-drill')
      wait_for_element_load('.mappings-container .ci-mapping-toggle')
      wait_expect('This Visualization is mapped to "Date Drill 1". To map this Visualization, disable the mapping on that Control first.') do
        page.find('.mappings-container .ci-mapping-toggle', text: 'v1').hover
        wait_for_element_load('.hui-tooltip-floating')
        page.find('.hui-tooltip-floating').text
      end
    end

    it 'prefillses date-drill label' do
      safe_login(admin, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      safe_click('[data-ci="ci-edit-canvas-dashboard"]')
      wait_for_element_load('#block-v1')
      safe_click('[data-ci="add-others"]')
      safe_click('.ci-add-date-drill')

      expect(page.find('.ci-filter-label-input').value).to eq('Date Drill')
    end

    it 'prefillses pop label' do
      safe_login(admin, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      wait_for_element_load('.h-dashboard-container')
      safe_click('[data-ci="ci-edit-canvas-dashboard"]')
      wait_for_element_load('.h-dashboard-container')
      safe_click('[data-ci="add-others"]')
      safe_click('.ci-add-pop')

      expect(page.find('.ci-filter-label-input').value).to eq('Period Comparison')
    end
  end
end
