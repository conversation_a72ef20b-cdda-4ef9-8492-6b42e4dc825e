require 'rails_helper'

describe 'save visualization to canvas dashboard', :js do
  include_context 'edit_production_dashboard'

  let(:explorer) do
    FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
    get_test_explorer
  end
  let(:block_title) { 'Viz block from dataset' }

  before do
    FeatureToggle.toggle_global(Permission::FT_EXPLORER_EDIT_DASHBOARD, true)
    FeatureToggle.toggle_global(Permission::FT_COMPOUND_ACTIONS, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EDIT_IN_PRODUCTION, true)

    admin.share(explorer, :read, allowable_dataset)
    admin.share(explorer, :crud, live_dashboard)
    admin.share(explorer, :read, empty_dashboard)
  end

  it 'can save dataset exploration to canvas dashboard' do
    qlogin(explorer, "/datasets/#{allowable_dataset.id}")

    select_viz_field('users', 'id')
    safe_click('#ci-save-result-as')
    wait_for_element_load('.ci-title input')
    page.first('.ci-title input').set(block_title)

    select_h_select_option('.resource-treeselect', label: live_dashboard.title)
    page.first('.ci-save-report-from-explore-modal').click
    wait_for_element_load('[data-ci="open-new-report"]')

    live_dashboard.reload
    definition = live_dashboard.definition_struct(reload: true)
    expect(definition.viz_blocks.any? { |b| b.label == block_title }).to be(true)
  end

  context 'dashboard with custom interaction' do
    let(:live_dashboard_content) do
      <<~AML
        Dashboard live_dashboard {
          title: 'Live'
          description: ''''''

          view: CanvasLayout {
            label: 'View 1'
            height: 840
            grid_size: 20
            block allowable_viz {
              position: pos(60, 180, 400, 300)
              layer: 1
            }
            block unallowable_viz {
              position: pos(640, 180, 400, 300)
              layer: 1
            }
            block allowable_filter {
              position: pos(80, 20, 300, 100)
            }
            block unallowable_filter {
              position: pos(660, 20, 300, 100)
            }
            block text {
              position: pos(80, 520, 380, 120)
              layer: 2
            }
          }

          theme: H.themes.vanilla
          block allowable_viz: VizBlock {
            label: 'allowable viz'
            viz: DataTable {
              dataset: dataset_users
              fields: [
                VizFieldFull {
                  ref: ref('users', 'id')
                  format {
                    type: 'number'
                    pattern: 'inherited'
                  }
                }
              ]
              settings {
                show_row_number: true
                aggregate_awareness {
                  enabled: true
                  debug_comments: true
                }
              }
            }
          }
          block unallowable_viz: VizBlock {
            label: 'unallowable viz'
            viz: DataTable {
              dataset: dataset_products
              fields: [
                VizFieldFull {
                  ref: ref('products', 'id')
                  format {
                    type: 'number'
                    pattern: 'inherited'
                  }
                }
              ]
              settings {
                show_row_number: true
                aggregate_awareness {
                  enabled: true
                  debug_comments: true
                }
              }
            }
          }
          block allowable_filter: FilterBlock {
            label: 'allowable filter block'
            type: 'field'
            source: FieldFilterSource {
              dataset: dataset_users
              field: ref('users', 'id')
            }
            default {
              operator: 'is'
              value: []
            }
          }
          block unallowable_filter: FilterBlock {
            label: 'unallowable filter block'
            type: 'field'
            source: FieldFilterSource {
              dataset: dataset_products
              field: ref('products', 'id')
            }
            default {
              operator: 'is'
              value: []
            }
          }
          block text: TextBlock {
            content: @md Text block;;
          }
          interactions: [
            FilterInteraction {
              from: 'unallowable_filter'
              to: [
                CustomMapping {
                  block: 'unallowable_viz'
                  disabled: true
                }
              ]
            }
          ]
        }
      AML
    end

    it 'works' do |ex|
      qlogin(explorer, "/datasets/#{allowable_dataset.id}")

      select_viz_field('users', 'id')
      safe_click('#ci-save-result-as')
      wait_for_element_load('.ci-title input')
      page.first('.ci-title input').set(block_title)

      select_h_select_option('.resource-treeselect', label: live_dashboard.title)
      page.first('.ci-save-report-from-explore-modal').click
      wait_for_element_load('[data-ci="open-new-report"]')

      new_dashboard_content = proj_work_flow.read_file('live.page.aml', branch_name: project.production_branch)

      SnapshotTest.test!(new_dashboard_content[:content], rspec_example: ex,
                                                          snapshot_name: 'save_visualization_to_dashboard_with_custom_interaction.aml',)
    end
  end
end
