# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'drill down on canvas dashboard', js: true do
  include_context 'test_tenant'
  include_context 'data_set'

  before do
    FeatureToggle.toggle_global('viz_result:show_context_menu_on_data_table', true)
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global('drill_features:drill_down', true)
    FeatureToggle.toggle_global('drillthrough:enabled', true)
    FeatureToggle.toggle_global('dac:drillthrough', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    ThreadContext.set(:current_user, admin)
  end

  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/drill_down'
    end
  end

  let(:dashboard) do
    deploy_result # make sure deployment is already done
    Dashboard.find_by!(uname: 'drill_down_dashboard')
  end
  let(:dashboard_url) { dashboard_path(dashboard) }

  def verify_suggested_drill_fields(drill_field_labels)
    suggested_drill_fields = page.find_all('[data-ci="ci-suggested-drill-field"]')
    wait_expect(suggested_drill_fields.length) { drill_field_labels.length }
    drill_field_labels.each_with_index do |label, index|
      wait_expect(suggested_drill_fields[index].text) { label }
    end

    suggested_drill_fields
  end

  def select_drill_field(suggested_drill_fields, index = 0)
    safe_click_element(suggested_drill_fields[index])
    wait_for_all_ajax_requests
    wait_for_viz_load
  end

  def verify_drill_info_tooltips(selector, expected_tooltips)
    drill_info_tooltips = page.find_all("#{selector} [data-ci=\"ci-drill-info-tooltip\"]")
    wait_expect(drill_info_tooltips.length) { expected_tooltips.length }

    expected_tooltips.each_with_index do |tooltip_text, i|
      wait_expect(drill_info_tooltips[i].text) { tooltip_text }
    end

    drill_info_tooltips
  end

  def reset_drill(selector)
    safe_click("#{selector} [data-ci=\"ci-drill-down-reset-btn\"]")
    wait_for_viz_load
  end

  context 'with line chart' do
    def perform_highchart_drill_down(viz_selector, need_scroll: true)
      wait_for_viz_load
      wait_for_element_load(viz_selector)
      scroll_to_js(viz_selector) if need_scroll
      viz = page.find_by_id(viz_selector.delete_prefix('#'))

      retry_until_success(retries: 3) do
        cell = viz.all('.highcharts-series .highcharts-point')[0]
        cell.right_click
        safe_click('.ci-drill-down')
      end
    end

    it 'works' do
      safe_login(admin, dashboard_url)
      wait_for_all_ajax_requests

      perform_highchart_drill_down('#block-column_chart')

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nStatus",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_expect(page.find('#block-column_chart .dac-viz-block-label').text) { 'Sum of Price by Products Status' }

      verify_drill_info_tooltips('#block-column_chart', [
        'Original',
        'Products.Name: bread',
      ],)

      # keep showing drill state on expanded block
      wait_for(true) do
        page.find_by_id('block-column_chart').hover
        safe_click('[data-ci="expand-block-btn"]')
      rescue StandardError => e
        e.inspect
      end

      verify_drill_info_tooltips('.ci-expanded-block', [
        'Original',
        'Products.Name: bread',
      ],)

      wait_for_viz_load

      expect(page.find('.ci-expanded-block .highcharts-xaxis-labels').text).to eq("available\nexpired")
      expect(page.find('.ci-expanded-block .highcharts-xaxis .highcharts-axis-title').text).to eq('Products.Status')

      safe_click('[data-ci="ci-drill-info-tooltip"]', text: 'Original')

      wait_for_viz_load

      expect(page.find('.ci-expanded-block .highcharts-xaxis-labels').text).to eq("bread\negg\nmilk")
    end
  end

  context 'with pie chart', skip: 'somehow fails on CI' do
    def verify_chart_result(expected_title, expected_legends)
      wait_expect(page.find('#block-pie_chart .dac-viz-block-label').text) { expected_title }

      legends = page.all('#block-pie_chart .highcharts-legend-item.highcharts-pie-series > span')&.map(&:text)
      wait_expect(legends) { expected_legends }
    end

    it 'works' do
      safe_login(admin, dashboard_url)
      wait_for_all_ajax_requests

      # first drill
      perform_highchart_drill_down('#block-pie_chart', need_scroll: false)

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nStatus",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      verify_chart_result('Count of Id by Products Status', ['available Count of Id', 'expired Count of Id'])

      verify_drill_info_tooltips('#block-pie_chart', [
        'Original',
        'Products.Name: bread',
      ],)

      # second drill
      perform_highchart_drill_down('#block-pie_chart', need_scroll: false)

      suggested_drill_fields_2nd = verify_suggested_drill_fields([
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields_2nd, 1)

      verify_drill_info_tooltips('#block-pie_chart', [
        'Original',
        'Products.Name: bread',
        'Products.Status: available',
      ],)

      verify_chart_result('Count of Id by Products Created At', ['2019-08-09 00:00:00 Count of Id'])

      reset_drill('#block-pie_chart')

      verify_chart_result('Count of Id by Name', ['bread Count of Id', 'egg Count of Id', 'milk Count of Id'])
    end
  end

  context 'with pivot table' do
    def perform_pivot_drill_down(viz_selector, cell_selector: nil, cell_element: nil, need_scroll: true)
      wait_for_viz_load
      wait_for_element_load(viz_selector)
      scroll_to_js(viz_selector) if need_scroll
      viz = page.find_by_id(viz_selector.delete_prefix('#'))

      retry_until_success(retries: 3) do
        cell = cell_element || viz.all(cell_selector)[0]
        cell.right_click
        safe_click('.ci-drill-down')
      end
    end

    def perform_pivot_row_header_drill_down(viz_selector, row_index:, col_id:, need_scroll: true)
      cell_selector = "[row-index=\"#{row_index}\"] [col-id=\"#{col_id}\"]"
      perform_pivot_drill_down(viz_selector, cell_selector: cell_selector, need_scroll: need_scroll)
    end

    def perform_pivot_column_header_drill_down(viz_selector, col_id:, need_scroll: true)
      cell_selector = ".ag-header-group-cell[col-id='#{col_id}']"
      perform_pivot_drill_down(viz_selector, cell_selector: cell_selector, need_scroll: need_scroll)
    end

    def perform_pivot_cell_value_drill_down(viz_selector, row_id:, col_index:, need_scroll: true)
      pivot_table_element = page.find("#{viz_selector} [data-ci=\"ci-ag-grid-pivot-table\"]")
      cell_element = find_cell_element(table_element: pivot_table_element, row_id: row_id, col_index: col_index)
      perform_pivot_drill_down(viz_selector, cell_element: cell_element, need_scroll: need_scroll)
    end

    def verify_row_header_titles(expected_titles)
      row_header_titles = page.all('.ag-pinned-left-header .ag-header-row .ag-header-cell')
                              .sort_by { |e| e['aria-colindex'] }
                              .map(&:text)

      wait_expect(row_header_titles) { expected_titles }
    end

    def verify_column_header_titles(expected_titles)
      column_header_titles = page.all('.ag-pinned-left-header .ag-header-row .ag-header-group-cell')
                                 .sort_by { |e| e['aria-colindex'] }
                                 .map(&:text)

      wait_expect(column_header_titles) { expected_titles }
    end

    before do
      safe_login(admin, "#{dashboard_url}?_t=tab2")
      wait_for_all_ajax_requests
    end

    it 'drills on first row header' do
      perform_pivot_row_header_drill_down('#block-pivot_table', row_index: 0, col_id: '!rows[0]')

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to rows (after created at), keep columns
      verify_row_header_titles(['Month Created at', 'Name', 'Status'])
      verify_column_header_titles(['Id', 'Merchant Id'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Created at: Aug 2019',
      ],)
    end

    it 'drills on 2nd row header' do
      perform_pivot_row_header_drill_down('#block-pivot_table', row_index: 1, col_id: '!rows[1]')

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to rows, keep columns
      verify_row_header_titles(['Month Created at', 'Status', 'Name'])
      verify_column_header_titles(['Id', 'Merchant Id'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Created at: Aug 2019, Products.Status: available',
      ],)
    end

    it 'drills on first column header' do
      perform_pivot_column_header_drill_down('#block-pivot_table', col_id: '1_0')

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to columns (after Id), keep rows
      verify_column_header_titles(['Id', 'Name', 'Merchant Id'])
      verify_row_header_titles(['Month Created at', 'Status'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Id: 1',
      ],)
    end

    it 'drills on 2nd column header' do
      perform_pivot_column_header_drill_down('#block-pivot_table', col_id: '1->1_0')

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to columns, keep rows
      verify_column_header_titles(['Id', 'Merchant Id', 'Name'])
      verify_row_header_titles(['Month Created at', 'Status'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Id: 1, Products.Merchant Id: 1',
      ],)
    end

    it 'drills on subtotal value cell' do
      perform_pivot_cell_value_drill_down('#block-pivot_table', row_id: 0, col_index: 4)

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to rows (after created at), keep columns
      verify_row_header_titles(['Month Created at', 'Name', 'Status'])
      verify_column_header_titles(['Id', 'Merchant Id'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Created at: Aug 2019',
      ],)
    end

    it 'drills on column total value cell' do
      perform_pivot_cell_value_drill_down('#block-pivot_table', row_id: 'b-0', col_index: 3)

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to columns, keep rows
      verify_column_header_titles(['Id', 'Merchant Id', 'Name'])
      verify_row_header_titles(['Month Created at', 'Status'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Id: 1, Products.Merchant Id: 1',
      ],)
    end

    it 'drills on normal value cell' do
      perform_pivot_cell_value_drill_down('#block-pivot_table', row_id: 1, col_index: 3)

      suggested_drill_fields = verify_suggested_drill_fields([
        "Products\nName",
        "Categories\nName",
        "Products\nCreated at",
      ])

      select_drill_field(suggested_drill_fields, 0)

      wait_for_viz_load

      # verify pivot result: add drill dimension to rows, keep columns
      verify_row_header_titles(['Month Created at', 'Status', 'Name'])
      verify_column_header_titles(['Id', 'Merchant Id'])

      verify_drill_info_tooltips('#block-pivot_table', [
        'Original',
        'Products.Created at: Aug 2019, Products.Status: available, Products.Id: 1, Products.Merchant Id: 1',
      ],)
    end
  end
end
