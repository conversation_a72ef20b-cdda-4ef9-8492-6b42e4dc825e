# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'Canvas Dashboard Data Alerts', :js do
  include_context 'canvas_dashboard'
  let(:email_dest) { create(:email_dest, title: '<PERSON><PERSON> Dest', recipients: ['<EMAIL>']) }
  let!(:slack_dest) do
    create(:slack_dest, slack_channels: [
      { id: 'chat-to-someone', name: 'chat-to-someone', type: 'public' },
    ],)
  end
  let(:schedule) { create(:schedule, repeat: '* * * * *', paused: true) }
  let!(:slack_data_alert) do
    create(
      :data_alert,
      tenant: admin.tenant,
      creator: admin,
      source_id: "#{canvas_dashboard.id}:v1",
      source_type: 'VizBlock',
      dest: slack_dest,
      schedule: schedule,
    )
  end
  let!(:viz_condition) do
    create(
      :viz_condition,
      source_id: slack_data_alert.id,
      source_type: DataAlert.to_s,
      field_path: {
        model_id: products_model.id,
        field_name: 'status',
      },
      condition: {
        operator: 'is',
        values: ['expired'],
      },
      transformation: nil,
      user_id: admin.id,
      tenant_id: tenant.id,
    )
  end

  let(:team_info) do
    {
      slack_settings: {
        slack_team_id: 'T01EGCAN5C2',
        slack_enabled: true,
      },
      slack_team_info: {
        id: 'T01EGCAN5C2',
        name: 'Holistics Test',
        url: 'https://holisticstestgroup.slack.com/',
        domain: 'holisticstestgroup',
        email_domain: 'holistics.io',
        icon: {
          image_default: true,
          image_34: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-34.png',
          image_44: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-44.png',
          image_68: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-68.png',
          image_88: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-88.png',
          image_102: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-102.png',
          image_230: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-230.png',
          image_132: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-132.png',
        },
        avatar_base_url: 'https://ca.slack-edge.com/',
        is_verified: false,
        lob_sales_home_enabled: false,
      },
    }
  end

  before do
    admin.nux[:seen_features] = ['dac-controls-panel']
    admin.save!
    FeatureToggle.toggle_global('integrations:slack', true)
    update_tenant_slack_settings
    allow(Slack::SlackService).to receive(:get_team_info).and_return(team_info)
  end

  it 'works' do
    safe_login(admin, dashboard_path(canvas_dashboard))
    wait_for_element_load('#block-v1')
    page.find_by_id('block-v1').hover
    page.find('[data-ci="ci-more-dropdown"]').click
    page.find('div.cursor-pointer', text: 'Data Alerts').click
    wait_for_all_ajax_requests
    expect(page.first('.data-alert-list .h-table-wrapper').text).to eq("Alert\nCheck at\nDestination\nInfo\ndata alert title\nPaused\nchat-to-someone")
    page.find('button.ci-actions').click
    page.find('div.cursor-pointer', text: 'Edit').click
    wait_for_element_load('.data-alert-modal .channels-dropdown')
    page.find('button', text: 'Save').click
    wait_for_all_ajax_requests
  end

  context 'png option in slack dest' do
    let(:slack_auth_version) do
      SlackAuthorization::VERSION_1
    end

    before do
      FactoryBot.create :slack_authorization, user_id: user.id, version: slack_auth_version
      update_tenant_slack_settings
    end

    before do
      FeatureToggle.toggle_global(SlackDest::FT_ENABLE_CUSTOM_SLACK_DYNAMIC_MESSAGE, true)
    end

    it 'Update correctly option in slack dest' do
      safe_login(admin, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      page.find('#block-v1').hover
      page.find('[data-ci="ci-more-dropdown"]').click
      page.find('div.cursor-pointer', text: 'Data Alerts').click
      wait_for_all_ajax_requests
      safe_click('button.ci-actions')
      safe_click('div.cursor-pointer', text: 'Edit')

      # Update the option to hide png option
      wait_for_element_load('.data-alert-modal .channels-dropdown')
      safe_click('.ci-attach-png')
      page.find('button', text: 'Save').click
      wait_for_all_ajax_requests

      safe_click('button.ci-actions')
      safe_click('div.cursor-pointer', text: 'Edit')
      wait_for_element_load('.data-alert-modal .channels-dropdown')

      wait_expect(true) do
        page.find('.ci-attach-png')['data-hui-checked'] == 'false'
      end
    end
  end

  context 'can use dynamic message for data alert' do
    before do
      FeatureToggle.toggle_global(SlackDest::FT_ENABLE_CUSTOM_SLACK_DYNAMIC_MESSAGE, true)
    end

    it 'should works' do
      safe_login(admin, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      page.find('#block-v1').hover
      page.find('[data-ci="ci-more-dropdown"]').click
      page.find('div.cursor-pointer', text: 'Data Alerts').click
      wait_for_all_ajax_requests
      expect(page.first('.data-alert-list .h-table-wrapper').text).to eq("Alert\nCheck at\nDestination\nInfo\ndata alert title\nPaused\nchat-to-someone")
      page.find('button.ci-actions').click
      page.find('div.cursor-pointer', text: 'Edit').click
      wait_for_element_load('.data-alert-modal .channels-dropdown')
      page.find('button', text: 'Save').click
      wait_for_all_ajax_requests
    end
  end
end
