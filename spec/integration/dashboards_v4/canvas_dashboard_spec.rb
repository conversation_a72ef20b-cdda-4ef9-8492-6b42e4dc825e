# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'In Canvas Dashboard user', js: true do
  include_context 'canvas_dashboard'
  let(:explorer) { get_test_explorer }
  let(:viewer) { get_test_user }
  before do
    FeatureToggle.toggle_global('user:explorer_role', true)
    FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
    FeatureToggle.toggle_global('viz:table_v2', true)
    admin.share(explorer, :read, canvas_dashboard)
    admin.share(viewer, :read, canvas_dashboard)
    admin.nux[:seen_features] = ['dac-controls-panel']
    admin.save!
    viewer.nux[:seen_features] = ['dac-controls-panel']
    viewer.save!
    explorer.nux[:seen_features] = ['dac-controls-panel']
    explorer.save!
  end
  context 'viewer' do
    it 'can load dashboard' do
      safe_login(viewer, dashboard_path(canvas_dashboard))
      wait_for_element_load('.h-dashboard-container')
      wait_for_element_load('#block-v1 .ci-viz-result')
      expect(page.has_css?('#block-v1 .ci-viz-result'))
    end

    it 'can check mapped filters, sort table' do
      safe_login(viewer, dashboard_path(canvas_dashboard))
      wait_for_element_load('.h-dashboard-container')
      wait_for_element_load('#block-v1 .ci-viz-result')
      page.find('#block-v1').hover
      wait_for_element_load('[data-ci="block-v1-controls"]')

      # filters mapping
      page.find('.ci-widget-conditions-trigger').hover

      # sort
      reset_ajax_requests
      count = 0
      safe_click('[data-ci="ci-table-sort"]') while !page.has_css?('[data-ci="ci-add-sort-btn"]') && count < 3

      safe_click('[data-ci="ci-add-sort-btn"]')
      select_h_select_option('.ci-sort-column-select', label: 'Name')
      wait_expect(1) do
        extract_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }).size
      end
    end

    it 'should display export button in dashboard metadata' do
      safe_login(viewer, dashboard_path(canvas_dashboard))
      wait_for_element_load('.h-dashboard-container')
      expect(page).to have_selector('.dac-metadata [data-ci="ci-export-dropdown"]')
    end
  end

  context 'explorer' do
    it 'can load dashboard' do
      safe_login(explorer, dashboard_path(canvas_dashboard))
      wait_for_element_load('.h-dashboard-container')
      wait_for_element_load('#block-v1 .ci-viz-result')
      expect(page.has_css?('#block-v1 .ci-viz-result'))
    end
  end

  context 'dashboard auto run' do
    let(:fstate) {
      state = DynamicFilterState.new(
        data: [
          {
            "dynamic_filter_id": filter_block.id,
            "selected_condition": {
              "modifier": nil,
              "operator": "is",
              "values": ['test']
            }
          }
        ],
        tenant: admin.tenant
      )
      state.save!
      state
    }
    context 'is disabled' do
      before do
        canvas_dashboard.definition['settings']['autorun'] = false
        canvas_dashboard.save!
      end
      it 'should run after pressing button if it is disabled' do
        safe_login(admin, dashboard_path(canvas_dashboard))
        wait_for_all_ajax_requests

        expect(page.find('[data-ci="ci-viz-block-body-placeholder"]')).to be_visible
        expect(page.find('[data-ci="ci-load-dashboard"]')).to be_visible
        page.find('[data-ci="ci-load-dashboard"]').click
        expect(page.find('.ci-viz-result')).to be_visible
      end

      it 'should run if fstate is present' do
        safe_login(admin, "#{dashboard_path(canvas_dashboard)}?_fstate=#{fstate.hashid}")

        wait_for_all_ajax_requests

        expect(page.has_css?('[data-ci="ci-load-dashboard"]')).to eq(false)
        expect(page.has_css?('.result-viz')).to eq(true)
      end
    end
  end
end
