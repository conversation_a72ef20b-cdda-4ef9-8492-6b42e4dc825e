# typed: false
require 'rails_helper'

describe 'report deleting spec', js: true, stable: true do
  before do
    FeatureToggle.toggle_global('new_navigation_node', true)
    @report = FactoryBot.create :query_report, title: 'Test report'
  end

  it 'can be deleted from left nav' do
    qlogin :admin, '/home'
    wait_for_element_load('.node-explorer')

    page.find('#navigation-node-QueryReport-1').hover
    safe_click('.ci-navigation-node-action')
    safe_click('.ci-delete')
    safe_click('.ci-confirm-delete')
    expect_notifier_content(/deleted successfully/)
  end
end
