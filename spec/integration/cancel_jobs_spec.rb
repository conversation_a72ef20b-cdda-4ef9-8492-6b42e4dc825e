# typed: false
require 'rails_helper'

describe 'cancel Jobs', js: true, stable: true do
  let(:admin) {get_test_admin}
  let(:tenant) {get_test_tenant}


  let! (:transform) { FactoryBot.create :data_transform, tenant: tenant, data_source: nil }
  let! (:dashboard) { FactoryBot.create :dashboard, tenant: tenant, title: 'Test Dashboard' }
  let! (:query_report) { FactoryBot.create :query_report }

  let! (:user) { FactoryBot.create :user }

  let! (:job1) { FactoryBot.create :job, source_type: transform.class.name, status: 'created' }
  let! (:job2) { FactoryBot.create :job, source_type: dashboard.class.name, status: 'created' }
  let! (:job3) { FactoryBot.create :job, source_type: dashboard.class.name, status: 'created' }
  # let! (:job4) { FactoryBot.create :job, source_type: query_report.class.name, status: 'created' }
  # let! (:job5) { FactoryBot.create :job, source_type: query_report.class.name, status: 'created' }

  it 'remove all dashboard created jobs' do
    qlogin(admin, '/manage/jobs')
    wait_for_element_load '.ci-cancel-job'
    select_h_select_option('.ci-source-type', value: 'Dashboard')
    select_h_select_option('.ci-status-select', value: '1,2,6,7')
    page.first('.ci-cancel-job').click

    wait_for_element_load '.ci-refresh:not(.disabled)'
    click_button('Refresh')

    a = page.find('.ci-table-list-jobs')
    a.all('tbody tr').each do |tr|
      wait_expect('Cancelled') { tr.first('.ci-status').text }
    end
  end

  it 'remove all created jobs' do
    qlogin(admin, '/manage/jobs')
    wait_for_element_load '.ci-cancel-job'
    select_h_select_option('.ci-status-select', value: '1,2,6,7')
    page.first('.ci-cancel-job').click

    wait_for_element_load '.ci-refresh:not(.disabled)'
    click_button('Refresh')

    wait_for_element_load '.ci-table-list-jobs'
    a = page.find('.ci-table-list-jobs')
    a.all('tbody tr').each do |tr|
      wait_expect('Cancelled') { tr.first('.ci-status').text }
    end
  end
end
