# typed: false

require 'rails_helper'

describe 'recent and favorite items do', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let!(:category) { FactoryBot.create :report_category, name: 'aihhi' }
  let!(:report) { FactoryBot.create :query_report, title: 'report', category: category }
  let!(:dashboard) { FactoryBot.create :dashboard, title: 'dashboard', category: category }

  def expect_no_favorite_items(click_header_menu: true)
    # no favorite on homepage
    expect(page).to_not have_content('Favorites')

    # no favorite in fav and recent header section
    safe_click('.recent-fav-wrapper')
    wait_for_element_load('.ci-fav-items') if click_header_menu
    expect(page).to have_content('No favorites item')
  end

  def expect_no_recent_items(click_header_menu: false)
    # no recent on homepage
    expect(page).to have_content('No dashboards viewed. Dashboard is a collection of widgets that help you tell a story, or paint a big picture of your organization.')

    # no recent in fav and recent header section
    safe_click('.recent-fav-wrapper') if click_header_menu
    wait_for_element_load('.ci-recent-items')
    expect(page).to have_content('No recents item')
  end

  def add_or_remove_items_from_fav
    visit query_report_path(report)
    # when the report is loaded, there will be more buttons on the header that push the favorite button away
    # so we should wait until the report is loaded and the header is stabilized.
    wait_for_element_load('.ci-table-report-data')
    safe_click('.ci-fav')
    wait_for_all_ajax_requests

    visit dashboard_path(dashboard)
    wait_for_all_holistics_loadings
    wait_for_all_ajax_requests

    safe_click('.ci-fav')
    wait_for_all_ajax_requests
  end

  def expect_have_favorite_and_recent
    visit '/home'
    wait_for_element_load('.ci-fav-section')

    expect(page.find('.ci-fav-section')).to have_content(report.title)
    expect(page.find('.ci-recent-section')).to have_content(report.title)

    safe_click('.recent-fav-wrapper')
    expect(page.find('.ci-fav-items')).to have_content(report.title)
    expect(page.find('.ci-recent-items')).to have_content(report.title)
  end

  it 'successfully added to favorite and recent then removed from favorite', otel: true do
    safe_login(admin, '/home')
    expect_no_favorite_items

    # add to favorite
    add_or_remove_items_from_fav
    expect_have_favorite_and_recent
    # removed from favorite
    add_or_remove_items_from_fav
    visit '/home'
    expect_no_favorite_items

    # test otel span attributes
    toggle_fav = otel_finished_spans.find { |s| s.name == 'FavouritesController#toggle' }
    instrumented_params = JSON.parse(toggle_fav.attributes['h.params']).rsk
    expect(instrumented_params[:favourite]).to eq({ source_id: 1, source_type: 'QueryReport' })
    expect(toggle_fav.attributes['h.tenant_id']).to eq(admin.tenant_id.to_s)
    expect(toggle_fav.attributes['h.user_id']).to eq(admin.id.to_s)
  end

  it 'successfully removed from favorite and recent when item is deleted' do
    safe_login(admin, '/home')
    expect_no_favorite_items

    add_or_remove_items_from_fav
    expect_have_favorite_and_recent

    # delete folder containing report and dashboard
    visit '/home'
    wait_for_element_load('.report-container')
    page.find('.ci-public-workspace').click
    wait_for_element_load('.node-browse')
    page.first('.ci-actions').click
    page.first('.ci-delete').click
    page.first('.ci-confirm-delete').click
    wait_for_element_load('.modal-delete-category')
    page.first('.ci-delete-mode').click
    fill_text('.ci-confirm-field', 'DELETE')
    page.find('.ci-submit').click

    visit '/home'
    expect_no_favorite_items
    expect_no_recent_items(click_header_menu: false)
  end
end
