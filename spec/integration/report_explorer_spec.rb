# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'report explorer', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:category) { create :report_category, name: 'Parent' }
  let!(:query_report) { create :query_report, title: 'Report', query: 'select 1', category: category }
  let!(:dashboard) { create :dashboard }
  let!(:metric_sheet) { create :metric_sheet }

  before do
    FeatureToggle.toggle_global('reporting_nav:enabled', true)
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
  end

  def count_navigation_nodes
    page.all('.ci-navigation-node').count
  end

  describe 'report explorer node count' do
    it 'shows the correct number of nodes' do
      safe_login(admin, '/browse')

      # Section public + Section shared with me + Section private + Metrics Root + Reports & Dashboards Root + 2 Report Categories
      wait_for_element_load '.ci-navigation-node'
      wait_expect(7) { count_navigation_nodes }
    end

    context 'with ds.is_sample' do
      before do
        ds.update!(is_sample: true)
        DataSource.all.each { |x| x.destroy unless x.is_sample }
      end

      it 'shows the correct number of nodes' do
        safe_login(admin, '/browse')

        # Section public + Section shared with me + Section private + 4 previous nodes + 1 metric and 1 report
        wait_for_element_load '.ci-navigation-node'
        wait_expect(9) { count_navigation_nodes }
      end
    end
  end
end
