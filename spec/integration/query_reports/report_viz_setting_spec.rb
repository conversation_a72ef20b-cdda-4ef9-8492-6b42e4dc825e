# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Report\'s viz setting', js: true do
  include_context 'test_tenant'
  let(:pivot_table_report) { FactoryBot.create :pivot_table_report }
  let(:data_table_report) { FactoryBot.create :data_table_report }

  def edit_and_save_report
    safe_click('.ci-edit-report-link')
    sleep 5
    safe_click('.ci-btn-run')
    wait_for_element_load('.result-toolbar')
    page.first('.ci-viz-type-select').click
    safe_click('.ci-viz-type-column_chart')
    safe_click('.ci-save-report')
  end

  it 'should be cleaned after updating viz type' do
    safe_login admin, query_report_path(pivot_table_report)
    edit_and_save_report
    sleep 2
    expected_settings = {
      'misc' => { 'custom_color_list'=>[{}], 'pagination_size' => 25, 'row_limit' => nil, 'show_row_number' => true },
      'others' => { 'always_display_points' => false, 'connect_discontinuous_points' => true, 'include_empty_children_rows' => false },
      'pop_settings' => nil,
      'quick_pivot' => false,
      'sort' => {},
      'legend' => { 'enabled' => true, 'alignment' => 'bottom' },
      'x_axis' => { 'title' => nil, 'show_null_datetime' => false },
      'y_axes' =>
      [{ 'max' => nil,
         'min' => nil,
         'reversed_stack' => false,
         'align' => 'left',
         'title' => nil,
         'scale_type' => 'linear',
         'stack_type' => 'normal',
         'stack_series' => false,
         'maximum_groups' => 5,
         'group_long_tail' => false,
         'show_data_label' => false,
         'show_group_total' => false,
         'show_stack_total' => false,
         'show_series_percentage' => false,
         'hidden' => false,}],
    }
    updated_report = QueryReport.find(pivot_table_report.id)
    expect(updated_report.viz_setting.settings).to eq(expected_settings)
  end

  it 'viz setting form\'s format should update after add new columns' do
    safe_login admin, query_report_path(data_table_report)

    safe_click '.ci-edit-report-link'

    safe_click '.ci-btn-run'

    wait_for_element_load '.viz-setting-form'

    safe_click '.viz-setting-tabs .ci-tab-toggle', text: 'Format'

    search_h_select('.viz-setting-tabs .h-tab.active .viz-section:first-child .format-picker', text: 'Currency')
    select_h_select_option('.viz-setting-tabs .h-tab.active .viz-section:first-child .format-picker', value: '{\\"pattern\\":\\"[$$]#,###0.00\\"}')

    expect(page.find('.h-tab.active').text).to eq("foo\nType\nNumber\nFormat\n$1,234.12")

    ace_set_text('#awesome-query-editor', 'select 1 as foo, 2 as bar')

    safe_click '.ci-btn-run'

    wait_for_element_load '.ci-viz-result '

    expect(page.find('.h-tab.active').text).to eq("foo\nType\nNumber\nFormat\n$1,234.12\nbar\nType\nNumber\nFormat\nInherited from Modeling")
  end
end
