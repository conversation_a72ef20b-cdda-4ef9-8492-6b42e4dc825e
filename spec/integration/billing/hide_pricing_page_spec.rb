# typed: false
require 'rails_helper'

describe 'Billing: Hide pricing page', js: true, stable: true do
  let(:tenant) { get_test_tenant }
  let(:admin) { users(:admin) }
  let(:growth_admin) {
    FactoryBot.create :user,
                        tenant: tenant,
                        role: User::ROLE_GROWTH_ADMIN
  }
  let(:group_version) { 69 }
  let!(:std_plan) { FactoryBot.create :plan_standard_v4, group_version: group_version }
  let!(:tsub) {
    FactoryBot.create :tenant_subscription,
                       tenant: tenant,
                       status: 'trial.active',
                       plan: std_plan,
                       expired_at: Time.now + 10.days
  }

  before do
    FeatureToggle.toggle_global('billing:hide_in_app_pricing', true)
  end

  describe 'Client side' do
    it 'should hide pricing page' do
      pricing_group = FactoryBot.create :pricing_group
      tsub.plan.update!(pricing_group: pricing_group)
      qlogin(admin, '/manage/billing')
      wait_for_element_load '.ci-non-self-serve-info'
    end
  end

  # TODO:  New billing page doesn't support the internal page yet
  describe 'Internal side' do
    xit 'should show pricing page' do
      qlogin(growth_admin, "/admin/tenants/#{tenant.id}/billing_usage")
      wait_for_element_load '.ci-subs-info'
      page.should have_selector :css, '.ci-subs-info'
    end
  end
end
