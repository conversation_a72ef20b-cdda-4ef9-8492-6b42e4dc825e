# typed: false
require 'rails_helper'

describe 'Billing: Freemium expired modal', js: true, stable: true do
  let(:tenant) { get_test_tenant }
  let(:admin) { users(:admin) }
  let!(:tsub) {
    FactoryBot.create :tenant_subscription,
                       tenant: tenant,
                       status: 'trial.expired',
                       expired_at: Time.now
  }
  let!(:std_plan) { FactoryBot.create :plan_standard_v4 }
  let!(:entry_plan) { FactoryBot.create :plan_standard_v4, name: 'entry' }

  before do
    FeatureToggle.toggle_global('billing:freemium_expired_modal', true)
    FeatureToggle.toggle_global('data_models:manager', true)

    GlobalConfig.set('billing:latest_group', 4)
    PricingGroup::latest_group.update!(pricing_page_url: 'https://www.holistics.io/pricing')
    VcrHelper.ignore_hosts('127.0.0.1', 'localhost')
  end

  it 'should not show expired modal in /billing route' do
    qlogin(admin, '/manage/billing')
    expect(page).to_not have_selector('.trial-expired')
    expect(page).to_not have_content('Oops! Looks like your trial has expired!')
  end

  it 'should show expired modal in other routes' do
    qlogin(admin, '/')
    expect(page).to have_selector('.trial-expired')
    expect(page).to have_content('Oops! Looks like your trial has expired!')

    visit '/data_models'
    expect(page).to have_selector('.trial-expired')
    expect(page).to have_content('Oops! Looks like your trial has expired!')

    visit '/adhoc/query_editor'
    expect(page).to have_selector('.trial-expired')
    expect(page).to have_content('Oops! Looks like your trial has expired!')
  end
end
