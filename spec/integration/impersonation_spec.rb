# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'impersonation', js: true, stable: true do
  include_context 'test_tenant'
  let!(:impersonator) { get_test_admin }
  let!(:impersonatee) { get_test_analyst }

  def start_impersonation_session
    if impersonator.tenant.saml_sso_enforced?
      visit 'users/sessions/new_sso'
      wait_for_element_load('.ci-email-field')
      fill_text('.ci-email-field', impersonator.email)
      OmniAuth.config.add_mock(:saml, {
                                 provider: 'saml',
                                 uid: '7',
                                 info: impersonator,
                                 extra: extra,
                               },)
      safe_click('.ci-submit-btn')
    else
      safe_login(impersonator, '/home')
    end

    wait_for_element_load('.ci-header-settings')
    safe_click('.ci-header-settings')
    safe_click('.ci-impersonate-btn')
    page.first('.ci-impersonate-item').click

    if impersonator.super_admin?
      fill_text('.ci-impersonate-reason', 'I have a reason')
      fill_text('.ci-impersonate-related-link', 'I have a link')
      safe_click('.ci-impersonate-submit')
    end
  end

  def end_impersonation_session
    wait_for_element_load('.ci-impersonating-alert')
    expect(page.all('.ci-impersonating-alert').size).to eq 1
    safe_click('.ci-end-impersonation-btn')
    wait_expect(0) { page.all('.ci-impersonating-alert', wait: false).size }
  end

  describe 'impersonation reminder' do
    def mock_impersonation_at
      mock_cookies = "impersonation_at=#{3.hours.ago.to_i * 1000}"
      page.evaluate_script("document.cookie='#{mock_cookies};path=/'")
      page.evaluate_script('window.location.reload()')
    end

    def check_extend_impersonation
      safe_click('.ci-confirm')
      end_impersonation_session
      # This check to make sure the modal does not show multiple times.
      wait_expect(0) { page.all('.ci-impersonation-reminder-modal', wait: false).size }
    end

    def end_impersonation_session_from_the_reminder(cta_class: '.ci-cancel')
      safe_click(cta_class)
      wait_expect(0) { page.all('.ci-impersonating-alert', wait: false).size }
      # This check to make sure the modal does not show multiple times.
      wait_expect(0) { page.all('.ci-impersonation-reminder-modal', wait: false).size }
    end

    context 'Tenant with SSO on' do
      include_context 'sso_with_saml'

      let!(:saml_provider) { create :saml_provider, enforced: true }
      let!(:dashboard) { create :dashboard, title: 'dashboard', owner: impersonatee }

      before do
        FeatureToggle.toggle_global(::SamlProvider::FW_SAML_SSO, true)
        OmniAuth.config.add_mock(:saml, {
                                   provider: 'saml',
                                   uid: '7',
                                   info: impersonatee,
                                   extra: extra,
                                 },)
      end

      it 'can be impersonated by super admin' do
        start_impersonation_session
        # test api v2 https://holistics.slack.com/archives/C2W4QL77F/p1690339674987309
        visit dashboard_path(dashboard)
        visit '/home'
      end
    end

    context 'FT to check the impersonation expired is on' do
      before do
        sign_in impersonator
        FeatureToggle.toggle_global(User::FT_IMPERSONATION_REMINDER, true)
      end

      context 'impersonator click extend impersonation session' do
        it 'extends the impersonation session successfully' do
          start_impersonation_session
          mock_impersonation_at
          check_extend_impersonation
        end
      end

      context 'impersonator click end impersonation session' do
        it 'cancels the impersonation session successfully' do
          start_impersonation_session
          mock_impersonation_at
          end_impersonation_session_from_the_reminder
        end
      end

      context 'impersonator click dismiss icon in the impersonation expired modal' do
        it 'cancels the impersonation session successfully' do
          start_impersonation_session
          mock_impersonation_at
          end_impersonation_session_from_the_reminder(cta_class: '.ci-modal-close')
        end
      end

      context 'super admin' do
        before do
          impersonator.update!(email: User::TEST_SUPER_ADMIN_EMAIL)
        end

        context 'click extend impersonation session' do
          it 'extends the impersonation session successfully' do
            start_impersonation_session
            mock_impersonation_at
            check_extend_impersonation
          end
        end

        context 'click end impersonation session' do
          it 'cancels the impersonation session successfully' do
            start_impersonation_session
            mock_impersonation_at
            end_impersonation_session_from_the_reminder
            mock_impersonation_at
            wait_expect(0) { page.all('.ci-impersonation-reminder-modal', wait: false).size }
          end
        end

        context 'click dismiss icon in the impersonation expired modal' do
          it 'cancels the impersonation session successfully' do
            start_impersonation_session
            mock_impersonation_at
            end_impersonation_session_from_the_reminder(cta_class: '.ci-modal-close')
          end
        end
      end
    end

    context 'FT to check the impersonation expired is off' do
      before do
        sign_in impersonator
      end

      it 'does not show the impersonation session modal' do
        start_impersonation_session
        mock_impersonation_at
        wait_expect(0) { page.all('.ci-impersonation-reminder-modal', wait: false).size }
      end
    end
  end

  describe 'maintenance mode' do
    before do
      sign_in impersonator
      impersonatee.tenant.set_maintenance_mode!(is_on: true, message: 'Test turn on maintenance mode')
    end

    it 'admin could end impersonation session when in maintenance mode' do
      start_impersonation_session
      end_impersonation_session
    end
  end
end
