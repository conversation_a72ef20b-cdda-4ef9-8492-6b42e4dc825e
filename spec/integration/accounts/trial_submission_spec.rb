# typed: false

require 'rails_helper'

describe 'Trial submission test', js: true, stable: true do
  before { get_holistics_tenant }

  let(:submission1) do
    {
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      company_name: 'French Empire',
      email: '<EMAIL>',
      mobile_number: 342_424_242,
      important_feature: 'I gonna invade Russia',
      databases: 'PostgreSQL, MongoDB',
      other_solutions: 'I lived in mountain',
      referral: '<PERSON><PERSON>ra',
    }
  end

  def get_confirmation_link(text)
    text.scan(/(\/accounts\/activate\?code=[a-z0-9]+)/).try(:[], 0).try(:[], 0)
  end

  def test_confirmation_email(email)
    confirmation_email_body = email.to_s
    expect(confirmation_email_body.include?('accounts/activate?code')).to be true

    confirmation_link = get_confirmation_link(confirmation_email_body)
    expect(confirmation_link.nil?).to be false

    confirmation_link
  end

  def test_signup_form_21_day_trial(submission, success = true)
    visit '/accounts/sign_up'
    page.find('#ci-first_name').set(submission[:first_name])
    page.find('#ci-last_name').set(submission[:last_name])
    page.find('#ci-company_name').set(submission[:company_name])
    page.find('#ci-email').set(submission[:email])
    page.find('#ci-mobile_number').set(submission[:mobile_number])
    page.find('#ci-know_sql').click
    page.find('#ci-databases').set(submission[:databases])
    h_checkbox_check(selector: '.ci-get-extra-days')
    page.find('#ci-important-feature').set(submission[:important_feature])
    page.find('#ci-other-solutions').set(submission[:other_solutions])
    page.find('#ci-referral').set(submission[:referral])

    page.find('#ci-submit').click

    if success
      wait_for_element_load('.congrate-panel', 10)
    else
      wait_for_element_load('.ci-error', 20)
    end
  end

  def test_signup_form_14_day_trial(submission, success = true)
    visit '/accounts/sign_up'
    page.find('#ci-first_name').set(submission[:first_name])
    page.find('#ci-last_name').set(submission[:last_name])
    page.find('#ci-company_name').set(submission[:company_name])
    page.find('#ci-email').set(submission[:email])
    page.find('#ci-mobile_number').set(submission[:mobile_number])
    page.find('#ci-know_sql').click
    page.find('#ci-databases').set(submission[:databases])
    page.find('#ci-submit').click

    if success
      wait_for_element_load('.congrate-panel', 10)
    else
      wait_for_element_load('.ci-error', 20)
    end
  end

  def test_user_name(name)
    wait_for_element_load('#activate_trial_form')
    page.find('.ci-name').set(name)
  end

  def test_user_initial_password(password)
    wait_for_element_load('#activate_trial_form')
    page.find('.ci-password').set(password)
    page.find('.ci-password-confirmation').set(password)
  end

  def test_user_title
    safe_click('[data-ci="select-user-title"] > [data-ci="select-Engineering"]')
    safe_click('[data-ci="select-user-bi-tools"] > [data-ci="select-Looker"]')
    page.find('.ci-submit').click
  end

  def test_confirmation_result(submission, confirmation_link)
    visit confirmation_link

    tenant = Tenant.find_by(name: submission[:company_name])
    expect(tenant).to be_present
  end

  it 'sign up trial' do
    test_signup_form_21_day_trial(submission1)
    test_signup_form_14_day_trial(submission1)

    confirmation_link = test_confirmation_email(get_last_email)
    test_confirmation_result(submission1, confirmation_link)

    test_user_name('YOLO')
    test_user_initial_password('Yoloyeye123')
    page.find('.ci-next').click

    test_user_title
    sleep 1

    user = User.find_by(email: submission1[:email])

    expect(user).to be_present

    expect(page.evaluate_script('window.H.current_user.id').to_i).to eq user.id
  end
end
