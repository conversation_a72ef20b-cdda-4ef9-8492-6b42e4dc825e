# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'log out', :js, stable: true do
  include_context 'test_tenant'
  include_context 'aml_studio_dataset'
  include_context 'query_model_based_report'

  let!(:user) { get_test_admin }

  before do
    FeatureToggle.toggle_global(Viz::Constants::FT_PRODUCTION_AML_FRONTEND_CACHE, true)
    SurveyAnswer.new(question_key: 'user:sign_out_reminder', user_id: user.id, tenant_id: user.tenant_id,
                     data: { do_not_show_again: true },).save!
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CONVERSION, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    DataSourceVersions::SchemaSynchronizationService.new(ds).execute
  end

  def sign_out
    safe_click('.ci-header-settings')
    safe_click('.ci-sign-out')
    wait_expect("#{capybara_base_url}/users/sign_in") { page.current_url }
  end

  describe 'removing user browser storage', wait_ajax_requests_before_clean_up_repos: false do
    let(:qr) { query_model_based_report }
    let(:report_widget) { create(:dashboard_widget, source: qr, dashboard: dashboard) }
    let(:text_filter) { create(:dynamic_filter, dynamic_filter_holdable: dashboard) }
    let(:dashboard) { create(:dashboard, version: 3) }

    it 'delete all indexedDB AmlReportingDatabase when sign out' do
      safe_login admin, data_set_path(data_set)
      wait_for_all_holistics_loadings

      aml_versions = get_indexed_db_storage('AmlReportingDatabase', 'amlVersions')
      dataset_data = get_indexed_db_storage('AmlReportingDatabase', 'datasetData')
      dataset_metas = get_indexed_db_storage('AmlReportingDatabase', 'datasetMetas')
      expect(aml_versions).not_to be_empty
      expect(dataset_data).not_to be_empty
      expect(dataset_metas).not_to be_empty

      sign_out

      aml_versions = get_indexed_db_storage('AmlReportingDatabase', 'amlVersions')
      dataset_data = get_indexed_db_storage('AmlReportingDatabase', 'datasetData')
      dataset_metas = get_indexed_db_storage('AmlReportingDatabase', 'datasetMetas')

      expect(aml_versions).to be_empty
      expect(dataset_data).to be_empty
      expect(dataset_metas).to be_empty
    end

    it 'remove all jobs info in localstorage' do
      safe_login admin, "/dashboards/v3/#{dashboard.id}"
      wait_for { page.all('.v-loading-container', wait: false).count == 0 }
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-download-png')
      wait_for { page.all('.loading-info', wait: false).count == 0 }

      local_storage_data = get_local_storage
      expect(local_storage_data['h_user_export_jobs']).not_to be_nil

      sign_out

      local_storage_data = get_local_storage
      expect(local_storage_data['h_user_export_jobs']).to be_nil
    end

    it 'remove adhoc query in localstorage' do
      safe_login admin, '/adhoc/query_editor'
      wait_and_set_ace_editor_text('adhoc-editor', 'SELECT 1')
      safe_click('.ci-run-query')
      wait_for_all_ajax_requests

      local_storage_data = get_local_storage
      expect(local_storage_data['queryEditorContent']).not_to be_empty

      sign_out

      local_storage_data = get_local_storage
      expect(local_storage_data['queryEditorContent']).to be_nil
    end
  end
end
