# typed: false
require 'rails_helper'

describe 'search functionality', stable: true do
  include_context 'test_tenant'

  let!(:folder) { create(:report_category, name: 'a') }
  let!(:report) { create(:query_report, title: 'marketing', category: folder) }

  it 'show clickable folder', js: true do
    qlogin(admin, '/')
    wait_for_element_load '.header-search'
    page.find('.header-search').click

    # Have to set text two time to properly set text
    wait_for_element_load('#menu-search')
    set_text('.ci-search-input', 'mark')

    page.find('.search-bar .h-select__options-item[data-value="/queries/1-marketing"]').click
    sleep 2

    wait_expect(report.title) { page.find('.ci-report-title').text }
  end
  context 'search UI/UX' do
    before do
      SearchService::RecentSearchService.new(admin).cache_recent_searches([
        { query: 'widget', filters: [] },
        { query: 'report', filters: ['Dashboards'] },
      ])
    end
    it 'recent item should have proper style', js: true do
      qlogin(admin, '/')
      wait_for_element_load '.header-search'

      page.find('.header-search').click
      # sleep 100
      wait_for_element_load('#menu-search')
      page.first('.recent-item').assert_matches_style("border-radius" => "4px")
    end
  end
end
