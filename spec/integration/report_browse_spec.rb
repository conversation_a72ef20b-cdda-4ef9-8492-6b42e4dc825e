# typed: false
# frozen_string_literal: true

# TODO: changed to new navigation node
require 'rails_helper'

describe 'browse reports', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:users) { %w[bizuser analyst] }
  let(:parent_category) { FactoryBot.create :report_category, name: 'Product' }
  let(:category) { FactoryBot.create :report_category, name: 'Features', parent_id: parent_category.id }
  let!(:report) { FactoryBot.create :query_report, title: 'Abc', category: category }

  # click on actions button of nth-node
  def show_actions(nth)
    safe_click('.ci-actions', index: nth)
  end

  def create_folder(name, is_workbook = false)
    wait_for_element_load '#report-browser .ci-create'
    page.find('#report-browser .ci-create').click
    page.find('.ci-create-new-folder').click

    wait_for_element_load '#category-name'
    page.find('#category-name').set(name)

    h_checkbox_check(selector: '.ci-is-workbook') if is_workbook
    page.find('.ci-submit-btn').click
    expect_notifier_content(/success/)
  end

  def rename_folder(nth, name)
    show_actions(nth)
    safe_click('.ci-rename')

    wait_for_element_load '#rename'
    fill_text('.ci-rename-input', name)
    safe_click('.ci-submit-btn')
    expect_notifier_content(/success/)
  end

  def edit_folder(nth, name)
    show_actions(nth)
    safe_click('.ci-edit-folder')

    fill_text('.ci-folder-name', name)
    safe_click('.ci-submit')
    expect_notifier_content(/success/)
  end

  def share_node(nth)
    show_actions(nth)
    safe_click('.ci-share')
    wait_for_element_load('.modal-dialog')
    users.each do |u|
      select_h_select_option('.ci-share-select', label: u)
    end
    safe_click('.ci-share-btn')
    safe_click('.modal-share .close-btn')
    wait_for_all_ajax_requests
  end

  def go_to_node(nth)
    wait_for { page.all('.ci-node-browse-body tr td:nth-child(1) .ci-node-link').count > nth }
    page.all('.ci-node-browse-body tr td:nth-child(1) .ci-node-link')[nth].click
    sleep 2
  end

  def move_node_to_root(nth)
    show_actions(nth)
    page.find('.ci-moveto').click
    wait_for_element_load '.tree-leaf'
    page.find('.tree-label').click
    page.find('.ci-submit-btn').click
    expect_notifier_content(/success/)
  end

  def test_table(expected)
    wait_for_all_holistics_loadings
    wait_for_element_load('.ci-node-browse-body')
    table = page.first('.ci-node-browse-body')
    wait_expect('......') { table.first('body tr td:nth-child(1)').text } if expected[:has_upper]
    wait_expect(expected[:names].sort) { table.all('body tr td:nth-child(1) .ci-node-link').map(&:text).sort }
    sleep 1
    sharings = table.all('tr td:nth-child(2)').map do |t|
      t.all('.ci-sharing-link > span', visible: true).inject('') do |current, s|
        current + s.text.strip
      end.gsub(' ,', ',')
    end
    sharings = sharings.map { |t| t.split(',').sort.join(',') }
    expect(sharings).to eq(expected[:sharings])

    wait_expect(expected[:owners].sort) { table.all('tr td:nth-child(3)').map(&:text).sort }
  end

  before(:each) do
    safe_login(admin, '/browse')
  end

  it 'create Foo folder' do
    create_folder 'Foo'
    expected = {
      names: %w[Foo Product],
      sharings: %w[Nobody Nobody],
      owners: ['', 'admin'],
    }
    test_table(expected)
  end

  it 'rename Foo to Bar' do
    rename_folder 0, 'Bar'
    expected = {
      names: %w[Bar],
      sharings: %w[Nobody],
      owners: [''],
    }
    test_table(expected)
  end

  it 'open Bar and create Lum folder' do
    create_folder 'Bar'
    go_to_node 0
    create_folder 'Lum'
    expected = {
      has_upper: true,
      names: %w[Lum],
      sharings: ['', 'Nobody'],
      owners: ['', 'admin'],
    }
    test_table(expected)
  end

  it 'share with "analyst,bizuser"' do
    expected = {
      names: %w[Product],
      sharings: ['analyst,bizuser'],
      owners: [''],
    }
    share_node 0
    test_table(expected)
  end

  it 'share with all' do
    wait_for_element_load('.share-with')
    page.first('.share-with').click
    share_all
    expected = {
      names: %w[Product],
      sharings: ['All users'],
      owners: [''],
    }
    test_table(expected)
  end

  it 'unshare' do
    expected = {
      names: %w[Product],
      sharings: %w[analyst,bizuser],
      owners: [''],
    }
    share_node 0
    # unshare with
    page.first('.share-with').click
    wait_for_element_load('.modal-share')
    row = page.find('.form-group tr', text: 'bizuser')
    row.first('.ci-unshare-btn').click
    page.first('.ci-confirm-delete').click
    page.first('.modal-share .ci-modal-close').click
    expected[:sharings] = ['analyst']
    test_table(expected)
  end

  it 'create workbook Mew' do
    create_folder 'Bar'
    go_to_node 0
    create_folder 'Lum'
    create_folder 'Mew', true

    expected = {
      has_upper: true,
      names: %w[Lum Mew],
      sharings: ['', 'Nobody', 'Nobody'],
      owners: ['', 'admin', 'admin'],
    }
    test_table(expected)
  end

  it 'back to root' do
    create_folder 'Bar'
    go_to_node 0
    create_folder 'Lum'
    # open Lum
    go_to_node 0
    # back to root
    page.find('.node-up').click
    sleep 1
    page.find('.node-up').click
  end

  it 'edit Bar' do
    # edit Bar => Bee, mark as work book
    create_folder 'Bar'
    wait_expect(false, 10) do
      page.has_css?('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
    end
    edit_folder 0, 'Bee'
    expected = {
      names: %w[Bee Product],
      sharings: %w[Nobody Nobody],
      owners: ['', 'admin'],
    }
    test_table(expected)
  end

  # Gandalf: YOU SHALL NOT PAAASSSS!
  # this test: OK
  it 'delete Product' do
    # create Bee
    create_folder 'Bee'
    # delete Product, move child nodes to test
    show_actions(1)
    page.find('.ci-delete').click
    page.find('.ci-confirm-delete').click
    wait_for_element_load '.modal-dialog'
    page.find('.modal-dialog .ci-move-mode').click

    select_h_select_option('.ci-folder', value: '0c')

    page.find('.modal-dialog .ci-submit').click
    # go to Bee / Feature
    expect(page).to have_content('Features')
    go_to_node 1
  end

  it 'others' do
    go_to_node 0
    go_to_node 0
    show_actions 0
    safe_click('.ci-clone-report')
    safe_click('.ci-close-modal')

    show_actions 0
    safe_click('.ci-delete')
    safe_click('.ci-confirm-delete')
    expect_notifier_content 'report deleted successfully'
  end
end
