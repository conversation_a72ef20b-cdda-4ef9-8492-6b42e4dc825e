# typed: false
require 'rails_helper'

describe 'Error notifier', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:tsub) { FactoryBot.create :tenant_subscription, tenant_id: tenant.id, status: 'active', embed_workers: 3 }
  let(:ds) {get_test_ds}
  let (:report) { FactoryBot.create :query_report, title: 'Report', query: 'select 1;' }
  let (:filter) {
    FactoryBot.create :shared_filter, name: 'country',
                       tenant_id: tenant.id, is_adhoc: false,
                       settings: ({
                         type: 'dropdown',
                         dropdown_source: 'sql',
                         data_source_id: ds.id,
                         dropdown_sql: "values ('a','A'), ('b', 'B');"
                       })
  }
  let (:dashboard) { FactoryBot.create :dashboard }

  before(:each) do
    FeatureToggle.toggle_tenant('shareable_link:ui_enabled', tenant.id, true)
  end

  after(:each) do
    clear_page_unload_events
  end

  describe 'submit_filters failed' do
    before do
      filter.settings[:dropdown_sql] += 'bla'
      filter.save
      @error_message = "syntax error at or near \"bla\" LINE 2: values ('a','A'), ('b', 'B');bla"
    end

    context 'in report' do
      before do
        FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'text'
      end
      it 'should show error notification' do
        safe_login(:admin, query_report_path(report))
        test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', @error_message)
        safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
      end
    end

    context 'in dashboard' do
      before do
        FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: 'text'
      end

      it 'should show error notification' do
        safe_login(:admin, dashboard_path(dashboard))
        safe_click('.ci-filter')
        test_error_notifier('.ci-filter-error', @error_message)

        # shareable link
        safe_click('.ci-share-dropdown')
        safe_click('.ci-shareable-links')
        safe_click('.ci-new-shareable-link')
        test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', @error_message)
        safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
        safe_click('.shareable-link-edit-modal .close-btn')
        safe_click('.preference-modal .close-btn')

        # embed link
        # create :tenant_subscription, tenant: tenant, status: 'active', embed_workers: 3
        safe_click('.ci-dashboard-settings')
        safe_click('.ci-embed-links')
        safe_click('.ci-new-embed-link')
        test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', @error_message)
      end
    end
  end

  describe 'submit_query failed' do
    before do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'text'
      report.query = "values ('1','A')a;"
      report.save
    end

    context 'in report' do
      it 'should show query error' do
        safe_login(:admin, query_report_path(report))
        test_error_notifier('.ci-query-error', 'syntax error at or near "a"')
      end
    end

    context 'in dashboard' do
      before do
        FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: 'text'
        FactoryBot.create :dashboard_widget, dashboard: dashboard, source: report
      end
      it 'should show error in widget' do
        safe_login(:admin, dashboard_path(dashboard))
        wait_for_element_load '.widget-content .error-container'
        test_error_notifier('.widget-content .error-container', "syntax error at or near \"a\" LINE 3: values ('1','A')a")
      end
    end
  end

  context 'when preview filter failed' do
    before do
      filter.settings[:dropdown_sql] += 'bla'
      filter.save
    end
    it 'should show filter error' do
      safe_login(:admin, '/shared_filters')
      safe_click('.ci-filter-name')
      safe_click('.ci-sf-preview')
      test_error_notifier('.ci-filter-error', "syntax error at or near \"bla\" LINE 2: values ('a','A'), ('b', 'B');bla")
    end
  end
end
