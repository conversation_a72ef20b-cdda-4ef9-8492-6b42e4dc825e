# typed: false

require 'rails_helper'

describe 'data sources edit/new', :js, stable: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }

  it 'can edit existing ds' do
    expected_dbconfig = ds.dbconfig.dup

    safe_login(admin, "/data_sources/#{ds.to_param}/edit")

    fill_text('.ci-name', 'something_else')
    safe_click('.ci-enable-password')
    fill_text('.ci-password', expected_dbconfig[:password])

    expect(page.first('.ci-dbtype.hui-select-trigger')[:class]).to include('disabled')

    safe_click('.ci-test')
    wait_expect('connected successfully') do
      page.find('[data-ci="ci-test-message"]').text.downcase
    end

    safe_click('.ci-submit')
    wait_expect('something_else') { ds.reload.name }

    [:host, :user, :dbname, :password, :port].each do |k|
      expect(ds.dbconfig[k].to_s).to eq expected_dbconfig[k].to_s
    end
  end

  shared_examples 'can edit password' do
    it 'edit and not update password' do
      ds = FactoryBot.create(:normalized_data_source)
      expected_dbconfig = ds.dbconfig.dup

      safe_login(admin, "/data_sources/#{ds.to_param}/edit")
      fill_text('.ci-name', 'something_else')
      safe_click('.ci-test')

      wait_expect('connected successfully') do
        page.find('[data-ci="ci-test-message"]').text.downcase
      end
      safe_click('.ci-submit')
      wait_expect('something_else') { ds.reload.name }

      [:host, :user, :dbname, :password, :port].each do |k|
        expect(ds.dbconfig[k].to_s).to eq expected_dbconfig[k].to_s
      end
    end

    it 'can edit and change password' do
      ds = FactoryBot.create(:normalized_data_source)
      expected_dbconfig = ds.dbconfig.dup

      safe_login(admin, "/data_sources/#{ds.to_param}/edit")
      fill_text('.ci-name', 'something_else')
      safe_click('.ci-enable-password')
      fill_text('.ci-password', ds.dbconfig[:password])

      safe_click('.ci-test')
      wait_expect('connected successfully') do
        page.find('[data-ci="ci-test-message"]').text.downcase
      end

      safe_click('.ci-submit')
      wait_expect('something_else') { ds.reload.name }

      [:host, :user, :dbname, :password, :port].each do |k|
        expect(ds.dbconfig[k].to_s).to eq expected_dbconfig[k].to_s
      end
    end
  end

  it_behaves_like 'can edit password'

  it 'can rename ds' do
    FeatureToggle.toggle_tenant('data_sources:google_analytics', admin.tenant_id, true)
    ds = google_analytics_testdb_ds
    expect(ds.name).to eq('MyString')

    safe_login(admin, "/data_sources/#{ds.to_param}/edit")
    fill_text('.ci-name', 'new-ga-name')
    sleep 1 # wait for the v-h-variablize to take effect
    safe_click('.ci-submit')

    # make sure that after clicking, the save action should be done
    wait_expect('Save') do
      page.find('.ci-submit').text
    end

    wait_expect('newganame') { ds.reload.name }
  end

  context 'with FT data_sources:hide_json_credentials' do
    let(:bq_ds) { create(:normalized_bigquery_data_source) }

    context 'when turning on' do
      before do
        FeatureToggle.toggle_tenant(DataSource::FT_HIDE_JSON_CREDENTIALS, admin.tenant_id, true)
      end

      it 'hide and disable json credentials input' do
        safe_login(admin, "/data_sources/#{bq_ds.to_param}/edit")

        wait_expect(true) { page.find('[name="JSON Credentials"] textarea').disabled? }
      end

      it 'can edit and update json cred' do
        safe_login(admin, "/data_sources/#{bq_ds.to_param}/edit")

        expected_dbconfig = bq_ds.dbconfig.dup

        fill_text('.ci-name', 'something_else')
        safe_click('.ci-enable-json-cred')
        fill_text('.ci-json-cred', bq_ds.dbconfig[:cred_json])

        safe_click('.ci-test')
        wait_expect('connected successfully') do
          page.find('[data-ci="ci-test-message"]').text.downcase
        end

        safe_click('.ci-submit')
        wait_expect('something_else') { bq_ds.reload.name }

        [:project_id, :cred_json].each do |k|
          expect(bq_ds.dbconfig[k].to_s).to eq expected_dbconfig[k].to_s
        end
      end

      it 'can edit and not update json cred' do
        safe_login(admin, "/data_sources/#{bq_ds.to_param}/edit")

        expected_dbconfig = bq_ds.dbconfig.dup

        fill_text('.ci-name', 'something_else')
        safe_click('.ci-enable-json-cred')

        safe_click('.ci-test')
        wait_expect('connected successfully') do
          page.find('[data-ci="ci-test-message"]').text.downcase
        end

        safe_click('.ci-submit')
        wait_expect('something_else') { bq_ds.reload.name }

        [:project_id, :cred_json].each do |k|
          expect(bq_ds.dbconfig[k].to_s).to eq expected_dbconfig[k].to_s
        end
      end

      it_behaves_like 'can edit password'
    end

    context 'when turning off' do
      before do
        FeatureToggle.toggle_tenant(DataSource::FT_HIDE_JSON_CREDENTIALS, admin.tenant_id, false)
      end

      it 'show existed json credentials' do
        safe_login(admin, "/data_sources/#{bq_ds.to_param}/edit")

        wait_expect(false) { page.find('[name="JSON Credentials"] textarea').disabled? }
        expect(page.find('textarea[name="JSON Credentials"]').value).to be_present
      end
    end
  end

  describe 'rename' do
    before do
      FeatureToggle.toggle_tenant('aml_studio:enable', admin.tenant_id, true)
      safe_login(admin, "/data_sources/#{ds.to_param}/edit")
    end

    it '4.0 - confirm rename' do
      fill_text('.ci-name', 'rename')
      safe_click('.ci-test')
      safe_click('.ci-submit')
      expect(all('div',
                 text: /Changing the data source name may affect Datasets\/Models referring to it./,)[0]).to be_present
      click_on(text: 'Confirm')
      ds.reload
      wait_expect('rename') { ds.reload.name }
    end

    it '4.0 - cancel rename' do
      fill_text('.ci-name', 'rename')
      safe_click('.ci-test')
      safe_click('.ci-submit')
      expect(all('div',
                 text: /Changing the data source name may affect Datasets\/Models referring to it./,)[0]).to be_present
      click_on(text: 'Cancel')
      ds.reload
      wait_expect('pg') { ds.reload.name }
    end
  end

  context 'with clickhouse form' do
    let(:clickhouse_ds) { clickhouse_testdb_ds }

    before do
      FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, true)
    end

    it 'canal default native port should be 9000 when first enable canal' do
      safe_login(admin, "/data_sources/#{clickhouse_ds.to_param}/edit")
      safe_click('[data-ci="ci-ds-advanced-settings"]')
      safe_click('[data-ci="enable-canal"]')
      wait_for_element_load('[data-ci="canal-native-port"]')
      expect(page.find('[data-ci="canal-native-port"]').value).to eq '9000'
    end
  end

  context 'with Canal available' do
    before do
      FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, true)
    end

    describe 'with force_enable_for_successfully_connected' do
      context 'enable FT' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_FOR_SUCCESSFULLY_CONNECTED, true)
        end

        context 'data source disables Canal' do
          it 'shows Canal always enabled if has flag _successfully_connected_canal_at' do
            ds = FactoryBot.create(:data_source, dbtype: DataSource::DBTYPE_POSTGRESQL, tenant_id: admin.tenant_id)
            ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now
            ds.save!
            safe_login(admin, "/data_sources/#{ds.to_param}/edit")

            safe_click('[data-ci="ci-ds-advanced-settings"]')
            wait_for_element_load('[data-ci="enable-canal"]')
            expect(page.find('[data-ci="enable-canal"]')[:class]).to include('checkbox-checked')
            expect(page.find('[data-ci="enable-canal"]')[:class]).to include('disabled')
            wait_expect('Fast Query Streaming Engine. Click to learn more. (NOTE: Canal is now always enabled for this data source)') do
              reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
              page.find('.hui-tooltip-floating').text
            end
            wait_for_element_load('[data-ci="canal-connection-pooling"]')
            expect(page.find('[data-ci="canal-connection-pooling"]')[:class]).to include('checkbox-checked')

          end

          it 'allow toggle Canal for if not has flag _successfully_connected_canal_at' do
            ds = FactoryBot.create(:data_source, dbtype: DataSource::DBTYPE_POSTGRESQL, tenant_id: admin.tenant_id)
            ds.save!
            safe_login(admin, "/data_sources/#{ds.to_param}/edit")

            safe_click('[data-ci="ci-ds-advanced-settings"]')
            wait_for_element_load('[data-ci="enable-canal"]')
            expect(page.find('[data-ci="enable-canal"]')[:class]).not_to include('disabled')
            wait_expect('Fast Query Streaming Engine. Click to learn more') do
              reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
              page.find('.hui-tooltip-floating').text
            end
          end

          describe 'Clickhouse form' do
            let(:clickhouse_ds) do
              ds = clickhouse_testdb_ds
              ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now
              ds.save!
              ds
            end

            it 'shows native port config along side http port' do
              safe_login(admin, "/data_sources/#{clickhouse_ds.to_param}/edit")
              wait_for_element_load('.ci-port')
              expect(page.find('.ci-port').value).to eq('8123')

              expect(page.has_no_css?('[data-ci="ci-ds-advanced-settings-content"]', { wait: 0 })).to eq true

              wait_for_element_load('[data-ci="canal-native-port"]')
              expect(page.find('[data-ci="canal-native-port"]').value).to eq '9000'
            end
          end
        end
      end

      context 'disable FT' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_FOR_SUCCESSFULLY_CONNECTED, false)
        end

        it 'allow toggle Canal for if has flag _successfully_connected_canal_at' do
          ds = FactoryBot.create(:data_source, dbtype: DataSource::DBTYPE_POSTGRESQL, tenant_id: admin.tenant_id)
          ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now
          ds.save!
          safe_login(admin, "/data_sources/#{ds.to_param}/edit")
          safe_click('[data-ci="ci-ds-advanced-settings"]')
          expect(page.find('[data-ci="enable-canal"]')[:class]).not_to include('disabled')
          wait_expect('Fast Query Streaming Engine. Click to learn more') do
            reliable_hover('[data-ci="canal-tootip"]', '.hui-tooltip-floating')
            page.find('.hui-tooltip-floating').text
          end
        end
      end
    end

    it 'edit will not change flag _successfully_connected_canal_at' do
      ds = FactoryBot.create(:data_source, dbtype: DataSource::DBTYPE_POSTGRESQL, tenant_id: admin.tenant_id)
      ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now
      ds.save!
      safe_login(admin, "/data_sources/#{ds.to_param}/edit")
      safe_click('[data-ci="ci-test-connection"]')
      wait_expect('connected successfully') do
        page.find('[data-ci="ci-test-message"]').text.downcase
      end
      safe_click('.ci-submit')
      expect_notifier_content('Saved successfully')
      ds.reload
      expect(ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym]).not_to be_nil
    end
  end
end
