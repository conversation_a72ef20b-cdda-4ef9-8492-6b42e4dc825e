# typed: false

require 'rails_helper'

describe 'data sources delete', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }

  describe '4.0 - delete' do
    before do
      FeatureToggle.toggle_tenant('aml_studio:enable', admin.tenant_id, true)
      safe_login(admin, "/data_sources")
    end

    it '4.0 - confirm delete' do
      expect(page).to have_css('.ci-datasource', :count => 2)
      safe_click('.ci-delete')
      warning_regex = 'Deleting the data source may affect Datasets\/Models referring to it\.*'
      expect(page.has_content?(warning_regex))
      safe_click('[data-hui-comp="modal"] button', text: 'Delete') # confirm button from new HModal
      wait_expect(1) { page.all('.ci-datasource').count }
    end
  end

  describe '3.0 - delete' do
    before do
      safe_login(admin, "/data_sources")
    end

    it '3.0 - confirm delete' do
      expect(page).to have_css('.ci-datasource', :count => 2)
      safe_click('.ci-delete')
      safe_click('.ci-confirm-delete') # confirm button from old modal
      wait_expect(1) { page.all('.ci-datasource').count }
    end
  end
end

