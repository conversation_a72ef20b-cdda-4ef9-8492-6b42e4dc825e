# typed: false

require 'rails_helper'

describe 'new-api-version alert', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:qr) { create :query_report, query: "select 'ahuhuhuhu'", is_adhoc: true }
  let!(:dw) { create :dashboard_widget, source: qr, dashboard: dashboard }
  let(:dashboard) { create :dashboard }
  let(:refresh_button) { '.ci-node-refresh' }

  shared_examples 'new-api-version alert' do
    it 'displays notification banner with refresh button' do
      wait_for_element_load(refresh_button)
      expect(page.all('.ci-notification-banner').size).to eq 0

      # mock api version
      allow_any_instance_of(ApplicationController).to receive(:api_version).and_return(HolisticsSetting::API_VERSION + 1)

      # trigger an ajax call
      safe_click(refresh_button)

      # test notification banner
      wait_for_element_load('.ci-notification-banner')
      expect(page.all('.ci-notification-banner').size).to eq 1
      expect(page.find('.ci-notification-banner').text).to match(/refresh the page/i)

      allow_any_instance_of(ApplicationController).to receive(:api_version).and_return(HolisticsSetting::API_VERSION)
      safe_click('.ci-notification-banner .ci-cta')

      sleep 1
      wait_for_element_load(refresh_button)
      expect(page.all('.ci-notification-banner').size).to eq 0
    end
  end

  context 'in-app users' do
    before do
      sign_in admin
    end
    context 'desktop browse page' do
      before do
        visit '/'
      end
      it_behaves_like 'new-api-version alert'
    end

    context 'mobile dashboard view', driver: :iphone do
      let(:refresh_button) { '.ci-dashboard-refresh' }
      before do
        visit dashboard_path(dashboard)
      end
      it_behaves_like 'new-api-version alert'
    end
  end

  context 'embedded users' do
    let(:el) do
      el = create :embed_link, source: dashboard, filter_ownerships: []
      el.set_public_user
      el.share_source
      el
    end
    let(:url) do
      sk = el.secret_key
      token = jwt_encode(sk, {}, Time.now.to_i + 90000)
      "/embed/#{el.hash_code}?_token=#{token}"
    end
    let(:refresh_button) { '.cache-status.refresh' }
    before do
      FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', true)
      visit url
    end
    it_behaves_like 'new-api-version alert'
  end
end
