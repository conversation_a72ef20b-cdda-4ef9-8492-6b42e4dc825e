# typed: false
require 'rails_helper'

describe 'report creating test', js: true, stable: true do
  it 'can load data source without default data source' do
    # set then delete default datasource
    default_ds= FactoryBot.create :data_source, dbtype: 'postgresql', name: 'hdefault', dbconfig: dbconfig_rails_test_env.to_json
    ds = FactoryBot.create :data_source, dbtype: 'postgresql', name: 'htest', dbconfig: dbconfig_rails_test_env.to_json
    get_test_tenant.set_setting('default_ds_id', default_ds.id)
    DataSource.find(default_ds.id).destroy

    qlogin(:admin, '/queries/new')
    select_h_select_option('.h-data-source-select', value: ds.id)

    # check datasource loaded
    wait_for_element_load '.schema-explorer .tree-select-option-list'
    expect(page).to have_selector('.schema-explorer .tree-select-option-list')
  end
end

describe 'report editing test', js: true do
  let(:sql) {
    'select generate_series(1, 25) as num;'
  }

  before do
    @cat = FactoryBot.create :report_category, name: 'Taxi'
    settings = {paging_page_size: 10}
    @report = FactoryBot.create :query_report, title: 'Report To Edit',
                                 query: sql, settings: settings, category_id: @cat.id
  end

  it 'edits the report successfully' do
    qlogin(:admin, "/queries/#{@report.to_param}/edit")

    # Change title
    safe_click('.ci-title')
    wait_and_set('.ci-title input', 'Number Series')
    safe_click('.ci-save')

    wait_expect('Number Series') do
      @report.reload.title
    end
  end

  it 'prompt users when they leave the page without saving' do
    qlogin(:admin, "/queries/#{@report.to_param}/edit")

    wait_for_element_load '.ace_editor'
    fill_in_ace '', 'select 1;'

    # go to any other page, e.g. homepage
    page.execute_script('window.location.href = "/browse";')
    page.driver.browser.switch_to.alert.dismiss

    # reload
    page.execute_script 'window.location.reload()'
    page.driver.browser.switch_to.alert.accept

    wait_for_element_load '.ace_editor'
    wait_expect(sql) { ace_get_text }
  end

  describe 'working with filters' do
    let(:sql) do
      'select {{drop}}'
    end
    let(:dropdown_sql) do
      <<~SQL
        with t(id, name) as (
          values('alice', 'alice'),
            ('bob', 'bob')
        )
        select * from t
      SQL
    end
    let(:filters) do
      [{ type: 'dropdown', dropdown_source: 'sql', data_source_id: get_test_ds.id, dropdown_sql: dropdown_sql, name: 'drop' }]
    end
    before do
      @viz_setting = FactoryBot.create :viz_setting, source: @report
      @report.filters = filters
    end
    it 'keeps filter values after saving' do
      qlogin(:admin, "/queries/#{@report.to_param}/edit")

      wait_for_element_load('.ci-filters')
      safe_click('.ci-filters')
      safe_click('.ci-report-filter-input .select2-container')
      safe_click('.select2-results__option:nth-child(2)')

      safe_click('.ci-btn-run')
      wait_for_element_load('.ci-table-report-data')
      expect(page.find('.ci-table-report-data').has_content?('bob')).to eq true

      # sometimes the keys get registered as a weird character, so we need to input backspace
      page.all('.ace_text-input', visible: false).first.send_keys([:control, 's'], :backspace)
      expect_notifier_content(/success/)

      safe_click('.ci-filters')
      expect(page.find('.ci-report-filter-input .select2-selection__rendered').has_content?('bob')).to eq true
      job_count = Job.count
      safe_click('.ci-btn-run')
      wait_expect(true) { job_count < Job.count } # wait for new run
      wait_for_element_load('.ci-table-report-data')
      expect(page.find('.ci-table-report-data').has_content?('bob')).to eq true
    end
  end

  describe 'with failed filters' do
    let(:column_name) { 'something_does_not_exist'}
    let(:error) { "ERROR: column \"#{column_name}\" does not exist"}
    let(:filters) {
      [{type: 'dropdown', dropdown_source: 'sql', data_source_id: get_test_ds.id, dropdown_sql: "select #{column_name}", name: 'country'}]
    }
    before do
      @report.filters = filters
    end

    def correct_filter_values
      wait_for_element_load '.ci-filter-error'
      expect(page.first('.ci-filter-error').text).to include error
      safe_click '.ci-filter-edit'
      wait_for_element_load '.filter-editable'
      sleep 1
      page.execute_script 'window.ace.edit("dropdown-sql-editor").setValue("select 1, 2")' # clear and set new values
      safe_click('.ci-shared-filter-save') # submit
    end

    context 'legacy report' do
      it 'shows filters with warning' do
        qlogin(:admin, "/queries/#{@report.to_param}/edit")
        wait_for_element_load '.ci-filters-warning'
        safe_click '.ci-filters-warning'
        correct_filter_values

        wait_expect(false) { page.has_css?('.ci-filters-warning', wait: false) }
      end
    end

    context 'new report' do
      before do
        @viz_setting = FactoryBot.create :viz_setting, source: @report
      end

      it 'shows filters with warning' do
        qlogin(:admin, "/queries/#{@report.id}/edit")
        wait_for_element_load '.ci-filters'
        safe_click '.ci-filters'
        correct_filter_values
        clear_page_unload_events
      end

      context 'multiselect dropdown' do
        let(:filters) {
          [{type: 'dropdown', dropdown_source: 'sql', data_source_id: get_test_ds.id, dropdown_sql: "select #{column_name}", name: 'country', dropdown_multiselect: true}]
        }
        it 'shows filters with warning' do
          qlogin(:admin, "/queries/#{@report.id}/edit")
          wait_for_element_load '.ci-filters'
          safe_click '.ci-filters'
          correct_filter_values
          clear_page_unload_events
        end
      end
    end
  end

  context 'dont prompt when user leaves without editing' do
    include_context 'test_tenant'
    include_context 'query_model_dataset_based_report'

    let(:query_report) { FactoryBot.create :pivot_table_report, owner_id: admin, tenant_id: tenant.id }

    it 'SQL report' do
      qlogin(:admin, "/queries/#{query_report.id}/edit")

      wait_for_element_load '.ace_editor'
      safe_click('.ci-discard')
      sleep 10
      expect(current_path).to eq '/browse'
    end

    it 'Data Set report' do
      qlogin(:admin, "/queries/#{query_model_dataset_based_report.id}")

      safe_click('.ci-edit-report-link')

      safe_click('.ci-cancel-ds-based-report')

      expect(current_path).to eq "/queries/#{query_model_dataset_based_report.id}"
    end
  end
end
