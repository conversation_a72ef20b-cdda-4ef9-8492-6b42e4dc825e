# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'data import type dbtable', js: true, legacy: true do
  let(:current_user) { users(:admin) }
  let(:from_ds) { mysql_testdb_ds }
  let(:dest_ds) { get_test_ds }
  let(:from_fqname) { FQName.parse 'foobar' }
  let(:from_fqname2) { FQName.parse 'foobar2' }
  let(:columns) do
    [
      { column_name: 'col_0', data_type: 'bigint' },
      { column_name: 'col_1', data_type: 'tinyint(2)' },
      { column_name: 'col_2', data_type: 'float' },
      { column_name: 'col_3', data_type: 'varchar(25)' },
    ]
  end
  let(:dest_fqname) { FQName.parse('public.test_di_dest') }
  let(:dest_fqname2) { FQName.parse('public.test_di_dest_2') }
  let(:di) do
    FactoryBot.create :data_import, source_type: DataImport::SOURCE_TYPE[:DATABASE],
                                     source_config: {
                                       dbtable: { ds_id: from_ds.id, fqname: from_fqname.to_unquoted_s },
                                     },
                                     dest_ds_id: dest_ds.id,
                                     dest_schema_name: dest_fqname.schema_name,
                                     dest_table_name: dest_fqname.table_name,
                                     table_config: { columns: columns }
  end
  let(:title) { 'Sample DB to DB Import' }

  before do
    FeatureToggle.toggle_global(::DataImport::FT_V1_CREATION, true)
    Cache.clear from_ds.cacher.all_columns_cache_key
    ds_create_table(connector: Connectors.from_ds(from_ds), columns: columns, fqname: from_fqname, rows: [])
    ds_create_table(connector: Connectors.from_ds(from_ds), columns: columns, fqname: from_fqname2, rows: [])
  end

  after do
    ds_drop_table(connector: Connectors.from_ds(from_ds), fqname: from_fqname)
    ds_drop_table(connector: Connectors.from_ds(from_ds), fqname: from_fqname2)
  end

  it 'can create a data import successfully' do
    safe_login(current_user, '/data_imports/new')
    select_source_table(from_ds, from_fqname)
    select_dest_table(dest_ds, dest_fqname)
    expect(page).not_to have_selector('.ci-reset-sync')

    # should validate dest def
    page.find('.ci-validate-dest').click
    wait_for_element_load('span.ci-validate-success')

    # set new title
    set_text('.ci-import-desc', title)
    page.find('.ci-submit-import').click
    wait_for { page.current_path == '/data_imports' }

    # expected values
    expected_tc = {
      columns: [
        { id: 'column_1', data_type: 'bigint', column_name: 'col_0', is_nullable: true, source_expression: 'col_0' },
        { id: 'column_2', data_type: 'integer', column_name: 'col_1', is_nullable: true, source_expression: 'col_1' },
        { id: 'column_3', data_type: 'double precision', column_name: 'col_2', is_nullable: true, source_expression: 'col_2' },
        { id: 'column_4', data_type: 'varchar', column_name: 'col_3', is_nullable: true, source_expression: 'col_3' },
      ],
      dist_key: '',
      sort_keys: nil,
      increment_column: '',
      dist_style: 'EVEN',
      sort_style: 'NONE',
    }
    expected_sc = { dbtable: {
      ds_id: from_ds.id,
      fqname: from_fqname.to_unquoted_s,
    } }
    expected = {
      title: title,
      source_type: 'dbtable',
      import_mode: 'full',
      owner_id: current_user.id,
      dest_ds_id: dest_ds.id,
    }

    di = DataImport.last
    sync_imported_column_ids(di.table_config[:columns], expected_tc[:columns]) # ensure column ids are correct

    compare_expect(di, expected)
    expect(di.source_config).to include(expected_sc)
    expect(di.table_config).to include(expected_tc)
  end

  it 'edit import without losing columns config' do
    current_cols = columns.map { |col| col[:data_type] }
    safe_login(current_user, edit_data_import_path(di))
    wait_for_element_load('.edit-data-import')

    # edit import
    wait_for_element_load('.ci-source-table ul.table-list', 20) # need more wait time to load
    select_source_ds(from_ds, from_fqname2)
    select_dest_table(dest_ds, dest_fqname2)

    # should not change column config
    wait_for_element_load('.ci-source-table ul.table-list')
    cols_type = page.all('.ci-col-type').map(&:value)
    expect(cols_type).to eq current_cols

    # reset config
    safe_click('.ci-reset-sync')
    sleep 1
    wait_for do
      cols_type = page.all('.ci-col-type').map(&:value)
      cols_type != current_cols
    end
  end
end
