# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'import google analytics', js: true, legacy: true do
  let!(:current_user) { get_test_admin }
  let!(:from_ds) { google_analytics_testdb_ds }
  let(:dest_ds) { get_test_ds }
  let(:title) { 'Sample GA to DB Import' }
  let(:dest_fqname) { FQName.parse('public.test_di_dest') }
  let(:ga_query_parameters) do
    {
      view: {
        id: '*********',
        name: 'All Web Site Data',
        type: 'WEB',
      },
      ds_id: from_ds.id,
      filters: '',
      metrics: ['ga:users'],
      start_date: '2018-07-11',
      end_date: '2018-07-17',
      property: {
        id: 'UA-********-2',
        name: 'demo',
        views: [{
          id: '*********',
          name: 'All Web Site Data',
          type: 'WEB',
        }],
        website_url: 'https://fake_account.github.io',
        internal_web_property_id: '********',
      },
      dimensions: ['ga:userType'],
      description: 'demo - All Web Site Data',
    }
  end
  let(:expected_cls) do
    # TODO: map data types appropriately instead of just setting them to the default data type?
    [{
      id: 'column_3',
      data_type: 'text',
      column_name: 'ga:userType',
      is_nullable: true,
      source_expression: 'ga:userType',
    }, {
      id: 'column_4',
      data_type: 'text',
      column_name: 'ga:users',
      is_nullable: true,
      source_expression: 'ga:users',
    },]
  end

  let!(:data_import) do
    di = FactoryBot.create(:data_import,
                           source_config: {
                             url: '',
                             dbtable: { ds_id: from_ds.id, fqname: '' },
                             # file_id: from_ds.id,
                             # file_name: 20,
                             post_import_query: '',
                             ga_query_parameters: ga_query_parameters,
                           },
                           dest_ds_id: dest_ds.id,
                           source_type: 'google_analytics',
                           table_config: {
                             columns: expected_cls,
                             dist_tyle: 'EVEN',
                             sort_style: 'NONE',
                           },
                           dest_schema_name: 'public',
                           dest_table_name: 'test_di_dest',
                           import_mode: 'full',)
    di.save!
    di
  end

  before do
    FeatureToggle.toggle_global('data_sources:google_analytics', true)
    FeatureToggle.toggle_global(DataImport::FT_V1_CREATION, true)
  end

  def create_ga_data_import(start_date, end_date)
    VcrHelper.ignore_hosts('127.0.0.1', 'localhost')

    VCR.use_cassette('google/google_analytics_data_import') do
      # select google analytics
      safe_login(current_user, '/data_imports/new')
      safe_click('.ci-ga')
      sleep 0.5
      # Select data source
      select_h_select_option('.ci-source-ds .hui-select', value: from_ds.id)
      # select metric
      wait_for_element_load '.ci-ga-metric .tree-select-control-input'
      select_ga_checkbox('.ci-ga-metric .tree-select-control-input', 'Users')
      safe_click('.ci-ga')

      # select dimension
      select_ga_checkbox('.ci-ga-dimension .tree-select-control-input', 'User Type')
      safe_click('.ci-ga')

      # Change date to match VCR date
      page.find('.ci-ga-start-date .ci-date-select').click
      wait_and_set('.ci-date-input', start_date)
      page.find('.ci-ga-start-date .ci-date-select').click

      wait_for { page.all('.ci-date-input', wait: false).count == 0 }

      page.find('.ci-ga-end-date .ci-date-select').click
      wait_and_set('.ci-date-input', end_date)
      page.find('.ci-ga-end-date .ci-date-select').click

      # Validate query
      page.find('.ga-validate-btn').click
      wait_for_element_load('span.alert-success')

      # Select destination dest data source and table
      dest_ds_sel = '.ci-dest-table .ci-data-source .hui-select'
      wait_for_element_load(dest_ds_sel)
      select_h_select_option(dest_ds_sel, value: dest_ds.id)
      fill_text '.ci-dest-table .ci-table-name', dest_fqname.table_name

      # should validate dest def
      safe_click('.ci-validate-dest')
      wait_for_element_load('span.ci-validate-success', 20)

      # naming
      set_text('.ci-import-desc', title)
      safe_click('.ci-submit-import')

      # Assert result
      wait_for_all_ajax_requests
      di = DataImport.last
      sync_imported_column_ids(di.table_config[:columns], expected_cls)
      expect(di.table_config[:columns].should =~ expected_cls)
      expect(di.source_config[:ga_query_parameters].should =~ ga_query_parameters)
    end
  end

  describe 'can create a data import successfully' do
    it 'with specific date' do
      ga_query_parameters[:start_date] = '2018-07-11'
      ga_query_parameters[:end_date] = '2018-07-17'
      create_ga_data_import('2018-07-11', '2018-07-17')
    end

    it 'with relative date' do
      Timecop.travel(Time.parse('2018-07-18 01:01:01 UTC')) do
        ga_query_parameters[:start_date] = '7 days ago'
        ga_query_parameters[:end_date] = 'yesterday'
        create_ga_data_import('7 days ago', 'yesterday')
      end
    end
  end

  def load_ga_data_import(start_date, end_date)
    VcrHelper.ignore_hosts('127.0.0.1', 'localhost')

    VCR.use_cassette('google/google_analytics_data_import') do
      safe_login(current_user, '/data_imports')
      # Expand and open first data import
      wait_for_element_load('.group-di')
      page.find('.group-di').click
      wait_for_element_load('.action-items .ci-edit-import')
      page.find('.action-items .ci-edit-import').click
      sleep 2 # wait for loading page
      wait_for_element_load('.ci-ga-description')
      expect(page.first('.ci-ga-start-date .ci-date-select').text).to eq(start_date)
      expect(page.first('.ci-ga-end-date .ci-date-select').text).to eq(end_date)
      expect(page.first('.ci-ga-property > div').text).to eq(ga_query_parameters[:property][:name])
      expect(page.first('.ci-ga-view> div').text).to eq(ga_query_parameters[:view][:name])
    end
  end

  describe 'Should loaded exists GA data import' do
    it 'Load specfic date range' do
      load_ga_data_import('2018-07-11', '2018-07-17')
    end

    it 'Load relative date range' do
      Timecop.travel(Time.parse('2018-07-18 01:01:01 UTC')) do
        data_import.source_config[:ga_query_parameters][:start_date] = '7 days ago'
        data_import.source_config[:ga_query_parameters][:end_date] = 'yesterday'
        data_import.save!
        load_ga_data_import('7 days ago', 'yesterday')
      end
    end
  end
end
