# typed: false
require 'rails_helper'

describe 'data import auto type guessing', js: true, legacy: true do
  context 'when source is MySQL and destination is Postgres' do
    let (:from_ds) { mysql_testdb_ds }
    let (:dest_ds) { get_test_ds }
    let (:fqname) { FQName.parse 'foobar' }
    let (:columns) do
      %w(bigint tinyint date timestamp float longtext).map { |dtype| {column_name: "col_#{dtype}", data_type: dtype} }
        .concat(%w(varchar char binary).map { |dtype|
          {column_name: "col_#{dtype}", data_type: "#{dtype}(1)"}
        })
        .concat([{column_name: 'col_decimal', data_type: 'decimal(2,1)'},
                 {column_name: 'col_enum', data_type: "enum('foo')"}
                ])
    end

    before do
      FeatureToggle.toggle_global(::DataImport::FT_V1_CREATION, true)
      Cache.flushdb
      ds_create_table(connector: Connectors.from_ds(from_ds), columns: columns, fqname: fqname, rows: [])
    end

    after do
      ds_drop_table(connector: Connectors.from_ds(from_ds), fqname: fqname)
    end

    it 'should auto fill destination column data types' do
      qlogin :admin, new_data_import_path
      select_source_table(from_ds, fqname)

      select_dest_table(dest_ds, FQName.parse('public.barfoo'))

      sleep 3 # wait for loading
      data_types = page.all('input.ci-col-type').map { |node| node.value }
      expect(data_types).to eq ['bigint', 'integer', 'date', 'timestamp', 'double precision', 'text', 'varchar', 'varchar', 'bytea', 'double precision', 'varchar']

      page.find('.ci-validate-dest').click

      # should validate dest def
      wait_for_element_load 'span.ci-validate-success'
    end
  end
end
