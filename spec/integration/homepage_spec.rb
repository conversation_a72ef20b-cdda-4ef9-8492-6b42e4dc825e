# typed: false
require 'rails_helper'

describe 'Homepage', js: true, stable: true do
  include_context 'test_tenant'

  before do
    FeatureToggle.toggle_global('reporting_nav:enabled', true)
  end

  it 'sets page title' do
    qlogin admin
    wait_for_element_load '.ci-home-user-name'

    expect(page.title).to eq('Home - test')

    # navigate to public workspace
    safe_click('.ci-public-workspace')
    wait_expect('Reports - test') { page.title }

    # navigate back to homepage
    safe_click('a[href="/home"]')
    wait_expect('Home - test') { page.title }
  end

  context 'create canvas dashboard from homepage' do
    let(:user) { get_test_admin }
    include_context 'aml_studio_dataset'
    before do
      FeatureToggle.toggle_global('aml_studio:dashboard_v4', true)
      FeatureToggle.toggle_global('new_navigation_node', true)
      # skip onboarding flow
      SurveyAnswer.create!(
        question_key: 'onboarding:welcome_screen',
        data: { passed: true },
        user_id: user.id,
        tenant_id: user.tenant_id,
      )
      SurveyAnswer.create!(
        question_key: 'onboarding:first_data_set',
        data: { created: true  },
        user_id: user.id,
        tenant_id: user.tenant_id,
      )
    end

    def expect_new_page_created
      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="editor-header-dropdown"]')
      wait_expect(/page\.aml/) { page.find('[data-ci="editor-header-dropdown"]').text }
    end

    it 'create canvas dashboard from quicklink' do
      qlogin admin
      wait_for_element_load '.ci-home-user-name'
      wait_for_element_load '.ci-quicklinks'
      safe_click('.ci-quicklinks')
      wait_for_element_load '.ci-new-canvas-dashboard'
      safe_click('.ci-new-canvas-dashboard')
      expect_new_page_created
    end

    it 'create canvas dashboard from sidebar' do
      qlogin admin
      wait_for_all_ajax_requests
      safe_click('#navigation-node-ReportCategory-0')
      safe_click('.navigation-node-action')
      # click option create canvas dashboard
      safe_click('.node-creation-menu-item')
      expect_new_page_created
    end

    it 'create canvas dashboard from workspace section' do
      qlogin admin
      wait_for_element_load '.ci-home-user-name'
      wait_for_element_load '.ci-btn-report-and-dashboard'
      safe_click('.ci-btn-report-and-dashboard')
      # click option create canvas dashboard
      safe_click('.ci-btn-add-canvas-dashboard')
      expect_new_page_created
    end
  end
end
