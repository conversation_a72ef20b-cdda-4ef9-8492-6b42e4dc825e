# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'log in', js: true, stable: true do
  include_context 'test_tenant'
  let!(:user) { get_test_admin }

  def login_by_sso
    if user.tenant.saml_sso_enforced?
      visit 'users/sessions/new_sso'
      wait_for_element_load('.ci-email-field')
      fill_text('.ci-email-field', user.email)
      OmniAuth.config.add_mock(:saml, {
                                 provider: 'saml',
                                 uid: '7',
                                 info: user,
                                 extra: extra,
                               },)
      safe_click('.ci-submit-btn')
    else
      safe_login(user, '/home')
    end
  end

  describe 'remember me' do
    context 'when SSO is on' do
      include_context 'sso_with_saml'

      let!(:saml_provider) { create :saml_provider, enforced: true }

      shared_examples 'can remember user' do
        before do
          FeatureToggle.toggle_global(::SamlProvider::FW_SAML_SSO, true)
          OmniAuth.config.add_mock(:saml, {
                                     provider: 'saml',
                                     uid: '7',
                                     info: user,
                                     extra: extra,
                                   },)
        end

        it 'can remember user after session cleared' do
          login_by_sso
          page.driver.browser.manage.delete_cookie('_holistics_session')
          visit '/home'
          expect(page.title).to eq('Home - test')
          expect(page.driver.browser.manage.cookie_named('_holistics_session')[:value]).to be_present
        end
      end

      it_behaves_like 'can remember user'

      context 'with google_only' do
        before do
          tenant.settings[:sign_in_mechanism] = 'google_only'
          tenant.save
        end

        it_behaves_like 'can remember user'
      end
    end
  end
end
