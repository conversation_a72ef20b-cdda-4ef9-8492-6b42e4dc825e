# typed: false
require 'rails_helper'

describe 'add users to groups', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:ds) { get_test_ds }
  let!(:group1) { FactoryBot.create :group, name: 'Group1', tenant_id: tenant.id }
  let!(:group2) { FactoryBot.create :group, name: 'Group2', tenant_id: tenant.id }
  let!(:group3) { FactoryBot.create :group, name: 'Group3', tenant_id: tenant.id }
  let!(:cow) { FactoryBot.create :user, email: '<EMAIL>' }

  def add_group(user, groups)
    open_modal_by_click(".ci-user-#{user.id} .ci-add-user")
    groups.each do |group|
      h_select_dropdown('.ci-group-select', value: group.id)
    end
    safe_click('.ci-submit')
  end

  def count_users_by_group_id(group_id)
    page.all(".ci-user-#{group_id} .ci-user-groups").count
  end

  it 'add groups to user' do
    safe_login(:admin, '/manage/users')
    wait_for_element_load('.ci-add-user')

    add_group(cow, [group1, group2])
    wait_expect(2) { count_users_by_group_id(cow.id) }

    add_group(cow, [group3])
    wait_expect(3) { count_users_by_group_id(cow.id) }
  end
end
