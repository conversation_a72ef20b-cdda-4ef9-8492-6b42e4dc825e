# typed: false
require 'rails_helper'

describe 'filters have long url', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:very_long_filter_value) { 'a' * 600 }

  def test_long_url
    wait_for_report_load

    # fill in text filter
    page.find('.ci-text').set very_long_filter_value
    safe_click('.ci-submit-filters-btn')

    safe_click('.ci-filter-header') if page.has_css?('.ci-filter-header')

    # cannot find input.ci-input-editable, will fix later
    wait_for_element_load '.ci-filters'
    wait_for_element_load '.ci-text-filter'
    wait_for_element_load '.ci-text'

    filter_value = page.first('.ci-text').value
    wait_expect(very_long_filter_value) { filter_value }

    wait_expect(1) {
      values = get_report_page_values(page)
      values.length
    }

    uri = URI.parse current_url
    wait_expect(true) { uri.query.length < 50 }
  end

  before do
    sql = <<-SQL.strip_heredoc
      select
        column1 as id,
        column2 as sort,
        column3 as dropdown,
        column4 as text
      from (
      values
        ('a', 1, 1, 'WAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA'),
        ('b', 2, 1, 'HAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA'),
        ('c', 3, 1, 'GRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR'),
        ('d', 4, 2, 'HELLO'),
        ('e', 5, 2, 'WORLD'),
        ('f', 6, 2, '#{very_long_filter_value}')
      ) f
      where [[ column3 = {{dropdown}} ]] and [[ column4 = {{text}} ]];
    SQL

    filters = [
      {
        name: 'dropdown',
        type: 'dropdown',
        order: '0',
        default: '1',
        dropdown_source: 'manual',
        parent_filter_id: nil,
        dropdown_manual_entries: "1,1\n2,2"
      },
      {
        name: 'text',
        type: 'input',
        order: '1',
        parent_filter_id: nil
      }
    ]

    @report = FactoryBot.create :query_report, title: 'Report Long URL', data_source_id: ds.id, query: sql
    @f1 = FactoryBot.create :shared_filter, name: 'dropdown', settings: filters[0], is_adhoc: true
    @f2 = FactoryBot.create :shared_filter, name: 'text', settings: filters[1], is_adhoc: true
    FactoryBot.create :filter_ownership, filterable: @report, shared_filter: @f1, var_name: 'dropdown'
    FactoryBot.create :filter_ownership, filterable: @report, shared_filter: @f2, var_name: 'text'
  end

  context 'when submitting a report with query string length > 500' do
    it 'shorten the query string before submit' do
      safe_login(admin, query_report_path(@report))
      test_long_url
    end
  end

  context 'when submitting a dashboard with query string length > 500' do
    before do
      @dashboard = FactoryBot.create :dashboard
      FactoryBot.create :dashboard_widget, dashboard: @dashboard, tenant: @dashboard.tenant, source_type: 'QueryReport',
        source_id: @report.id

      FactoryBot.create :filter_ownership, filterable: @dashboard, shared_filter: @f1, var_name: 'dropdown'
      FactoryBot.create :filter_ownership, filterable: @dashboard, shared_filter: @f2, var_name: 'text'
    end

    it 'shorten the query string before submit' do
      qlogin(admin, dashboard_path(@dashboard))
      wait_for_element_load ('.filters-preview')
      wait_for do
        safe_click('.filters-preview')
        page.all('.ci-text').count > 0
      end
      test_long_url
    end
  end
end
