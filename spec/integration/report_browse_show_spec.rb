# typed: false
require 'rails_helper'

describe 'browse reports show', js: true, stable: true do
  include_context 'test_tenant'

  def count_headers
    page.first('.ci-node-browse-body thead tr').all('th').count
  end

  it 'with admin' do
    qlogin admin
    wait_for_element_load '.ci-node-browse-body'
    wait_expect(6) { count_headers }
  end

  it 'with analyst' do
    qlogin analyst
    wait_for_element_load '.ci-node-browse-body'
    wait_expect(6) { count_headers }
  end

  it 'with user' do
    qlogin biz_user
    sleep 1
    wait_for_element_load '.ci-node-browse-body'
    wait_expect(3) { count_headers }
  end
end
