# typed: false
require 'rails_helper'

describe 'Job log displays job status', stable: true do
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:ds) do
    create :data_source, {
      dbtype: 'postgresql',
      dbconfig: dbconfig_rails_test_env.to_json,
    }
  end

  before do
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', true)
  end

  context 'FT jobs:display_revamped_job_statuses is turned on', js: true do
    before do
      FeatureToggle.toggle_global('jobs:display_revamped_job_statuses', true)
      goto_create_table_model_modal(ds.id)
    end

    it 'display mapped job log status' do
      safe_click('.ci-status-last-synced')
      wait_for_element_load '.jobLog'
      rows = page.all('.jobLog-growedCol')
      expect(rows[0].text).to match 'Status: Pending'
      expect(rows[2].text).to match 'Status: Starting'
    end
  end

  context 'FT jobs:display_revamped_job_statuses is turned off', js: true do
    before do
      FeatureToggle.toggle_global('jobs:display_revamped_job_statuses', false)
      goto_create_table_model_modal(ds.id)
    end

    it 'display prime job log status' do
      safe_click('.ci-status-last-synced')
      wait_for_element_load '.jobLog'
      rows = page.all('.jobLog-growedCol')
      expect(rows[0].text).to match 'Status: Created'
      expect(rows[2].text).to match 'Status: Queued'
    end
  end
end

describe 'Job log displays source type' do
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('integrations:slack', true)
  end

  def run_schedule
    safe_click('.ci-schedule-dropdown')
    safe_click('.ci-manage-schedules')
    safe_click '.ci-actions'
    safe_click '.ci-es-send'
    safe_click '.ci-confirm'
  end

  def test_display_data_schedule
    qlogin(:admin, dashboard_path(dashboard))
    sleep 1
    run_schedule
    safe_click('.ci-status-last-synced')
    wait_for_element_load('.jobLog')
    rows = page.all('.jobLog-growedCol')
    expect(rows[1].text).to match(/DataSchedule/)
  end

  context 'with EmailSchedule', js: true do
    let!(:dashboard) { create :dashboard, version: 3 }
    let!(:email_dest) { create :email_dest, recipients: ['<EMAIL>'] }
    let!(:email_schedule) do
      create :email_schedule, tenant_id: admin.tenant.id, creator_id: admin.id,
                              dest_type: 'EmailDest', dest_id: email_dest.id, source: dashboard
    end

    it 'display DataSchedule' do
      test_display_data_schedule
    end
  end

  context 'with SlackSchedule', js: true do
    let!(:dashboard) { create :dashboard, version: 3 }
    let!(:slack_dest) { create :slack_dest }
    let!(:slack_schedule) do
      create :email_schedule, dest_type: 'SlackDest', dest_id: slack_dest.id,
                              source: dashboard, tenant_id: admin.tenant_id
    end

    it 'display DataSchedule' do
      test_display_data_schedule
    end
  end
end
