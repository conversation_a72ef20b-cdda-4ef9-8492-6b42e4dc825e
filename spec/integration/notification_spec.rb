# typed: false
require 'rails_helper'

describe 'show notification', js: true, stable: true do

  let(:tenant) { get_test_tenant }
  let(:user) { get_test_admin }

  let(:noti1) {
    {
      tenant_id: tenant.id,
      recipient_id: user.id,
      title: "Insights Generator",
      body: "Notification 1",
      read_at: nil
    }
  }

  let(:noti2) {
    {
      tenant_id: tenant.id,
      recipient_id: user.id,
      title: "Insights Generator",
      body: "Notification 2",
      read_at: nil
    }
  }

  before do
    FactoryBot.create :app_notification, noti1
    FactoryBot.create :app_notification, noti2
  end

  after do
    AppNotification.destroy_all
  end

  # Todo: write test to test the notification modal
  # those tests below are deprecated

  xit 'should display unread notification' do
    qlogin(:admin)

    #Check number of notification
    expect(page.all('.main-content .notification .strip').size).to eq(2)

    #Check content
    expect(page.first('.main-content .notification .strip .strip-content').text).to include("Notification 1")
  end

  xit 'should close notification and not display the next time page loaded' do
    qlogin(:admin)

    wait_for(1) { page.all('.main-content .notification .strip button.close').size }
    sleep 1 #sometimes cannot click

    #Close notification
    page.first('.main-content .notification .strip button.close').click
    sleep 1

    #expect 1 notification left
    expect(page.all('.main-content .notification .strip').size).to eq(1)

    #refresh browser
    page.evaluate_script("window.location.reload()")

    #expect 1 notification left
    wait_expect(1) { page.all('.main-content .notification .strip').size }
  end

end
