# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'manage integration', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:sftp_source) { FactoryBot.create :test_ds_sftp }

  before(:each) do
    FeatureToggle.toggle_global('integrations:sftp', true)
    allow_any_instance_of(Connectors::Connection::SftpConnection).to receive(:test_connection).and_return('/tmp/.holistics_tmp')

    safe_login(admin, '/manage/integrations')
    wait_for_element_load('.add-ftp-connection')
  end

  context 'Available connection' do
    it 'Add new connection' do
      page.find('.add-ftp-connection').click

      # Fill form
      wait_for_element_load('.ci-name')
      fill_text('.ci-name', 'new_sftp_connection')
      fill_text('.ci-host', 'localhost')
      fill_text('.ci-port', '22')
      fill_text('.ci-remote-path', '/tmp')
      fill_text('.ci-username', 'user')
      fill_text('.ci-password', 'secure-password')

      # validate connection
      page.find('.ci-test').click
      sleep(0.5)
      wait_for_element_load('[data-ci="ci-test-message"]')
      expect(page.find('[data-ci="ci-test-message"]').text.downcase).to eq('connected successfully')

      # Submit
      wait_for_element_load('.ci-submit')
      page.find('.ci-submit').click
      sleep(0.5)

      new_ds = DataSource.last
      ds_config = new_ds.dbconfig

      expect(new_ds.dbtype).to eq('sftp')
      expect(new_ds.name).to eq('new_sftp_connection')
      expect(ds_config[:host]).to eq('localhost')
      expect(ds_config[:port]).to eq('22')
      expect(ds_config[:username]).to eq('user')
      expect(ds_config[:password]).to eq('secure-password')
    end

    it 'Edit connection' do
      # edit first connection
      wait_for_element_load('.ftp-edit')
      page.first('.ftp-edit').click
      fill_text('.ci-name', 'updated_sftp_connection')

      # validate connection
      page.find('.ci-test').click
      sleep(0.5)
      wait_for_element_load('[data-ci="ci-test-message"]')
      expect(page.find('[data-ci="ci-test-message"]').text.downcase).to eq('connected successfully')

      # Submit
      wait_for_element_load('.ci-submit')
      page.find('.ci-submit').click
      sleep(0.5)

      sftp_source.reload
      expect(sftp_source.name).to eq('updated_sftp_connection')
    end

    it 'Remove connection' do
      # remove first connection
      wait_for_element_load('.ci-delete-adls2')
      page.first('.ci-delete-adls2').click

      wait_for_element_load('.ci-confirm-delete')
      page.first('.ci-confirm-delete').click

      wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
      expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text.downcase).to eq('sftp connection successfully deleted!')
    end
  end
end
