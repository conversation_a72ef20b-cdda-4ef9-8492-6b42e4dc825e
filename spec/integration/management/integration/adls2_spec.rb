# typed: ignore
# frozen_string_literal: true

require 'rails_helper'

xdescribe 'manage adls2 integration', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let!(:adls2_source) { FactoryBot.create :test_ds_adls2 }

  before(:each) do
    allow_any_instance_of(Connectors::Connection::Adls2Connection).to receive(:test_connection).and_return(true)

    safe_login(admin, '/manage/integrations')
    wait_for_element_load('.ci-add-adls2-connection')
  end

  context 'Available connection' do
    it 'Add new connection' do
      page.find('.ci-add-adls2-connection').click

      # Fill form
      wait_for_element_load('.ci-name')
      fill_text('.ci-name', 'new_adls2_connection')
      fill_text('.ci-storage-name', 'holistics')
      fill_text('.ci-storage-sas-token', 'token')
      fill_text('.ci-container', 'test-container')

      # validate connection
      page.find('.ci-test').click
      sleep(0.5)
      wait_for_element_load('[data-ci="ci-test-message"]')
      expect(page.find('[data-ci="ci-test-message"]').text.downcase).to eq('connected successfully')

      # Submit
      wait_for_element_load('.ci-submit')
      page.find('.ci-submit').click
      sleep(0.5)

      new_ds = DataSource.last
      ds_config = new_ds.dbconfig

      expect(new_ds.dbtype).to eq('adls2')
      expect(new_ds.name).to eq('new_adls2_connection')
      expect(ds_config[:storage_name]).to eq('holistics')
      expect(ds_config[:storage_sas_token]).to eq('token')
      expect(ds_config[:container]).to eq('test-container')
    end

    it 'Edit connection' do
      # edit first connection
      wait_for_element_load('.ci-edit-adls2')
      page.first('.ci-edit-adls2').click
      fill_text('.ci-name', 'updated_adls2_connection')

      # validate connection
      page.find('.ci-test').click
      sleep(0.5)
      wait_for_element_load('[data-ci="ci-test-message"]')
      expect(page.find('[data-ci="ci-test-message"]').text.downcase).to eq('connected successfully')

      # Submit
      wait_for_element_load('.ci-submit')
      page.find('.ci-submit').click
      sleep(0.5)

      adls2_source.reload
      expect(adls2_source.name).to eq('updated_adls2_connection')
    end

    it 'Remove connection' do
      # remove first connection
      wait_for_element_load('.ci-delete-adls2')
      page.first('.ci-delete-adls2').click

      wait_for_element_load('.ci-confirm-delete')
      page.first('.ci-confirm-delete').click

      wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
      expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text.downcase).to eq('adls2 connection successfully deleted!')
    end
  end
end
