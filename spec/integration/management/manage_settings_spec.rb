# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'manage settings', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:tenant) { get_test_tenant }
  let(:ds) { get_test_ds }

  before do
    FeatureToggle.toggle_global(Tenant::FT_ALLOW_SENDER_EMAIL, true)
  end

  before(:each) do
    tenant.report_jobs_per_user_limit = 10
    tenant.save
    safe_login(admin, '/manage/settings')
  end

  def reload_tenant
    wait_for_element_load '[data-ci="ci-toasts-top"] [data-ci="ci-toast"]' # wait for notification
    tenant.reload
  end

  it 'updates general settings' do
    settings = {
      time_zone: 'Pacific/Honolulu',
      label: 'Pacific/Honolulu',
    }

    scroll_to('#form-general')
    search_h_select('.h-timezone-select', text: settings[:label])
    select_h_select_option('.h-timezone-select', label: settings[:label])
    page.find('.ci-general-settings').click

    wait_for_element_load '#form-general'
    sleep 2

    tenant.reload
    expect(tenant[:settings].slice(:time_zone)).to eq settings.slice(:time_zone)
  end

  it 'updates email settings' do
    settings = { email: { sender_name: '<EMAIL>',
                          sender_email: 'trung le',
                          failure_recipients: '<EMAIL>', } }

    scroll_to('#form-email')
    fill_text('#sender-name', settings[:email][:sender_name])
    fill_text('#sender-email', settings[:email][:sender_email])
    fill_text('#recipients', settings[:email][:failure_recipients])
    page.find('.ci-email-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:email)).to eq settings
  end

  context 'updates viz settings' do
    it 'updates records_limit when tenant:visualization_setting_v2 is disabled' do
      FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, false)
      safe_login(admin, '/manage/settings')

      settings = { viz: { records_limit: 20_000 } }

      scroll_to('#form-viz')
      expect(page).to have_css('[data-ci="records-limit"]')
      expect(page).to_not have_css('[data-ci="default-records-limit"]')

      select_h_select_option('#viz-num', value: settings[:viz][:records_limit])
      page.find('.ci-viz-settings').click

      reload_tenant
      expect(tenant[:settings].slice(:viz)).to eq settings
    end

    it 'updates default_records_limit when tenant:visualization_setting_v2 is enabled' do
      FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)
      safe_login(admin, '/manage/settings')

      settings = { viz: { default_records_limit: 20_000 } }

      scroll_to('#form-viz')
      expect(page).to have_css('[data-ci="default-records-limit"]')
      expect(page).to_not have_css('[data-ci="records-limit"]')

      select_h_select_option('#viz-num', value: settings[:viz][:default_records_limit])
      page.find('.ci-viz-settings').click

      reload_tenant
      expect(tenant[:settings].slice(:viz)).to eq settings
    end
  end

  it 'updates cache settings' do
    settings = { report_cache_duration: 360 }
    option_text = "#{settings[:report_cache_duration] / 60} hours"

    scroll_to('#form-cache')
    select_h_select_option('.ci-cache-duration', label: option_text)
    page.find('.ci-cache-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:report_cache_duration)).to eq settings
  end

  it 'updates queue settings' do
    settings = { report_jobs_per_user_limit: 5 }

    scroll_to('#form-queue')
    select_h_select_option('#queue-num', value: settings[:report_jobs_per_user_limit])
    page.find('.ci-queue-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:report_jobs_per_user_limit)).to eq settings
  end

  it 'allow update to unlimited queue' do
    settings = { report_jobs_per_user_limit: nil }

    scroll_to_js('#form-queue')
    select_h_select_option('#queue-num', label: 'Unlimited concurrent report jobs', scroll_to_find: true)
    page.find('.ci-queue-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:report_jobs_per_user_limit)).to eq settings
  end

  it 'updates maintenance settings' do
    settings = { maintenance: { is_on: true,
                                message: 'Hi, Holistics', } }

    scroll_to('#form-maintenance')
    page.find('#maintenance-is-on').click
    fill_text('#maintenance-message', settings[:maintenance][:message])
    page.find('.ci-maintenance-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:maintenance)).to eq settings
  end

  it 'updates security settings' do
    settings = { sign_in_mechanism: 'all' }

    scroll_to('#form-security')
    select_h_select_option('#security-choice', value: settings[:sign_in_mechanism])
    page.find('.ci-security-settings').click

    reload_tenant
    expect(tenant[:settings].slice(:sign_in_mechanism)).to eq settings
  end

  context 'tenant unused_report_jobs_ttl' do
    before do
      tenant.update!(settings: { unused_report_jobs_ttl: 3 })
    end

    context 'with FT_UNUSED_JOB_TTL_TENANT_SETTING disabled' do
      before do
        FeatureToggle.toggle_global(::Job::FT_UNUSED_JOB_TTL_TENANT_SETTING, false)
      end

      it 'does not show setting on UI and is not affected when changing other settings' do
        safe_login(admin, '/manage/settings')
        expect(page).not_to have_css('#unused-jobs-ttl')

        settings = { report_jobs_per_user_limit: 5 }

        scroll_to('#form-queue')
        select_h_select_option('#queue-num', value: settings[:report_jobs_per_user_limit])
        page.find('.ci-queue-settings').click

        reload_tenant
        expect(tenant.settings[:unused_report_jobs_ttl]).to eq 3
      end
    end

    context 'with FT_UNUSED_JOB_TTL_TENANT_SETTING enabled' do
      before do
        FeatureToggle.toggle_global(::Job::FT_UNUSED_JOB_TTL_TENANT_SETTING, true)
      end

      it 'can set TTL properly' do
        safe_login(admin, '/manage/settings')

        scroll_to('#form-queue')
        expect(page).to have_css('#unused-jobs-ttl', text: '3 seconds')

        select_h_select_option('#unused-jobs-ttl', value: 0, scroll_to_find: true)
        page.find('.ci-queue-settings').click

        reload_tenant
        expect(page).to have_css('#unused-jobs-ttl', text: 'Disable auto cancellation')
        expect(tenant.settings[:unused_report_jobs_ttl]) == 0
      end

      it 'shows `Disable auto cancellation` for tenant without TTL' do
        tenant.update!(settings: {})
        safe_login(admin, '/manage/settings')

        scroll_to('#form-queue')
        expect(page).to have_css('#unused-jobs-ttl', text: 'Disable auto cancellation')

        select_h_select_option('#unused-jobs-ttl', value: 5, scroll_to_find: true)
        page.find('.ci-queue-settings').click

        reload_tenant
        expect(page).to have_css('#unused-jobs-ttl', text: '5 seconds (Default)')
        expect(tenant.settings[:unused_report_jobs_ttl]) == 5
      end

      it 'shows `Disable auto cancellation` for tenant with TTL = 0' do
        tenant.update!(settings: { unused_report_jobs_ttl: 0 })

        safe_login(admin, '/manage/settings')

        scroll_to('#form-queue')
        expect(page).to have_css('#unused-jobs-ttl', text: 'Disable auto cancellation')
      end
    end
  end

  context '2FA' do
    before do
      FeatureToggle.toggle_global('feature_wall:two_factor_auth', true)
    end

    it 'toggle 2FA' do
      safe_login(admin, '/manage/settings#form-security')
      wait_for_all_ajax_requests
      page.find('#form-security div.ci-enforce-2fa', exact_text: 'Enforce Two-factor Authentication for users with password login').click
      page.find('button', exact_text: 'Save Security Settings').click
      wait_for_all_ajax_requests
      expect(page.has_css?('div', exact_text: 'Your organization has required Two-factor Authentication (2FA)')).to eq(true)

      page.find('#form-security div.ci-enforce-2fa', exact_text: 'Enforce Two-factor Authentication for users with password login').click
      page.find('button', exact_text: 'Save Security Settings').click
      page.find('button', exact_text: 'Continue disabling 2FA enforcement').click
      wait_for_all_ajax_requests
      expect(page.has_css?('div[data-hui-comp="checkbox"][data-hui-type="warning"]')).to eq(false)
    end
  end
end
