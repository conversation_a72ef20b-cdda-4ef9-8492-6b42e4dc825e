# typed: false

require 'rails_helper'

describe 'Chart Annotator', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:user) { get_test_user }
  let(:tenant) { get_test_tenant }

  let(:report) do
    sql = <<~SQL
      select column1::date as date, column2 as sale
      from (
        values
        ('2017-03-27', 777),
        ('2017-04-01', 999),
        ('2017-04-05', 666)
      ) t
    SQL

    FactoryBot.create :query_report, query: sql, data_source: get_test_ds, viz: {
      has_viz: true,
      viz_type: 'chart',
      chart_type: 'line',
      owner: admin,
      tenant: tenant,
    }
  end

  before do
    FeatureToggle.toggle_global('chart_annotations', true)

    FactoryBot.create :annotation, title: 'Foo', notes: 'Foo', date_d: '2017-04-01', user: user, tenant: tenant
    FactoryBot.create :annotation, title: 'Bar', notes: 'Bar', date_d: '2017-04-01', user: user, tenant: tenant
    FactoryBot.create :annotation, title: 'Hello World', notes: 'Hello World', date_d: '2017-04-03', user: user,
                                   tenant: tenant
  end

  # ----- shared steps -----
  def _chart_annotations
    page.all('.highcharts-annotation-group .highcharts-label')
  end

  def _have_rendered_all_relevant_annotations
    wait_expect(@annotations.count) { _chart_annotations.count }
  end

  def _toggle_sidebar
    page.find('.ci-sidebar-toggle').click
  end

  def _have_sidebar_collapsed
    _find_sidebar to_be_present: false
  end

  def _find_sidebar(to_be_present: true)
    value = to_be_present ? 0 : 1
    wait_expect(value) { page.all('.ci-annotation-sidebar.collapsed').count }
  end

  # ----- main tests -----

  def handle_report_load
    def wait_for_chart_render
      sleep 2
    end

    wait_for_chart_render
    sleep 3
    _have_rendered_all_relevant_annotations
    _have_sidebar_collapsed
  end

  def handle_sidebar_expansion
    def have_sidebar_expanded
      _find_sidebar to_be_present: true
    end

    _toggle_sidebar
    have_sidebar_expanded
  end

  def handle_chart_annotation_toggle
    def toggle_annotation
      sleep 0.5
      page.find('.ci-annotation-toggle', visible: false).click
    end

    def have_removed_annotations_on_chart
      expect(_chart_annotations.count).to eq 0
    end

    toggle_annotation
    have_removed_annotations_on_chart
    toggle_annotation
    _have_rendered_all_relevant_annotations
  end

  def handle_annotation_selected_on_sidebar
    def select_annotation_inside_group
      page.all('.ci-annotation-label')[1].click
    end

    def have_chart_colour_annotations_in_selected_group
      bg_colors = page.evaluate_script <<~JS
        [...document.querySelectorAll('.highcharts-annotation-group .highcharts-label')]
          .map(function (element) { return element.childNodes[0].attributes.fill.value; })
      JS
      expect(bg_colors[1..2]).to eq %w[#fff #2f9ce8]
    end

    select_annotation_inside_group
    have_chart_colour_annotations_in_selected_group
  end

  def handle_annotation_selected_on_chart
    def select_single_annotation_on_chart
      _chart_annotations[0].click
    end

    def have_one_annotation_on_sidebar_highlighted
      bg_colors = page.evaluate_script <<~JS
        [...document.querySelectorAll('.annotation-sidebar .annotation')]
          .map(function (element) { return element.classList.contains('active'); })
      JS

      expect(bg_colors).to eq [true, false, false]
    end

    select_single_annotation_on_chart
    have_one_annotation_on_sidebar_highlighted
  end

  def handle_sidebar_collapse
    _toggle_sidebar
    _have_sidebar_collapsed
  end

  # ----- main tests -----

  it 'should handle normal user interaction correctly' do
    safe_login admin, query_report_path(report)

    @annotations = Annotation.all

    handle_report_load
    handle_sidebar_expansion
    handle_chart_annotation_toggle
    handle_annotation_selected_on_sidebar
    handle_annotation_selected_on_chart
    handle_sidebar_collapse
  end

  context 'business users not shared annotations' do
    def have_working_chart
      wait_for_element_load('.chart')
      expect(page).to have_selector '.chart'
    end

    def have_no_sidebar
      expect(page).not_to have_selector 'annotation-sidebar'
    end

    def grant_report_permission_to_user
      FactoryBot.create :permission, actor_id: user.id, actor_class: 'User',
                                     subject_id: report.id, subject_class: 'QueryReport', action: 'read'
    end

    before do
      grant_report_permission_to_user
    end

    it 'should render chart as normal but the sidebar is disabled' do
      safe_login user, query_report_path(report)

      have_working_chart
      have_no_sidebar
    end
  end
end
