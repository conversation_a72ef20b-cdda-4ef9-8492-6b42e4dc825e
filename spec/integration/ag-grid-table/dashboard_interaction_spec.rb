# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'bring dashboard to view mode', :js do
  before do
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global('ag-grid:metric-sheet', true)
    FeatureToggle.toggle_global('table:interaction_on_dashboard', true)
    FeatureToggle.toggle_global('table:freeze_columns', true)
    FeatureToggle.toggle_global('table:hide_fields', true)
    FeatureToggle.toggle_global('table:remove_columns', true)

    FeatureToggle.toggle_global(Viz::Constants::FT_TABLE_V2, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_PIVOT_V2, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_DATE_DRILL, true)
    FeatureToggle.toggle_global('viz_scope:allow_any_date_scope', true)
    FeatureToggle.toggle_global('viz_result:persist_adhoc_settings', true)
  end

  def check_sort_group(header:, direction:)
    # sort
    open_context_menu(header)
    page.find('.ci-sort-column [data-icon="exchange-arrow"]').hover
    safe_click("[data-icon=\"arrow-#{direction}\"]")

    wait_for_all_ajax_requests
    wait_for_widget_load

    # check sort group
    header.find("[data-icon=\"arrow-#{direction}\"]").click
    wait_expect(true) do
      page.has_css?('.ci-sort-remove-btn')
    end

    # remove sort
    page.find('.ci-sort-remove-btn').click
    wait_for_all_ajax_requests
    wait_for_widget_load
  end

  def interact_on_view_mode
    table = page.find('[data-ci="ci-ag-grid-data-table"]')
    date_time_header = find_header_element_by_name(element: table, header_name: 'Date And Time')
    check_sort_group(header: date_time_header, direction: 'up')
    open_context_menu(date_time_header)
    safe_click('[data-icon="column-freeze"]')

    open_context_menu_by_name(element: table, header_name: 'Value')
    safe_click('.ci-remove-column')
  end

  def check_table_after_refresh
    wait_expect(true) do
      page.has_css?('[data-ci="ci-ag-grid-data-table"]')
    end

    table = page.find('[data-ci="ci-ag-grid-data-table"]')
    total_headers = table.all('.ag-header-cell').count
    expect(total_headers).to eq(viz_table_fields.count + 1)
  end

  def change_date_transformation
    safe_click('.metric-sheet-wrapper .setting-wrapper .hui-select-trigger', exact_text: 'Month')
    page.find('.hui-select-option', exact_text: 'Year').click
  end

  def apply_dashboard_filter
    safe_click('.ci-date-picker-input')
    # start date
    safe_click('.mx-calendar .cell.today.active')
    # end date
    safe_click('.mx-calendar .cell.today.active')
    safe_click('[data-ci="ci-submit-filters-btn"]')
  end

  def check_context_menu_add_new_column_reorder_permission(table:, can_interaction:)
    value_column = find_header_element_by_name(element: table, header_name: 'Value')
    # check reorder permission
    wait_expect(can_interaction) do
      value_column.find('[data-ci="ci-header-column-label"]').hover
      value_column.has_css?('.reorder-indicator')
    end

    # check add new column permission
    wait_expect(can_interaction) do
      table.has_css?('[data-ci="ci-add-new-column-indicator"]')
    end

    # check context menu
    open_context_menu(value_column)
    if can_interaction
      expect(page.all('.ci-table-header-dropdown .hui-popper-content > div').size).to eq 6
      expect(page.all('.ci-table-header-dropdown .hui-popper-content > div').map(&:text)).to eq [
        'Sort',
        'Insert column left',
        'Insert column right',
        'Freeze up to this column',
        'Hide in view',
        'Remove column',
      ]
    else
      expect(page.all('.ci-table-header-dropdown .hui-popper-content > div').size).to eq 2
      expect(page.all('.ci-table-header-dropdown .hui-popper-content > div').map(&:text)).to eq [
        'Sort ascending',
        'Sort descending',
      ]
    end
  end

  def expect_show_block_controls(header)
    header.click

    wait_expect(true) do
      page.has_css?('[data-ci="edit-block-btn"]')
    end
  end

  def expect_reorder(header_1, header_2)
    header_1.drag_to(header_2)

    wait_for_all_ajax_requests
    wait_for_widget_load

    wait_expect(true) do
      header_1.native.location.x > header_2.native.location.x
    end
  end

  def expect_select_range(table)
    first_cell = find_cell_element(table_element: table, row_id: 0, col_index: 1)
    second_cell = find_cell_element(table_element: table, row_id: 0, col_index: 2)
    first_cell.drag_to(second_cell)

    wait_expect(true) do
      first_cell[:class].include?('h-range-selection-cell') && second_cell[:class].include?('h-range-selection-cell')
    end
  end

  def expect_show_hidden_banner(can_interaction, header_names)
    wait_for_element_load do
      page.find_by_id('block-v1')
    end
    wait_for_element_load('.ci-table-report-data')

    wait_expect('All columns are set to hidden') do
      page.find('[data-ci="ci-all-hidden-fields-banner"] [data-hui-section="header"]').text
    end

    if can_interaction
      wait_expect(true) do
        page.has_css?('[data-ci="ci-all-hidden-fields-banner"] [data-hui-comp="button"]')
      end
      safe_click('[data-ci="ci-all-hidden-fields-banner"] [data-hui-comp="button"]')

      wait_expect(header_names) do
        page.all('.ag-header-cell').map(&:text)
      end
    else
      wait_expect(true) do
        page.has_no_css?('[data-ci="ci-all-hidden-fields-banner"] [data-hui-comp="button"]')
      end
    end
  end

  def expect_show_freeze_out_of_viewport_banner(can_interaction, header_names)
    wait_for_element_load do
      page.find_by_id('block-v1')
    end
    wait_for_element_load('.ci-table-report-data')

    banner_text = can_interaction ? "Not enough space to show all frozen columns\nUnfreeze all" : 'Not enough space to show all frozen columns'
    wait_expect(banner_text) do
      page.find('[data-ci="ci-freeze-out-of-viewport-banner"]').text
    end

    if can_interaction
      wait_expect(true) do
        page.has_css?('[data-ci="ci-freeze-out-of-viewport-banner"] [data-hui-comp="button"]')
      end
      safe_click('[data-ci="ci-freeze-out-of-viewport-banner"] [data-hui-comp="button"]')

      wait_expect(header_names) do
        page.all('.ag-header-cell').map(&:text).select { |name| name.present? }
      end
    else
      wait_expect(true) do
        page.has_no_css?('[data-ci="ci-all-hidden-fields-banner"] [data-hui-comp="button"]')
      end
    end
  end

  context 'canvas dashboard' do
    include_context 'dashboards_v4'

    let(:viz_field_datetime) do
      {
        'type' => 'datetime', 'format' => { 'type' => 'timestamp' },
        'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'date_and_time' },
        'uuid' => 'field_datetime',
      }
    end

    let(:viz_field_value) do
      {
        'type' => 'number', 'format' => { 'type' => 'number', 'format' => { 'pattern' => 'inherited' } },
        'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'value' },
        'uuid' => 'field_value_number',
      }
    end

    before do
      FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    end

    context 'Data table block' do
      let(:table_settings) do
        {
          'misc' => {
            'row_limit' => -1, 'pagination_size' => 25, 'show_row_number' => true,
            'column_width' => {
              'type' => 'auto',
            },
          },
          'aggregation' => { 'show_total' => false, 'show_average' => false },
          'conditional_formatting' => [],
        }
      end

      # TODO: uncomment when we allow interacting on reporting dashboard view mode
      xit 'can interact and reset all interactions in view mode' do
        qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}")
        wait_for_element_load do
          page.find_by_id('block-v1')
        end
        wait_for_element_load('.ci-table-report-data')

        interact_on_view_mode

        wait_expect(true) do
          page.has_css?('.ci-table-report-data .single-value-table')
        end

        # refresh the page = reset all interactions in view mode
        refresh

        check_table_after_refresh
      end

      context 'when disable FT table:interaction_on_dashboard' do
        before do
          FeatureToggle.toggle_global('table:interaction_on_dashboard', false)
          FeatureToggle.toggle_global('table:reorder_columns', true)
          FeatureToggle.toggle_global('table:add_new_columns', true)
          FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)

          definition_aml = <<~STR
            Dashboard unnamed {
              title: 'Unnamed Dashboard'
              description: ''''''
              view: CanvasLayout {
                label: 'View 1'
                height: 800
                block v1 {
                  position: pos(120, 120, 810, 330)
                }
              }
              block v1: VizBlock {
                viz: DataTable {
                  dataset: 'test_data_set'
                  fields: [
                    VizFieldFull {
                      ref: ref('new_sql_model', 'date_and_time')
                      format {
                        type: 'datetime'
                      }
                      uname: 'field_datetime'
                    },
                    VizFieldFull {
                      ref: ref('new_sql_model', 'value')
                      aggregation: 'sum'
                      format {
                        type: 'number'
                        pattern: 'inherited'
                      }
                      uname: 'field_value_number'
                    }
                  ]
                  settings {
                    frozen_columns: 2
                    column_width {
                      manual_widths: [
                      ]
                    }
                  }
                }
              }
            }


          STR

          pivot_definition_aml = <<~STR
            Dashboard unnamed {
              title: 'Unnamed Dashboard'
              description: ''''''
              view: CanvasLayout {
                label: 'View 1'
                height: 800
                block v1 {
                  position: pos(120, 120, 810, 330)
                }
              }
              block v1: VizBlock {
                viz: PivotTable {
                  dataset: 'test_data_set'
                  rows: [
                    VizFieldFull {
                      ref: ref('new_sql_model', 'date_and_time')
                      format {
                        type: 'datetime'
                      }
                      uname: 'field_datetime'
                    }
                  ]
                  columns: [
                    VizFieldFull {
                      ref: ref('new_sql_model', 'value')
                      aggregation: 'sum'
                      format {
                        type: 'number'
                        pattern: 'inherited'
                      }
                      uname: 'field_value_number'
                    }
                  ]
                  values: [
                    VizFieldFull {
                      ref: ref('public_dashboard_widgets', 'updated_at')
                      transformation: 'datetrunc quarter'
                      format {
                        type: 'date'
                        pattern: 'yyyy Qq'
                      }
                      uname: 'datetrunc_quarter_updated_at'
                    }
                  ]
                  settings {
                    frozen_columns: 2
                    column_width {
                      manual_widths: [
                      ]
                    }
                  }
                }
              }
            }
          STR

          dashboard_table_no_timezone.update!(definition_aml: definition_aml)
          dashboard_table_with_coordinates.update!(definition_aml: definition_aml)
          dashboard_table_with_pivot_table.update!(definition_aml: pivot_definition_aml)
        end

        shared_examples 'limited table interactions' do |path_suffix, can_interaction, expand_block|
          it 'only show sorts, does not allow reordering, adding new columns' do
            qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}#{path_suffix}")
            wait_for_element_load do
              page.find_by_id('block-v1')
            end
            wait_for_element_load('.ci-table-report-data')

            if expand_block
              # Expand the block
              block = page.find_by_id('block-v1')
              block.click
              safe_click('[data-ci="expand-block-btn"]')

              wait_for_element_load('.ci-table-report-data')
            end

            check_context_menu_add_new_column_reorder_permission(table: table, can_interaction: can_interaction)
          end

          context 'when hidding all fields' do
            before do
              FeatureToggle.toggle_global('table:hide_fields', true)
              FeatureToggle.toggle_global('pivot:hide_fields', true)

              dashboard_table_no_timezone.definition['blocks'][0]['viz']['viz_setting']['fields']['table_fields'].each do |field|
                field['is_hidden'] = true
              end
              dashboard_table_no_timezone.save!

              dashboard_table_with_pivot_table.definition['blocks'][0]['viz']['viz_setting']['fields']['pivot_data']['rows'].each do |field|
                field['is_hidden'] = true
              end
              dashboard_table_with_pivot_table.definition['blocks'][0]['viz']['viz_setting']['fields']['pivot_data']['columns'] = []
              dashboard_table_with_pivot_table.definition['blocks'][0]['viz']['viz_setting']['fields']['pivot_data']['values'] = []
              dashboard_table_with_pivot_table.save!
            end

            it 'should show hidden banner and CTA if needed on table' do
              qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}#{path_suffix}")

              expect_show_hidden_banner(can_interaction, ['', 'Date And Time', 'Value', ''])
            end

            it 'should show hidden banner and CTA if needed on pivot' do
              qlogin(admin, "/dashboards/v4/#{dashboard_table_with_pivot_table.id}#{path_suffix}")

              expect_show_hidden_banner(can_interaction, ['Date And Time'])
            end
          end


          context 'when freeze out of viewport' do
            before do
              FeatureToggle.toggle_global('table:freeze_columns', true)
              FeatureToggle.toggle_global('pivot:freeze_columns', true)

              dashboard_table_no_timezone.definition['blocks'][0]['viz']['viz_setting']['settings']['misc']['column_width']['manual_widths'] = {
                "field_datetime" => 200,
                "field_value_number" => 200,
              }
              dashboard_table_no_timezone.definition['blocks'][0]['viz']['viz_setting']['settings']['misc']['column_freeze'] = 2
              dashboard_table_no_timezone.save!

              dashboard_table_with_pivot_table.definition['blocks'][0]['viz']['viz_setting']['settings']['misc']['column_width']['manual_widths'] = {
                "field_datetime" => 400,
              }
              dashboard_table_with_pivot_table.definition['blocks'][0]['viz']['viz_setting']['settings']['misc']['column_freeze'] = 1
              dashboard_table_with_pivot_table.save!
            end

            it 'shows the freeze out of viewport banner on table' do
              qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}#{path_suffix}")
              wait_for_element_load do
                page.find_by_id('block-v1')
              end
              expect_show_freeze_out_of_viewport_banner(can_interaction, ['Date And Time', 'Value'])
            end

            it 'shows the freeze out of viewport banner on pivot' do
              qlogin(admin, "/dashboards/v4/#{dashboard_table_with_pivot_table.id}#{path_suffix}")
              wait_for_element_load do
                page.find_by_id('block-v1')
              end
              expect_show_freeze_out_of_viewport_banner(can_interaction, ["Value", "Value", "Value", "Value", "Date And Time"])
            end
          end
        end

        # reporting mode
        it_behaves_like 'limited table interactions', '', false, false do
          let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }
        end

        # reporting edit mode
        it_behaves_like 'limited table interactions', '/edit', true, false do
          let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }
        end

        # reporting mode with expanded block
        it_behaves_like 'limited table interactions', '', false, true do
          let(:table) { page.find('.ci-expanded-block [data-ci="ci-ag-grid-data-table"]') }
        end

        # reporting edit mode with expanded block
        it_behaves_like 'limited table interactions', '/edit', true, true do
          let(:table) { page.find('.ci-expanded-block [data-ci="ci-ag-grid-data-table"]') }
        end

        it 'allow interaction when creating new vizblock' do
          qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}/edit")
          wait_for_element_load do
            page.find_by_id('block-v1')
          end
          wait_and_click('[data-ci="add-viz-block"]')
          safe_click('[data-value="new_sql_model-date_and_time"]')
          safe_click('[data-value="new_sql_model-value"]')
          safe_click('[data-ci="ci-explorer-control-get-results"]')
          wait_for_loading_finish
          table = page.find('.viz-result-section [data-ci="ci-ag-grid-data-table"]')
          check_context_menu_add_new_column_reorder_permission(table: table, can_interaction: true)
        end

        it 'shows block controls and can reorder, select range' do
          qlogin(admin, "/dashboards/v4/#{dashboard_table_with_coordinates.id}/edit")
          wait_for_element_load do
            page.find_by_id('block-v1')
          end

          table = page.find('[data-ci="ci-ag-grid-data-table"]')
          date_time_header = find_header_element_by_name(element: table, header_name: 'Date And Time')
          value_header = find_header_element_by_name(element: table, header_name: 'Value')

          expect_show_block_controls(date_time_header)

          expect_reorder(date_time_header, value_header)

          expect_select_range(table)
        end
      end

      context 'shareable link' do
        before do
          FeatureToggle.toggle_global('table:add_new_columns', true)
        end

        it 'does not show add column indicator' do
          qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}")
          wait_for_element_load do
            page.find_by_id('block-v1')
          end

          # TODO: uncomment when we allow interacting on reporting dashboard view mode
          # wait_expect(true) do
          #   page.has_css?('[col-id="add-new-column"]')
          # end

          page.find('button', text: 'Share').click
          page.find('div.cursor-pointer', text: 'Shareable Links').click
          wait_for_element_load('.shareable-link-manage')
          page.find('button', text: 'New Shareable Link').click
          wait_for_element_load('.shareable-link-edit-modal')
          page.find('.h-modal button', text: 'Save').click

          wait_for_all_ajax_requests
          sl = ShareableLink.last
          visit "#{dashboard_path(dashboard_table_no_timezone)}?_pl=#{sl.hash_code}"
          wait_for_element_load('#block-v1')

          wait_expect(true) do
            page.has_no_css?('[col-id="add-new-column"]')
          end

          table = page.find('[data-ci="ci-ag-grid-data-table"]')
          check_context_menu_add_new_column_reorder_permission(table: table, can_interaction: false)
        end
      end
    end

    context 'Metric sheet block' do
      before do
        FeatureToggle.toggle_global('viz_interaction:persist_adhoc_actions', true)
      end

      it 'still persist date transformation after applying dashboard filter' do
        qlogin(admin, "/dashboards/v4/#{dashboard_ms_no_timezone.id}")

        change_date_transformation
        wait_for_widget_load

        apply_dashboard_filter
        wait_for_widget_load

        expect(page.all('.metric-sheet-wrapper .setting-wrapper .hui-select-trigger')[0].text).to eq('Year')
      end
    end
  end

  def find_header(header)
    table = page.find('[data-ci="ci-ag-grid-data-table"]')
    table.find('.ag-header-cell', text: header)
  end

  def sort(header, dir)
    header = find_header(header)
    header.hover
    header.find('[data-ci="ci-angle-down"]').click

    # Interaction mode: sort options is a inside sub-menu of context menu => need to check .ci-sort-column
    # Sort only: sort is in context menu
    if page.all('.ci-sort-column').size > 0
      page.find('.ci-sort-column [data-icon="exchange-arrow"]').hover
    end
    safe_click("[data-icon=\"arrow-#{dir}\"]")
  end

  def date_drill(label)
    page.find('.result-viz').right_click
    page.find('.h-context-menu-content .ci-date-drill').hover
    page.find(".ci-date-drill-label-#{label}").click
    wait_for_all_ajax_requests
  end

  context 'dashboard with timezone' do
    include_context 'timezone_dynamic_dashboard'

    shared_examples 'persisting states' do
      it 'keeps sort after date-drilling' do
        safe_login(admin, dashboard_path(dashboard))

        wait_for_widget_load
        date_drill('month')
        wait_for_widget_load
        sort('Val', 'down')
        wait_expect(true) { find_header('Val').has_css?('[data-ci="ci-arrow-down"]') }

        date_drill('quarter')
        wait_for_widget_load
        wait_expect(true) { find_header('Val').has_css?('[data-ci="ci-arrow-down"]') }
      end
    end

    context 'dashboard_v3' do
      it_behaves_like 'persisting states' do
        let(:dashboard) { timezone_dashboard }

        before do
          FeatureToggle.toggle_global('viz_interaction:persist_adhoc_actions', true)
          Capybara.current_window.resize_to 1600, 1200
        end
      end
    end

    context 'dashboard_v4' do
      include_context 'dashboard_v4_timezone_context'

      it_behaves_like 'persisting states' do
        let(:dashboard) do
          create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, 'Asia/Bangkok')
        end

        before do
          FeatureToggle.toggle_global('viz_interaction:persist_adhoc_actions', true)
        end
      end
    end
  end
end
