# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'render data table using ag-grid', :js do
  def remove_column(table:, header_name:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-remove-column')
    wait_for_all_ajax_requests
  end

  def remove_transposed_column(table:)
    cell = find_cell_element(table_element: table, row_index: 'b-0', col_index: 4, row_id: 'b-0')
    cell.right_click
    safe_click('.ci-remove-column')
    wait_for_all_ajax_requests
  end

  def expect_no_toast_when_toggle_pop(table_selector:)
    # toggling off pop does not show toast
    safe_click('.ci-pop-setting [data-ci="ci-enable-pop"]')
    safe_click('[data-ci="ci-explorer-control-get-results"]')
    wait_for_all_ajax_requests
    wait_for_element_load(table_selector)
    wait_expect(true) do
      page.has_no_css?('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
    end


    # toggle on pop and remove
    safe_click('.ci-pop-setting [data-ci="ci-enable-pop"]')
    safe_click('[data-ci="ci-explorer-control-get-results"]')
    wait_for_all_ajax_requests
    wait_for_element_load(table_selector)
    wait_expect(true) do
      page.has_no_css?('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
    end
  end

  let(:admin) { get_test_admin }
  let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }
  let(:pivot) { page.find('[data-ci="ci-ag-grid-pivot-table"]') }

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('pivot:transpose', true)
      FeatureToggle.toggle_global('table:remove_columns', true)
      FeatureToggle.toggle_global('pivot:remove_columns', true)
      FeatureToggle.toggle_global('pop:enabled', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
    end

    context 'in data table' do
      it 'removes columns in data table properly' do
        click_on_first_fields(5)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        expect(page.all('.ag-header-cell').map(&:text).include?('Discount')).to be true

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        resize_column_by_name(element: table, header_name: 'Created At', right: -100)
        created_at_header_width = find_header_element_by_name(element: table, header_name: 'Created At').native.size.width
        remove_column(table: table, header_name: 'Discount')
        expect(page.all('.ag-header-cell').map(&:text).include?('Discount')).to be false
        expect(find_header_element_by_name(element: table, header_name: 'Created At').native.size.width).to eq(created_at_header_width)
      end

      it 'shows banner when removing a metric with pop columns' do
        create_pop_data_table_viz_setting
        expect_no_toast_when_toggle_pop(table_selector: '[data-ci="ci-ag-grid-data-table"]')

        remove_column(table: table, header_name: 'Sum of Discount')
        wait_expect(true) do
          page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text.include?('Removing a measure will also remove its linked Period Comparison columns.')
        end
      end
    end

    context 'in pivot table' do
      before do
        page.find('.ci-viz-type-pivot_table').click
      end

      shared_examples 'remove row fields in pivot table' do |metric_on_row|
        it 'works' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

          expect(page.all('.ag-header-cell').map(&:text).include?('Quarter Created At')).to be true

          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          remove_column(table: pivot, header_name: 'Quarter Created At')
          expect(page.all('.ag-header-cell').map(&:text).include?('Quarter Created At')).to be false
        end

        it 'does not show context menu if value fields are undefined' do
          create_pivot_viz_setting(has_value_fields: false)
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          empty_columns = pivot.all('.ag-header-cell', exact_text: '')

          wait_expect(true) do
            empty_columns.all? do |c|
              c.hover
              c.has_no_css?('[data-ci="ci-angle-down"]')
            end
          end
        end

        it 'shows banner when removing a metric with pop columns' do
          create_pivot_viz_setting(pop: true)
          expect_no_toast_when_toggle_pop(table_selector: '[data-ci="ci-ag-grid-pivot-table"]')

          if metric_on_row
            remove_transposed_column(table: pivot)
          else
            remove_column(table: pivot, header_name: 'Sum of Quantity')
          end

          wait_expect(true) do
            page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text.include?('Removing a measure will also remove its linked Period Comparison columns.')
          end
        end
      end

      describe 'metrics on row' do
        before do
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'remove row fields in pivot table', true
      end

      describe 'metrics on column' do
        it_behaves_like 'remove row fields in pivot table', false
      end
    end
  end
end
