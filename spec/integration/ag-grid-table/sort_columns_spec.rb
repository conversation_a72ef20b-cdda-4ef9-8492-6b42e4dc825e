# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Sort columns', :js do
  before do
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)

    FeatureToggle.toggle_global('table:rename_columns', true) # enable new context menu
    FeatureToggle.toggle_global('table:remove_columns', true)
    FeatureToggle.toggle_global('viz:table_v2', true) # enable multiple sorts
    FeatureToggle.toggle_global('viz:pivot_v2', true) # enable multiple sorts
    FeatureToggle.toggle_global('pivot:transpose', true)
  end

  def sort_column(table:, header_name:)
    header = table.find('.ag-header-cell', exact_text: header_name)
    open_context_menu(header)
    if page.all('.ci-table-header-dropdown [data-icon="exchange-arrow"]').count > 0
      page.find('.ci-table-header-dropdown [data-icon="exchange-arrow"]').hover
    end
    safe_click('[data-icon="arrow-up"]')
  end

  def check_sort_icon(table:, header_name:, sort_direction:)
    header = table.find('.ag-header-cell', exact_text: header_name)

    wait_expect(true) { header.has_css?("[data-icon=\"arrow-#{sort_direction}\"]") }
  end

  context 'edit in canvas dashboard and save' do
    include_context 'dashboards_v4'

    let(:viz_field_datetime) do
      {
        'type' => 'datetime', 'format' => { 'type' => 'timestamp' },
        'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'date_and_time' },
        'uuid' => 'field_datetime',
      }
    end
    let(:viz_field_value) do
      {
        'type' => 'number', 'format' => { 'type' => 'number', 'format' => { 'pattern' => 'inherited' } },
        'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'value' },
        'uuid' => 'field_value_number',
      }
    end
    let(:table_settings) do
      {
        'misc' => {
          'column_width' => {
            'type' => 'auto',
            'manual_widths' => {},
          },
          'show_row_number' => true,
        },
        'conditional_formatting' => [],
      }
    end

    shared_examples 'can sort and reorder correctly' do
      it 'works' do
        qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}")
        wait_for_element_load('[data-icon="edit"]')
        safe_click('[data-icon="edit"]')

        wait_for_element_load do
          page.find_by_id('block-v1')
        end

        wait_for_element_load('.ci-table-report-data')
        table = page.find(table_selector)
        sort_column(table: table, header_name: 'Date And Time')

        check_sort_icon(table: table, header_name: 'Date And Time', sort_direction: 'up')
        check_column_highlight(element: table, header_name: 'Date And Time')

        wait_for_all_ajax_requests
        safe_click("[data-icon='save']")

        wait_for_element_load '[data-ci="ci-export-dropdown"]'
        wait_for_element_load do
          page.find_by_id('block-v1')
        end
        wait_for_element_load('.ci-table-report-data')

        check_sort_icon(table: table, header_name: 'Date And Time', sort_direction: 'up')
      end
    end

    context 'in data table' do
      before do
        definition_aml = <<~STR
          Dashboard table_timezone {
            title: 'Unnamed Dashboard'
            description: ''''''
            view: CanvasLayout {
              label: 'View 1'
              height: 800
              block v1 {
                position: pos(0, 0, 810, 330)
              }
              block v2 {
                position: pos(400, 900, 810, 330)
              }
            }
            block v1: VizBlock {
              viz: DataTable {
                dataset: 'test_data_set'
                fields: [
                  VizFieldFull {
                    ref: ref('new_sql_model', 'date_and_time')
                    format {
                      type: 'datetime'
                    }
                    uname: 'field_datetime'
                  },
                  VizFieldFull {
                    ref: ref('new_sql_model', 'value')
                    aggregation: 'sum'
                    format {
                      type: 'number'
                      pattern: 'inherited'
                    }
                    uname: 'field_value_number'
                  }
                ]
                settings {
                  column_width {
                  }
                }
              }
            }
          }
        STR
        dashboard_table_no_timezone.update!(definition_aml: definition_aml)
      end

      it_behaves_like 'can sort and reorder correctly' do
        let(:table_selector) { '[data-ci="ci-ag-grid-data-table"]' }
      end
    end

    context 'in pivot table' do
      let(:viz_field_sum_value) do
        {
          **viz_field_value,
          'aggregation' => 'sum',
        }
      end

      let(:table_settings) do
        {
          'misc' => {
            'column_width' => {
              'type' => 'auto',
              'manual_widths' => {
                'field_datetime' => 100,
                'field_value_number' => 240,
              },
            },
            'value_labels_position' => {
              'placement' => 'rows',
            },
          },
          'conditional_formatting' => [],
        }
      end

      let(:viz_block_table) do
        { 'viz' =>
              { 'dataset_id' => data_set_timezone.id,
                'viz_setting' =>
                { 'id' => nil,
                  'key' => nil,
                  'fields' => {
                    'pivot_data' => {
                      'columns' => [{ **viz_field_datetime, 'uuid' => 'field_datetime2' }],
                      'rows' => [viz_field_datetime],
                      'values' => [**viz_field_value, 'aggregation' => 'sum'],
                    },
                  },
                  'format' => {},
                  'filters' => [],
                  'settings' => table_settings,
                  'viz_type' => 'pivot_table',
                  'source_id' => nil,
                  'tenant_id' => nil,
                  'created_at' => nil,
                  'updated_at' => nil,
                  'source_type' => nil,
                  'adhoc_fields' => [],
                  'custom_chart_id' => nil, }, },
          'type' => 'VizBlock',
          'label' => nil,
          'uname' => 'v1',
          'settings' => setting_viz_block_table,
          'description' => nil, }
      end

      before do
        definition_aml = <<~STR
          Dashboard table_timezone {
            title: 'Unnamed Dashboard'
            description: ''''''
            view: CanvasLayout {
              label: 'View 1'
              height: 800
              block v1 {
                position: pos(0, 0, 810, 330)
              }
            }
            block v1: VizBlock {
              viz: PivotTable {
                dataset: 'test_data_set'
                rows: [
                  VizFieldFull {
                    ref: ref('new_sql_model', 'date_and_time')
                    format {
                      type: 'datetime'
                    }
                    uname: 'field_datetime'
                  }
                ]
                columns: [
                  VizFieldFull {
                    ref: ref('new_sql_model', 'date_and_time')
                    format {
                      type: 'datetime'
                    }
                    uname: 'field_datetime2'
                  }
                ]
                values: [
                  VizFieldFull {
                    ref: ref('new_sql_model', 'value')
                    aggregation: 'sum'
                    format {
                      type: 'number'
                      pattern: 'inherited'
                    }
                    uname: 'field_value_number'
                  }
                ]
                settings {
                  column_width {
                    manual_widths: [
                    ]
                  }
                }
              }
            }
          }
        STR
        dashboard_table_no_timezone.update!(definition_aml: definition_aml)
      end

      describe 'metrics on row' do
        it_behaves_like 'can sort and reorder correctly' do
          let(:table_selector) { '[data-ci="ci-ag-grid-pivot-table"]' }
        end
      end
    end
  end

  context 'reorder and sort in data exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('table:reorder_columns', true)
      FeatureToggle.toggle_global('out-of-sync:show-banner', true)
      FeatureToggle.toggle_global('out-of-sync:block-table-interaction', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
    end

    let(:admin) { get_test_admin }

    def reorder_columns(table:)
      discount = find_header_element_by_name(element: table, header_name: 'Discount')
      created_at = find_header_element_by_name(element: table, header_name: 'Created At')
      discount.drag_to created_at
    end

    it 'does not show out-of-sync banner' do
      click_on_first_fields(6)
      safe_click('[data-ci="ci-explorer-control-get-results"]')

      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      reorder_columns(table: table)

      sort_column(table: page, header_name: 'Discount')
      # table interaction updates sort automatically so we don't need banner
      wait_expect(false) { page.has_css?('[data-ci="out-of-sync-banner"]') }
      wait_expect(true) { page.has_css?('.ci-sort-column-select', exact_text: 'Discount') }
      safe_click('[data-ci="ci-explorer-control-get-results"]')

      check_sort_icon(table: page, header_name: 'Discount', sort_direction: 'up')
    end
  end
end
