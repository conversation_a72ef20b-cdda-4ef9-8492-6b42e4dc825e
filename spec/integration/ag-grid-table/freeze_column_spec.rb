# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'freeze column', js: true do
  let(:admin) { get_test_admin }
  let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }
  let(:pivot) { page.find('[data-ci="ci-ag-grid-pivot-table"]') }

  def test_fields_order_by_freezing(table:)
    column = find_header_element_by_name(element: table, header_name: 'Product id')
    draggable_node = column.find(:xpath, '..')

    # freeze column by dragging them near the right edge of frozen range
    drag_to_coordinates(draggable_node, right: -180, bottom: 0, duration: 3)

    expect(page.all('.field-label').map(&:text)).to eq [
      'Created At',
      'Discount',
      'Product id',
      'Id',
      'Is Deleted',
      'Quantity',
    ]
    safe_click('[data-ci="ci-viz-setting-tab-styles"]')
    freeze_columns = page.find('[data-ci="ci-column-freeze"] input').value
    expect(freeze_columns).to eq '3'
  end

  def set_total_frozen_columns_by_viz_style(table:, total_freeze_columns:)
    safe_click('[data-ci="ci-viz-setting-tab-styles"]')
    input = page.find('[data-ci="ci-column-freeze"] input')
    input.set(total_freeze_columns.to_s)
    sleep 0.5 # wait for table flash renders
  end

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('pivot:transpose', true)
      FeatureToggle.toggle_global('table:freeze_columns', true)
      FeatureToggle.toggle_global('pivot:freeze_columns', true)
      FeatureToggle.toggle_global('table:reorder_columns', true)
      FeatureToggle.toggle_global('table:hide_fields', true)
      FeatureToggle.toggle_global('pivot:hide_fields', true)
      FeatureToggle.toggle_global('table:single_row', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
    end


    shared_examples 'freeze out of viewport' do |type|
      before do
        if type == 'table'
          click_on_first_fields(8)
          safe_click('[data-ci="ci-explorer-control-get-results"]')
          set_total_frozen_columns_by_viz_style(table: table, total_freeze_columns: 8)
        else type == 'pivot'
          create_pivot_viz_setting
          # append more row fields to grow the frozen ranges
          select_h_select_option '.pivot-section-rows .ci-empty-field', value: '1$!created_at'
          select_h_select_option '.pivot-section-rows .ci-empty-field', value: '1$!created_at'
          safe_click('.ci-collapsed-option')
          safe_click('[data-ci="ci-explorer-control-get-results"]')
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
        end
      end

      it 'shows banner' do
        wait_expect("Not enough space to show all frozen columns. Resize window, or adjust the frozen columns.\nUnfreeze all") do
          page.find('[data-ci="ci-freeze-out-of-viewport-banner"]').text
        end
      end

      it 'hides the banner when resize window' do
        safe_click('.ci-collapse-panel')
        wait_expect(true) do
          page.has_no_css?('[data-ci="ci-freeze-out-of-viewport-banner"]')
        end
      end

      it 'hides the banner when adjusting the frozen columns' do
        set_total_frozen_columns_by_viz_style(table: type == 'table' ? table : pivot, total_freeze_columns: 1)
        wait_expect(true) do
          page.has_no_css?('[data-ci="ci-freeze-out-of-viewport-banner"]')
        end
      end

      it 'hides the banner when unfreeze all' do
        safe_click('[data-ci="ci-freeze-out-of-viewport-banner"] [data-hui-comp="button"]')
        wait_expect(true) do
          page.has_no_css?('[data-ci="ci-freeze-out-of-viewport-banner"]')
        end
      end

      it 'hides the banner when hidding columns' do
        created_at = find_header_element_by_name(element: type == 'table' ? table : pivot, header_name: type == 'table' ? 'Created At' : 'Year Created At')
        created_at.right_click
        safe_click('.ci-hide-column')
        if type == 'pivot' && metrics_on_row
          created_at = find_header_element_by_name(element: type == 'table' ? table : pivot, header_name: 'Quarter Created At')
          created_at.right_click
          safe_click('.ci-hide-column')
        end
        wait_expect(true) do
          page.has_no_css?('[data-ci="ci-freeze-out-of-viewport-banner"]')
        end
      end
    end

    context 'in table' do
      it 'freezes by context menu' do
        click_on_first_fields(6)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        safe_click('[data-ci="ci-viz-setting-tab-styles"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        test_freeze_by_context_menu(table: table, total_freeze_columns: 1)
        test_freeze_by_context_menu(table: table, total_freeze_columns: 2)
      end

      it 'freezes by viz style tab' do
        click_on_first_fields(6)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        safe_click('[data-ci="ci-viz-setting-tab-styles"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        test_freeze_by_viz_style(table: table, total_freeze_columns: 1)
        test_freeze_by_viz_style(table: table, total_freeze_columns: 2)
      end

      context 'when combining with range selection' do
        it 'freezes by range selection' do
          click_on_first_fields(6)
          safe_click('[data-ci="ci-explorer-control-get-results"]')
          safe_click('[data-ci="ci-viz-setting-tab-styles"]')

          table = page.find('[data-ci="ci-ag-grid-data-table"]')
          test_freeze_by_viz_style(table: table, total_freeze_columns: 2)
          safe_click('.ci-viz-setting-tab-settings')

          test_fields_order_by_freezing(table: table)
        end
      end

      it_behaves_like 'freeze out of viewport', 'table'
    end

    context 'in pivot' do
      before do
        safe_click('.ci-collapse-panel')
        page.find('.ci-viz-type-pivot_table').click
      end

      let!(:metrics_on_row) { false }

      shared_examples 'pivot freeze correctly' do
        it 'freezes by context menu' do
          create_pivot_viz_setting
          safe_click('[data-ci="ci-viz-setting-tab-styles"]')

          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          # auto mode freeze all row fields
          total_row_fields = page.all('.ag-header-cell .pivot-column-fields-and-row-fields-header').size
          cell = find_cell_element(table_element: pivot, row_id: '0', col_index: total_row_fields)
          expect(cell[:class].include?('ag-cell-last-left-pinned')).to be true

          test_freeze_by_context_menu(table: pivot, total_freeze_columns: 3) if metrics_on_row
          test_freeze_by_context_menu(table: pivot, total_freeze_columns: 2)
          test_freeze_by_context_menu(table: pivot, total_freeze_columns: 1)
        end

        it 'freezes by viz style tab' do
          create_pivot_viz_setting
          safe_click('[data-ci="ci-viz-setting-tab-styles"]')

          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          test_freeze_by_viz_style(table: pivot, total_freeze_columns: 1)
          check_empty_header(table: pivot)
          test_freeze_by_viz_style(table: pivot, total_freeze_columns: 2)

          if metrics_on_row
            test_freeze_by_viz_style(table: pivot, total_freeze_columns: 3)
            test_freeze_by_viz_style(table: pivot, total_freeze_columns: 4)
          end
        end

        it_behaves_like 'freeze out of viewport', 'pivot'
      end

      describe 'metrics as row' do
        before do
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'pivot freeze correctly' do
          let!(:metrics_on_row) { true }
        end
      end

      describe 'metrics as column' do
        it_behaves_like 'pivot freeze correctly' do
          let!(:metrics_on_row) { false }
        end
      end
    end
  end
end

def test_freeze_by_context_menu(table:, total_freeze_columns:)
  headers = find_header_elements(element: table)
  last_index = table['data-ci'] == 'ci-ag-grid-data-table' ? total_freeze_columns : total_freeze_columns - 1
  header = headers[last_index]
  open_context_menu(header)
  safe_click('[data-icon="column-freeze"]')

  cell = find_cell_element(table_element: table, row_id: '0', col_index: last_index + 1)
  expect(cell[:class].include?('ag-cell-last-left-pinned')).to be true

  freeze_columns = page.find('[data-ci="ci-column-freeze"] input').value
  expect(freeze_columns).to eq total_freeze_columns.to_s
end

def test_freeze_by_viz_style(table:, total_freeze_columns:)
  set_total_frozen_columns_by_viz_style(table: table, total_freeze_columns: total_freeze_columns)

  last_index = table['data-ci'] == 'ci-ag-grid-data-table' ? total_freeze_columns : total_freeze_columns - 1
  cell = find_cell_element(table_element: table, row_id: '0', col_index: last_index + 1)
  expect(cell[:class].include?('ag-cell-last-left-pinned')).to be true
end

def check_empty_header(table:)
  wait_expect(['', '']) do
    page.all('.ag-header-viewport .ag-header-row-column-group .ag-header-group-cell:first-child').map(&:text)
  end
end
