# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'add and edit calculation fields', :js do
  let(:admin) { get_test_admin }
  let!(:metrics_on_row) { false }
  let!(:header_selector) do
    metrics_on_row ? '.pivot-transposed-header' : '.ag-header-cell'
  end
  let!(:context_menu_selector) do
    metrics_on_row ? '[data-ci="ci-angle-right"]' : '[data-ci="ci-angle-down"]'
  end

  before do
    Capybara.current_window.resize_to 1600, 1000
  end

  def add_pot(table:, header_name:)
    header = table.all(header_selector, exact_text: header_name).first

    header.hover
    header.find(context_menu_selector).click
    page.find('.ci-add-calculation').hover
    safe_click('.ci-percent-of-total')
  end

  def add_mc(table:, header_name:)
    header = table.all(header_selector, exact_text: header_name).first

    header.hover
    header.find(context_menu_selector).click
    page.find('.ci-add-calculation').hover
    safe_click('.ci-moving-calculations')
  end

  def edit_mc(table:, header_name:)
    header = table.all(header_selector, exact_text: header_name).first

    header.hover
    header.find(context_menu_selector).click
    safe_click('.ci-edit-calculation')

    safe_click('[data-ci="ci-advanced-calculation-form"] .ci-normal-field')
    safe_click('.hui-select-option[data-value="min"]')
    safe_click('[data-ci="ci-advanced-calculation-form"] .hui-btn', exact_text: 'Save')
  end

  def remove_dimension_field(table:, header_name:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-remove-column')
  end

  def check_error_details_expand_collapse
    safe_click('[data-ci="ci-viz-aql-compile-error"] [data-ci="ci-view-error-details"]')
    expect(page.has_css?('[data-ci="ci-viz-aql-compile-error-accordion"] [data-hui-comp="banner"]')).to eq true
    safe_click('[data-ci="ci-viz-aql-compile-error-accordion"] [data-hui-section="trigger"]')
    expect(page.has_no_css?('[data-ci="ci-viz-aql-compile-error-accordion"] [data-hui-comp="banner"]')).to eq true
  end

  shared_examples 'add and edit' do
    it 'works with moving calculations' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      if metrics_on_row
        safe_click('[data-ci="ci-metrics-as-row-field"]')
        safe_click('.ci-metrics-as-row-field-rows')
      end

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      table_class = viz_setting_aql.viz_type == 'data_table' ? '[data-ci="ci-ag-grid-data-table"]' : '[data-ci="ci-ag-grid-pivot-table"]'
      wait_for_element_load(table_class)

      table = page.find(table_class)

      wait_expect(false) { page.all('.ag-header-cell').map(&:text).include?('MovAvg of Sum of Quantity') }
      add_mc(table: table, header_name: 'Sum of Quantity')
      wait_for_all_holistics_loadings
      wait_expect(true) { page.has_no_css?('.h-loading-cell') } # updated vizsetting
      wait_expect(true) { page.all(header_selector).map(&:text).include?('MovAvg of Sum of Quantity') }

      edit_mc(table: table, header_name: 'MovAvg of Sum of Quantity')
      wait_expect(true) { page.all(header_selector).map(&:text).include?('MovAvg of Sum of Quantity') }
      unless metrics_on_row
        check_column_highlight(element: table, header_name: 'MovAvg of Sum of Quantity')
      end
    end

    it 'works with percent of total' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      if metrics_on_row
        safe_click('[data-ci="ci-metrics-as-row-field"]')
        safe_click('.ci-metrics-as-row-field-rows')
      end

      table_class = viz_setting_aql.viz_type == 'data_table' ? '[data-ci="ci-ag-grid-data-table"]' : '[data-ci="ci-ag-grid-pivot-table"]'
      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load(table_class)

      table = page.find(table_class)

      wait_expect(false) { page.all(header_selector).map(&:text).include?('PoT Sum of Quantity') }

      resize_column_by_name(element: table, header_name: 'Month Created At', right: -100)
      month_created_at_header_width = find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width

      unless metrics_on_row
        quantity_header = page.first('.ag-header-cell', exact_text: 'Sum of Quantity')
        resize_column(header: quantity_header, right: 100)
        sum_of_quantity_header_width = quantity_header.native.size.width
      end


      add_pot(table: table, header_name: 'Sum of Quantity')

      wait_expect(true) { page.all(header_selector).map(&:text).include?('PoT Sum of Quantity') }

      # should not change width after adding new columns
      expect(find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width).to eq(month_created_at_header_width)
      unless metrics_on_row
        expect(page.first('.ag-header-cell', exact_text: 'Sum of Quantity').native.size.width).to eq(sum_of_quantity_header_width)
        wait_expect(true) { page.first('.ag-header-cell', exact_text: 'PoT Sum of Quantity').native.size.width != 100 }
      end
    end
  end

  context 'pivot table in exploration' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'pivot_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          pivot_data: {
            rows: [
              {
                custom_label: nil,
                format: {
                  sub_type: 'mmm yyyy',
                  type: 'date',
                },
                path_hash: {
                  field_name: 'created_at',
                  model_id: 'data_modeling_orders',
                },
                transformation: 'datetrunc month',
                type: 'date',
                uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
              },
            ],
            columns: [
              {
                custom_label: nil,
                format: {
                  format: {},
                  type: 'string',
                },
                path_hash: {
                  field_name: 'status',
                  model_id: 'data_modeling_orders',
                },
                type: 'string',
                uuid: '4cbf1f68-ee0c-4610-bba6-731ee907a446',
              },
            ],
            values: [
              {
                aggregation: 'sum',
                custom_label: nil,
                format: {
                  format: {
                    pattern: 'inherited',
                  },
                  type: 'number',
                },
                path_hash: {
                  field_name: 'quantity',
                  model_id: 'data_modeling_orders',
                },
                type: 'number',
                uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
              },
            ],
          },
        },
        settings: {},
        filters: [],
      )
    end

    before do
      ThreadContext.set(:current_user, admin)
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('pivot:transpose', true)
      FeatureToggle.toggle_global('pivot:add_edit_calculations', true)
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global('moving_calculation:enabled', true)
      FeatureToggle.toggle_global('percent_of_total:enabled', true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)
      FeatureToggle.toggle_global('table:single_row', true)

      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
    end

    it_behaves_like 'add and edit'

    describe 'metrics on row' do
      it_behaves_like 'add and edit' do
        let!(:metrics_on_row) { true }
      end
    end
  end

  context 'data table in exploration' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
            },
          ],
        },
        settings: {},
        filters: [],
      )
    end

    before do
      ThreadContext.set(:current_user, admin)
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('table:add_edit_calculations', true)
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global('moving_calculation:enabled', true)
      FeatureToggle.toggle_global('percent_of_total:enabled', true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)
      FeatureToggle.toggle_global('table:remove_columns', true)
      FeatureToggle.toggle_global('table:show_friendly_aql_compile_error', true)
      FeatureToggle.toggle_global('table:single_row', true)

      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
    end

    it_behaves_like 'add and edit'

    it 'shows friendly error when remove dimension field and allow undoing changes' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      table_class = '[data-ci="ci-ag-grid-data-table"]'
      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load(table_class)

      table = page.find(table_class)
      add_pot(table: table, header_name: 'Sum of Quantity')
      wait_for_all_ajax_requests
      wait_for_element_load(table_class)

      remove_dimension_field(table: table, header_name: 'Month Created At')
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="ci-viz-aql-compile-error"]')
      expect(page).to have_css('[data-ci="ci-viz-aql-compile-error-accordion"]')

      check_error_details_expand_collapse

      safe_click('[data-ci="ci-viz-aql-compile-error"] [data-ci="ci-undo-changes"]')
      wait_for_element_load(table_class)
      expect(page).to have_css(table_class)
    end
  end
end
