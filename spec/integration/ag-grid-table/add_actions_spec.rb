# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'action fields in table', :js do
  let(:admin) { get_test_admin }

  def add_action(table:, header_name:, action_label:, has_action:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-manage-action-column')
    safe_click('[data-ci="add-action-btn"]') if has_action
    fill_in_monaco('.monaco-editor', '123')
    fill_text('[data-ci="manage-action-label"]', action_label)
    safe_click('[data-ci="manage-action-save"]')
    wait_for_all_ajax_requests
    wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
  end

  def edit_action(table:, header_name:, current_action_label:, action_label:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-manage-action-column')
    safe_click('[data-ci="action-item"]', exact_text: current_action_label)
    fill_text('[data-ci="manage-action-label"]', action_label)
    safe_click('[data-ci="manage-action-save"]')
    wait_for_all_ajax_requests
    wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
  end

  def delete_action(table:, header_name:, action_label:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-manage-action-column')
    safe_click('[data-ci="action-item"]', exact_text: action_label)
    safe_click('.hui-btn [data-icon="trash-alt"]')
    wait_for_all_ajax_requests
    wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
  end

  def test_action(table:, action_label:, header_name:)
    open_context_menu_by_name(element: table, header_name: header_name)
    safe_click('.ci-manage-action-column')

    expect(page.find('[data-ci="action-item"]').text).to eq action_label
  end

  def check_actions_indicator_position(table:, header_name:, justify_content:)
    header = find_header_element_by_name(element: table, header_name: header_name)

    expect(header.find('[data-ci="ci-header-column-label"]').style('justify-content')['justify-content']).to eq justify_content
  end

  context 'data table in exploration' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
            },
          ],
        },
        settings: {},
        filters: [],
      )
    end

    before do
      ThreadContext.set(:current_user, admin)
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('viz:action', true)
      FeatureToggle.toggle_global('table:manage_actions', true)
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global(DataSet::FT_CUSTOM_EXPRESSION, true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)

      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
      Capybara.current_window.resize_to 1600, 1000
    end

    it 'manage actions' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      table = page.find('[data-ci="ci-ag-grid-data-table"]')

      # add
      add_action(table: table, header_name: 'Sum of Quantity', action_label: 'Action 1', has_action: false)
      test_action(table: table, action_label: 'Action 1', header_name: 'Sum of Quantity')
      check_column_highlight(element: table, header_name: 'Sum of Quantity')

      # edit
      edit_action(table: table, header_name: 'Sum of Quantity', current_action_label: 'Action 1', action_label: 'Action 2')
      test_action(table: table, action_label: 'Action 2', header_name: 'Sum of Quantity')
      check_column_highlight(element: table, header_name: 'Sum of Quantity')

      # delete
      add_action(table: table, header_name: 'Sum of Quantity', action_label: 'Action 3', has_action: true)
      delete_action(table: table, header_name: 'Sum of Quantity', action_label: 'Action 2')
      test_action(table: table, action_label: 'Action 3', header_name: 'Sum of Quantity')
      check_column_highlight(element: table, header_name: 'Sum of Quantity')
    end


    context 'when field type icon is on' do
      before do
        FeatureToggle.toggle_global('table:show_data_type_icon_on_header', true)
      end

      it 'align actions indicator next to the label' do
        safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')

        add_action(table: table, header_name: 'Month Created At', action_label: 'Action 1', has_action: false)

        check_actions_indicator_position(table: table,header_name: 'Month Created At', justify_content: 'normal')
      end
    end
  end
end
