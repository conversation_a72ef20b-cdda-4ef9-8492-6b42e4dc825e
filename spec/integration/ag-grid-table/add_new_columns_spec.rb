# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'add new columns', js: true do
  let(:admin) { get_test_admin }

  CONTEXT_MENU_MAPPING = {
    'aql_metric_field' => [
      'Edit metric',
      'Sort',
      '', # divider
      'Insert column left',
      'Insert column right',
      'Add calculations',
      '', # divider
      'Add action',
      '', # divider
      'Freeze up to this column',
      'Auto-size',
      'Hide in view',
      'Rename',
      'Remove column',
    ],
    'advanced_field' => [
      'Edit calculations',
      'Sort',
      '', # divider
      'Insert column left',
      'Insert column right',
      '', # divider
      'Freeze up to this column',
      'Auto-size',
      'Hide in view',
      'Rename',
      'Remove column',
    ],
    'dimension_field' => [
      'Sort',
      'Change aggregation',
      '', # divider
      'Insert column left',
      'Insert column right',
      '', # divider
      'Add action',
      '', # divider
      'Freeze up to this column',
      'Auto-size',
      'Hide in view',
      'Rename',
      'Remove column',
    ],
    'aggregation_field' => [
      'Sort',
      'Change aggregation',
      '', # divider
      'Insert column left',
      'Insert column right',
      'Add calculations',
      '', # divider
      'Add action',
      '', # divider
      'Freeze up to this column',
      'Auto-size',
      'Hide in view',
      'Rename',
      'Remove column',
    ],
    'time_grain_field' => [
      'Sort',
      'Change aggregation & time-grain',
      '', # divider
      'Insert column left',
      'Insert column right',
      '', # divider
      'Add action',
      '', # divider
      'Freeze up to this column',
      'Auto-size',
      'Hide in view',
      'Rename',
      'Remove column',
    ],
  }.freeze

  def add_new_biz_calc
    safe_click('[col-id="add-new-column"]')
    # test auto-focus on add new column popover
    wait_expect(true) do
      page.has_css?('.hui-select-trigger.focused')
    end
    safe_click('[data-value="$add_business_calculation$"]')
    page.find('.ci-label-input').set('biz calc column')
    fill_in_monaco('.monaco-editor', '123')
    safe_click('[data-button-id="create_business_calculation"]')
  end

  def add_advanced_field
    safe_click('[col-id="add-new-column"]')
    # test auto-focus on add new column popover
    wait_expect(true) do
      page.has_css?('.hui-select-trigger.focused')
    end
    safe_click('[data-value="$add_advanced_calculation$"]')
    safe_click('[data-value="$add_percent_of_total$"]')
    safe_click('[data-ci="ci-advanced-calculation-form"] .ci-viz-field-select')
    safe_click('[data-value="$group_4$"]')
    safe_click('[data-value="data_modeling_orders$!id"]')
    safe_click('.hui-btn', exact_text: 'Save')
  end

  def add_new_dataset_field
    safe_click('[col-id="add-new-column"]')
    # test auto-focus on add new column popover
    wait_expect(true) do
      page.has_css?('.hui-select-trigger.focused')
    end
    safe_click('[data-value="$group_3$"]')
    safe_click('[data-value="data_modeling_orders$!id"]')
  end

  def add_new_dataset_field_by_context_menu(table:, header_name:, position:)
    open_context_menu_by_name(element: table, header_name: header_name)

    safe_click(".ci-add-column-#{position}")
    # test auto-focus on add new column popover
    wait_expect(true) do
      page.has_css?('.hui-select-trigger.focused')
    end

    safe_click('[data-value="$group_3$"]')
    safe_click('[data-value="data_modeling_orders$!id"]')
  end

  def add_new_aql_metric
    safe_click('[col-id="add-new-column"]')
    # test auto-focus on add new column popover
    wait_expect(true) do
      page.has_css?('.hui-select-trigger.focused')
    end
    safe_click('[data-value="$add_aql_metric$"]')
    fill_in_monaco('.monaco-editor', '123')
    safe_click('.hui-btn', exact_text: 'Create & Get Result')
  end

  def edit_aql_metric(table)
    open_context_menu_by_name(element: table, header_name: 'Untitled Metric')

    safe_click('.ci-edit-metric')

    fill_in_monaco('.monaco-editor', '1234')
    safe_click('.hui-btn', exact_text: 'Save & Get Result')
  end

  def check_field_icon(table:, header_name:, type:)
    header = find_header_element_by_name(element: table, header_name: header_name)
    expect(header.find('[data-ci="ci-field-icon"] [data-icon]')['data-icon']).to eq(type)
  end

  def check_context_menu_options(header, header_type)
    open_context_menu(header)
    options = page.all('.ci-table-header-dropdown .hui-popper-content > *').map(&:text)
    expect(options).to eq(CONTEXT_MENU_MAPPING[header_type])
  end

  context 'data table in exploration' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
            },
          ],
        },
        settings: {},
        filters: [],
      )
    end

    let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }

    before do
      ThreadContext.set(:current_user, admin)
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('table:freeze_columns', true)
      FeatureToggle.toggle_global('table:hide_fields', true)
      FeatureToggle.toggle_global('table:auto_size', true)
      FeatureToggle.toggle_global('table:remove_columns', true)
      FeatureToggle.toggle_global('table:rename_columns', true)
      FeatureToggle.toggle_global('table:aggregate_columns', true)
      FeatureToggle.toggle_global('table:add_edit_calculations', true)
      FeatureToggle.toggle_global('table:manage_actions', true)
      FeatureToggle.toggle_global('viz:action', true)
      FeatureToggle.toggle_global('table:add_new_columns', true)
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global('moving_calculation:enabled', true)
      FeatureToggle.toggle_global('percent_of_total:enabled', true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)
      FeatureToggle.toggle_global(DataSet::FT_CUSTOM_EXPRESSION, true)
      FeatureToggle.toggle_global('table:show_data_type_icon_on_header', true)
      FeatureToggle.toggle_global('table:single_row', true)
      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
      Capybara.current_window.resize_to 1600, 1000
    end

    it 'adds legacy biz calc successfully' do
      FeatureToggle.toggle_global(DataModel::FT_AQL_AS_DEFAULT_WITH_BIZ_CAL, true)
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      add_new_biz_calc

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('biz calc column') }

      check_field_icon(table: table, header_name: 'biz calc column', type: 'type/aggregated')
      check_column_highlight(element: table, header_name: 'biz calc column')
    end

    it 'adds and edits AQL metric successfully' do
      FeatureToggle.toggle_global('viz_setting:amql_new_edit_ui', true)

      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      add_new_aql_metric

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('Untitled Metric') }
      check_field_icon(table: table, header_name: 'Untitled Metric', type: 'type/metric-bordered-dash')
      check_column_highlight(element: table, header_name: 'Untitled Metric')

      edit_aql_metric(table)

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('Untitled Metric') }

      rows = table.find("[row-id='0']")
      expect(rows.text.include?('1234')).to be true
    end

    it 'checks AQL metric context menu options' do
      FeatureToggle.toggle_global('viz_setting:amql_new_edit_ui', true)

      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      add_new_aql_metric

      wait_for_all_ajax_requests

      header = find_header_element_by_name(element: table, header_name: 'Untitled Metric')
      check_context_menu_options(header, 'aql_metric_field')
    end

    it 'adds advanced fields successfully' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      add_advanced_field

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('Untitled PoT') }
      check_field_icon(table: table, header_name: 'Untitled PoT', type: 'type/percent')
      check_column_highlight(element: table, header_name: 'Untitled PoT')

      header = find_header_element_by_name(element: table, header_name: 'Untitled PoT')
      check_context_menu_options(header, 'advanced_field')
    end

    it 'adds dataset fields successfully' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      expect(page.find('[data-ci="ci-add-new-column-indicator"]')['class']).to include('cursor-pointer')
      add_new_dataset_field

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('Id') }

      header = find_header_element_by_name(element: table, header_name: 'Id')
      check_context_menu_options(header, 'dimension_field')
    end

    it 'adds dataset fields by context menu successfully' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      add_new_dataset_field_by_context_menu(table: table, header_name: 'Sum of Quantity', position: 'left')

      wait_for_all_ajax_requests
      wait_expect(true) { page.all('.ag-header-cell').map(&:text).include?('Id') }
      check_field_icon(table: table, header_name: 'Id', type: 'type/number')
      check_column_highlight(element: table, header_name: 'Id')

      header = find_header_element_by_name(element: table, header_name: 'Sum of Quantity')
      check_context_menu_options(header, 'aggregation_field')

      header = find_header_element_by_name(element: table, header_name: 'Month Created At')
      check_context_menu_options(header, 'time_grain_field')
    end

    context 'when resizing and adding new columns' do
      it 'does not auto-size the current columns when adding by context menu' do
        safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        resize_column_by_name(element: table, header_name: 'Month Created At', right: -100)
        resize_column_by_name(element: table, header_name: 'Sum of Quantity', right: 100)
        month_created_at_header_width = find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width
        sum_of_quantity_header_width = find_header_element_by_name(element: table, header_name: 'Sum of Quantity').native.size.width

        add_new_dataset_field

        wait_for_all_ajax_requests
        expect(find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width).to eq(month_created_at_header_width)
        expect(find_header_element_by_name(element: table, header_name: 'Sum of Quantity').native.size.width).to eq(sum_of_quantity_header_width)
        wait_expect(true) { find_header_element_by_name(element: table, header_name: 'Id').native.size.width != 100 }
      end

      it 'does not auto-size the current columns when adding by form' do
        safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        resize_column_by_name(element: table, header_name: 'Month Created At', right: -100)
        resize_column_by_name(element: table, header_name: 'Sum of Quantity', right: 100)
        month_created_at_header_width = find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width
        sum_of_quantity_header_width = find_header_element_by_name(element: table, header_name: 'Sum of Quantity').native.size.width

        safe_click('[data-value="data_modeling_orders-created_at"]')
        safe_click('[data-ci="ci-explorer-control-get-results"]')

        wait_for_all_ajax_requests
        expect(find_header_element_by_name(element: table, header_name: 'Month Created At').native.size.width).to eq(month_created_at_header_width)
        expect(find_header_element_by_name(element: table, header_name: 'Sum of Quantity').native.size.width).to eq(sum_of_quantity_header_width)
        expect(find_header_element_by_name(element: table, header_name: 'Created At').native.size.width).not_to eq(100)
      end
    end
  end
end
