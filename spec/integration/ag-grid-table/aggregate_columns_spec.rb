# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'aggregate on columns by context menu', :js do
  let(:admin) { get_test_admin }

  def test_aggregate_by_context_menu(table:, header_name:, aggregate:, expected_header_name:, expected_label:)
    header = find_header_element_by_name(element: table, header_name: header_name)
    open_context_menu(header)

    wait_expect(expected_label) { page.find('.ci-change-modifier-column').text }
    page.find('[data-icon="type/aggregated"]').hover
    page.find('.ci-modifier-option-column', exact_text: aggregate).click

    wait_expect(expected_header_name) { header.text }
    check_column_highlight(element: table, header_name: expected_header_name)
  end

  def test_empty_aggregation(table:, header_name:)
    open_context_menu_by_name(element: table, header_name: header_name)

    expect(page.has_no_css?('[data-icon="type/aggregated"]')).to be true
  end

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    context 'when data table' do
      before do
        FeatureToggle.toggle_global('ag-grid:data-table', true)
        FeatureToggle.toggle_global('pop:enabled', true)
        FeatureToggle.toggle_global('table:aggregate_columns', true)
        safe_login(admin, "/datasets/#{data_set.id}")
        Capybara.current_window.resize_to 1600, 1000
      end

      it 'works' do
        click_on_first_fields(6)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        safe_click('[data-ci="ci-viz-setting-tab-styles"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        test_aggregate_by_context_menu(
          table: table,
          header_name: 'Discount',
          aggregate: 'Sum',
          expected_header_name: 'Sum of Discount',
          expected_label: 'Change aggregation',
        )
        test_aggregate_by_context_menu(
          table: table,
          header_name: 'Created At',
          aggregate: 'Min',
          expected_header_name: 'Min of Created At',
          expected_label: 'Change aggregation & time-grain',
        )
      end

      it 'can not aggregate on pop columns' do
        create_pop_data_table_viz_setting
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        test_empty_aggregation(table: table, header_name: 'Sum of Discount (Prev. 1y)')
      end
    end

    context 'when pivot table' do
      before do
        FeatureToggle.toggle_global('ag-grid:pivot-table', true)
        FeatureToggle.toggle_global('pivot:transpose', true)
        FeatureToggle.toggle_global('pivot:aggregate_columns', true)
        safe_login(admin, "/datasets/#{data_set.id}")
        Capybara.current_window.resize_to 1600, 1000
        safe_click('.ci-viz-type-pivot_table')
      end

      shared_examples 'aggregate columns in pivot table' do
        it 'works' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')

          test_aggregate_by_context_menu(
            table: pivot,
            header_name: 'Month Created At',
            aggregate: 'by Week',
            expected_header_name: 'Week Created At',
            expected_label: 'Change time-grain',
          )
        end
      end

      describe 'metrics on row' do
        before do
          safe_login(admin, "/datasets/#{data_set.id}")
          safe_click('.ci-collapse-panel')
          page.find('.ci-viz-type-pivot_table').click
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'aggregate columns in pivot table'
      end
    end
  end
end
