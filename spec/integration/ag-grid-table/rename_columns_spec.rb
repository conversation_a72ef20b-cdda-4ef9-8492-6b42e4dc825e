# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'table interaction: rename columns', js: true do
  let(:admin) { get_test_admin }

  def double_click_on_carret(table:, header_name:)
    open_context_menu_by_name(element: table, header_name: header_name)

    expect(page.has_no_css?('.ci-title-input')).to eq true
  end

  def double_click_rename(table:, header_name:, new_label:)
    header = find_header_element_by_name(element: table, header_name: header_name)
    header.double_click

    wait_and_set('.ci-title-input', new_label)
    page.send_keys(:enter)
    wait_for_all_ajax_requests
  end

  def rename_column(table:, header_name:, new_label:)
    open_context_menu_by_name(element: table, header_name: header_name)

    wait_expect('Rename') { page.find('.ci-rename-column').text }
    safe_click('.ci-rename-column')
    fill_text('.h-input', new_label)
    safe_click('[data-hui-section="resolve-button"]')
    wait_for_all_ajax_requests
  end

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('pivot:transpose', true)
      FeatureToggle.toggle_global('table:rename_columns', true)
      FeatureToggle.toggle_global('pivot:rename_columns', true)
      FeatureToggle.toggle_global('data_models:table_custom_label', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
    end

    context 'data table' do
      it 'works with context menu' do
        click_on_first_fields(5)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        wait_expect('Discount') { page.all('.ag-header-cell')[2].text }

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        rename_column(table: table, header_name: 'Discount', new_label: 'New Discount')
        wait_expect('New Discount') { page.all('.ag-header-cell')[2].text }
        check_column_highlight(element: table, header_name: 'New Discount')
      end

      it 'works with double click' do
        click_on_first_fields(5)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        wait_expect('Discount') { page.all('.ag-header-cell')[2].text }

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        double_click_on_carret(table: table, header_name: 'Discount')
        double_click_rename(table: table, header_name: 'Discount', new_label: 'New Discount')
        wait_expect('New Discount') { page.all('.ag-header-cell')[2].text }
        check_column_highlight(element: table, header_name: 'New Discount')
      end
    end

    context 'pivot table' do
      before do
        page.find('.ci-viz-type-pivot_table').click
      end

      shared_examples 'rename in pivot table' do
        it 'works with context menu' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

          wait_expect('Quarter Created At') { page.all('.ag-header-cell')[1].text }

          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          rename_column(table: pivot, header_name: 'Quarter Created At', new_label: 'Custom Created At')
          wait_expect('Custom Created At') { page.all('.ag-header-cell')[1].text }
        end


        it 'works with double click' do
          page.find('.ci-viz-type-pivot_table').click
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

          wait_expect('Quarter Created At') { page.all('.ag-header-cell')[1].text }

          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          double_click_on_carret(table: pivot, header_name: 'Quarter Created At')
          double_click_rename(table: pivot, header_name: 'Quarter Created At', new_label: 'Custom Created At')
          wait_expect('Custom Created At') { page.all('.ag-header-cell')[1].text }
        end
      end

      describe 'metrics as row' do
        before do
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'rename in pivot table'
      end
    end
  end
end
