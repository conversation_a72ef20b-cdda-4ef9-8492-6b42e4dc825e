# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'style on pivot table in AG-Grid', js: true do
  let(:admin) { get_test_admin }
  let(:pivot) { page.find('[data-ci="ci-ag-grid-pivot-table"]') }

  def check_range_selection
    first_quarter_cell = find_cell_element(table_element: pivot, row_id: 0, col_index: 2)
    last_count_of_product_id_cell = find_cell_element(table_element: pivot, row_id: 'b-0', col_index: 5)
    first_quarter_cell.drag_to(last_count_of_product_id_cell)

    expect(first_quarter_cell[:class]).to include('h-range-selection-cell', 'h-background-color', 'h-left-border', 'h-top-border')
    expect(last_count_of_product_id_cell[:class]).to include('h-range-selection-cell h-background-color h-right-border')

    first_quarter_cell.right_click
    safe_click('.ci-copy-formatted-values')

    expect(page.all('[data-ci="ci-toasts-bottom-right"]').last.text).to eq 'Values copied'
  end

  def check_highlight(list_highlight:, element:, type:)
    list_highlight.each do |highlight|
      cell_element =
        type == 'header_group' ? find_header_group_element(element: element, row_index: highlight[:row_index], col_index: highlight[:col_index])
        : find_cell_element(table_element: element, row_id: highlight[:row_index], col_index: highlight[:col_index])

      if highlight[:mode] == 'active'
        expect(cell_element[:class].include?('h-inactive-element')).to be false
      else
        expect(cell_element[:class].include?('h-inactive-element')).to be true
      end
    end
  end

  before do
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global('pivot:transpose', true)
  end

  context 'when changing many formats' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
      safe_click('.ci-collapse-panel')
      page.all('.btn-toggle-icon').first.click
      page.find('.ci-viz-type-pivot_table').click
    end

    it 'renders row field format correctly' do
      create_pivot_viz_setting

      check_header_title_center
      check_range_selection
    end
  end

  describe 'metrics on row' do
    context 'when changing many formats' do
      include_context 'format is defined in data modeling'

      before do
        safe_login(admin, "/datasets/#{data_set.id}")
        Capybara.current_window.resize_to 1600, 1000
        safe_click('.ci-collapse-panel')
        page.find('.ci-viz-type-pivot_table').click
        safe_click('[data-ci="ci-metrics-as-row-field"]')
        safe_click('.ci-metrics-as-row-field-rows')
      end

      it 'renders row field format correctly' do
        create_pivot_viz_setting

        check_header_title_center(metrics_on_row: true)
        check_range_selection
      end
    end
  end

  context 'when cross-filtering' do
    include_context 'dashboards_v4'
    before do
      FeatureToggle.toggle_global('table:reorder_columns', true)
      FeatureToggle.toggle_global('table:add_new_columns', true)
      FeatureToggle.toggle_global('crossfilter:enabled', true)

      definition_aml = <<~STR
        Dashboard unnamed {
          title: 'Unnamed Dashboard'
          description: ''''''
          view: CanvasLayout {
            label: 'View 1'
            height: 800
            block v1 {
              position: pos(120, 120, 810, 330)
            }
          }
        }


      STR
      dashboard_table_with_coordinates.update!(definition_aml: definition_aml)
    end

    it 'highlights based on the highlighted ranges' do
      qlogin(admin, "/dashboards/v4/#{dashboard_table_with_coordinates.id}/edit")
      wait_for_element_load do
        page.find_by_id('block-v2')
      end

      cell = find_cell_element(table_element: pivot, row_id: 1, col_index: 4)
      cell.click

      wait_expect(true) do
        pivot.has_css?('.h-filterable-element')
      end

      # highlight header group
      check_highlight(
        list_highlight: [
          {row_index: 1, col_index: 3, mode: 'active'},
          {row_index: 2, col_index: 3, mode: 'active'},
          {row_index: 2, col_index: 6, mode: 'inactive'},
          {row_index: 3, col_index: 3, mode: 'inactive'},
          {row_index: 3, col_index: 4, mode: 'active'},
          {row_index: 3, col_index: 5, mode: 'inactive'},
        ],
        element: pivot,
        type: 'header_group'
      )

      # highlight cell
      check_highlight(
        list_highlight: [
          {row_index: 0, col_index: 1, mode: 'inactive'},
          {row_index: 0, col_index: 2, mode: 'inactive'},
          {row_index: 1, col_index: 1, mode: 'active'},
          {row_index: 1, col_index: 2, mode: 'active'},
          {row_index: 2, col_index: 1, mode: 'inactive'},
          {row_index: 2, col_index: 2, mode: 'inactive'},
          {row_index: 3, col_index: 1, mode: 'inactive'},
          {row_index: 3, col_index: 2, mode: 'inactive'},
        ],
        element: pivot,
        type: 'cell'
      )
    end
  end
end


def check_header_title_center(metrics_on_row: false)
  pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
  header_cell = find_header_elements(element: pivot)[1]
  header_style = header_cell.find('[data-ci="header-column-label-and-context-menu"]').style('justify-content')
  expect(header_style['justify-content']).to eq('flex-start')

  header_cell = find_header_elements(element: pivot)[0]
  header_style = header_cell.find('[data-ci="header-column-label-and-context-menu"]').style('justify-content')
  expect(header_style['justify-content']).to eq('flex-start')

  header_cell = find_header_elements(element: pivot)[metrics_on_row ? 5 : 4]
  header_style = header_cell.find('[data-ci="header-column-label-and-context-menu"]').style('justify-content')
  expect(header_style['justify-content']).to eq('center')

  unless metrics_on_row
    header_cell = find_header_elements(element: pivot)[4]
    header_style = header_cell.find('[data-ci="header-column-label-and-context-menu"]').style('justify-content')
    expect(header_style['justify-content']).to eq('center')
  end
end
