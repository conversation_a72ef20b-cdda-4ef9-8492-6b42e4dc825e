# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'hide fields', :js do
  let(:admin) { get_test_admin }

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    context 'data table' do
      before do
        FeatureToggle.toggle_global('ag-grid:data-table', true)
        FeatureToggle.toggle_global('pop:enabled', true)
        FeatureToggle.toggle_global('table:hide_fields', true)
        safe_login(admin, "/datasets/#{data_set.id}")
      end

      it 'hides fields by context menu' do
        click_on_first_fields(6)
        safe_click('[data-ci="ci-explorer-control-get-results"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        hide_field_by_context_menu(table: table, field_label: 'Created At')
        expect_no_toast
        sleep 0.5 # wait for table flash
        expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Created At')
      end

      it 'hides and show fields by viz field dropdown' do
        click_on_first_fields(6)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        hide_field_by_viz_field_dropdown(field_label: 'Created At')
        expect_no_toast
        sleep 0.5 # wait for table flash
        expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Created At')

        show_field_by_viz_field_dropdown(field_label: 'Created At')
        expect_no_toast
        sleep 0.5 # wait for table flash
        expect_header_names_and_hidden_indicator(is_hidden: false, field_label: 'Created At')
      end

      it 'shows banner when hiding all fields or changing viz type' do
        click_on_first_fields(3)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        hide_all_fields_by_dropdown(field_labels: ['Created At', 'Discount', 'Id'])
        expect_empty_banner
        expect_change_viz_type_and_show_banner

        remove_all_fields(total_fields: 1)
        expect_no_toast
      end

      it 'shows all fields when unhide all' do
        click_on_first_fields(3)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        hide_all_fields_by_dropdown(field_labels: ['Created At', 'Discount', 'Id'])
        expect_empty_banner

        page.find('[data-ci="ci-all-hidden-fields-banner"] button').click
        wait_expect(['', 'Created At', 'Discount', 'Id']) do
          page.all('.ag-header-cell').map(&:text)
        end
      end

      it 'shows banner when removing all hidden fields' do
        click_on_first_fields(3)
        safe_click('[data-ci="ci-explorer-control-get-results"]')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        hide_all_fields_by_dropdown(field_labels: ['Created At', 'Discount', 'Id'])

        remove_all_fields(total_fields: 3)
        # expect_empty_banner
      end

      it 'hides the pop columns' do
        safe_click('.ci-collapse-panel')
        create_pop_data_table_viz_setting
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        table = page.find('[data-ci="ci-ag-grid-data-table"]')

        expect_no_hide_option_for_pop_column(table: table)

        hide_measure_pop_field_by_context_menu(table: table)

        expect_pop_toast_text(/Period Comparison columns hidden/)
        expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Sum of Discount')

        safe_click('[data-hui-section="dismiss-button"]') if page.has_css?('[data-hui-section="dismiss-button"]')

        show_field_by_viz_field_dropdown(field_label: 'Sum of Discount')
        expect_pop_toast_text(/Unhiding a measure will also unhide its linked Period Comparison columns/)
      end

      it 'shows field by clicking on the hidden indicator' do
        click_on_first_fields(3)
        safe_click('[data-ci="ci-explorer-control-get-results"]')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        hide_field_by_context_menu(table: table, field_label: 'Created At')
        wait_expect(true) do
          expect(page.all('.ag-header-cell').map(&:text)).not_to include 'Created At'
        end

        created_at = page.find('.ci-field-info', exact_text: 'Created At')
        created_at.find('[data-icon="eye-light-slash"]').click
        expect(page.has_no_css?('.modifier-select-popover')).to be true
        wait_expect(true) do
          expect(page.all('.ag-header-cell').map(&:text)).to include 'Created At'
        end
        check_column_highlight(element: table, header_name: 'Created At')
      end
    end

    context 'pivot table' do
      before do
        FeatureToggle.toggle_global('ag-grid:pivot-table', true)
        FeatureToggle.toggle_global('pivot:transpose', true)
        FeatureToggle.toggle_global('pivot:hide_fields', true)
        FeatureToggle.toggle_global('pivot:post_update_when_paginating_v2', true)
        FeatureToggle.toggle_global('viz:pivot_v2', true)
        safe_login(admin, "/datasets/#{data_set.id}")
        Capybara.current_window.resize_to 1600, 1000
        safe_click('.ci-viz-type-pivot_table')
      end

      let!(:metrics_on_row) { false }

      shared_examples 'pivot show and hide fields correctly' do
        it 'hides fields by context menu' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          hide_field_by_context_menu(table: pivot, field_label: 'Year Created At')
          expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Year Created At')

          # still hide field after sorting
          sort_field_by_context_menu(table: pivot, field_label: 'Quarter Created At')
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Year Created At')
        end

        it 'hides and show fields by viz field dropdown' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

          hide_field_by_viz_field_dropdown(field_label: 'Year Created At')
          expect_no_toast
          sleep 0.5 # wait for table flash
          expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'Year Created At')

          show_field_by_viz_field_dropdown(field_label: 'Year Created At')
          expect_no_toast
          sleep 0.5 # wait for table flash
          expect_header_names_and_hidden_indicator(is_hidden: false, field_label: 'Year Created At')
        end

        context 'when hiding all row fields' do
          it 'does not show banner when pivot has another fields' do
            create_pivot_viz_setting
            wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

            hide_all_fields_by_dropdown(field_labels: ['Year Created At', 'Quarter Created At', 'Month Created At'])
            expect_no_hidden_banner
          end

          it 'does not show banner when removing all fields' do
            create_pivot_viz_setting
            wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

            hide_all_fields_by_dropdown(field_labels: ['Year Created At', 'Quarter Created At', 'Month Created At'])
            remove_all_fields(total_fields: 7)
            expect_no_hidden_banner
          end

          it 'shows banner and show all fields when unhide all' do
            create_pivot_viz_setting(has_column_fields: false, has_value_fields: false)
            wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

            hide_all_fields_by_dropdown(field_labels: ['Year Created At', 'Quarter Created At', 'Month Created At'])
            expect_empty_banner
            page.find('[data-ci="ci-all-hidden-fields-banner"] button').click
            wait_expect(['Year Created At', 'Quarter Created At', 'Month Created At']) do
              page.all('.ag-header-cell').map(&:text)
            end
          end
        end

        it 'spans and shows the total title cell correctly' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')

          hide_all_fields_by_dropdown(field_labels: ['Year Created At', 'Month Created At'])
          metrics_on_row ? expect_transposed_header_same_width_with_spanned_cell(element: pivot) : expect_header_same_width_with_spanned_cell(element: pivot)
        end

        it 'shows field by clicking on the hidden indicator' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          hide_field_by_context_menu(table: pivot, field_label: 'Year Created At')

          wait_expect(true) do
            expect(page.all('.ag-header-cell').map(&:text)).not_to include 'Year Created At'
          end

          created_at = page.find('.ci-field-info', exact_text: 'Year Created At')
          created_at.find('[data-icon="eye-light-slash"]').click
          expect(page.has_no_css?('.modifier-select-popover')).to be true
          wait_expect(true) do
            expect(page.all('.ag-header-cell').map(&:text)).to include 'Year Created At'
          end
        end
      end

      describe 'metrics on row' do
        before do
          safe_login(admin, "/datasets/#{data_set.id}")
          Capybara.current_window.resize_to 1600, 1000
          safe_click('.ci-viz-type-pivot_table')
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'pivot show and hide fields correctly' do
          let!(:metrics_on_row) { true }
        end

        it 'does not allow context menu on values columns and empty columns' do
          create_pivot_viz_setting
          wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
          pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
          values = find_header_element_by_name(element: pivot, header_name: 'Values')

          wait_expect(false) do
            values.hover
            values.has_css?('[data-ci="ci-angle-down"]')
          end

          empty_columns = pivot.all('.ag-header-cell', exact_text: '')

          wait_expect(false) do
            empty_columns.any? do |column|
              column.hover
              column.has_css?('[data-ci="ci-angle-down"]')
            end
          end
        end
      end
    end
  end

  context 'with PoT and moving calculation field (AQL dataset)' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: 'PoT',
              analytic: {
                of_all: 'grand_total',
                type: 'percentage',
              },
              format: {
                format: {
                  pattern: '#,###%',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: 'bc34a79b-eb67-43fb-8ead-8577bbffc864',
            },
          ],
        },
        settings: {},
        filters: [],
      )
    end

    before do
      ThreadContext.set(:current_user, admin)
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('table:hide_fields', true)
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)

      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
    end

    it 'hides and shows field by button in popover' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      # toggle hide field
      safe_click('.advanced-calculation-input')
      safe_click('[data-ci="ci-toggle-hide-advanced-calculation-field"]')

      expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'PoT', is_advanced_calculation: true)

      # toggle show field
      safe_click('[data-ci="ci-toggle-hide-advanced-calculation-field"]')

      expect_header_names_and_hidden_indicator(is_hidden: false, field_label: 'PoT', is_advanced_calculation: true)
    end

    it 'hides fields by context menu' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      hide_field_by_context_menu(table: table, field_label: 'PoT')
      expect_no_toast
      sleep 0.5 # wait for table flash
      expect_header_names_and_hidden_indicator(is_hidden: true, field_label: 'PoT', is_advanced_calculation: true)
    end

    it 'shows field by clicking on the hidden indicator' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      hide_field_by_context_menu(table: table, field_label: 'PoT')

      wait_expect(false) do
        page.all('.ag-header-cell').map(&:text).include?('PoT')
      end

      pot = page.find('.advanced-calculation-input', exact_text: 'PoT')
      pot.find('[data-icon="eye-light-slash"]').click
      expect(page.has_no_css?('[data-ci="ci-advanced-calculation-form"]')).to be true
      wait_expect(true) do
        page.all('.ag-header-cell').map(&:text).include?('PoT')
      end
    end

    it 'syncs the hidden state in popover with the viz field hidden indicator' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      # Hide by popover
      safe_click('.advanced-calculation-input')
      safe_click('[data-ci="ci-toggle-hide-advanced-calculation-field"]')

      expect_sync_hidden_state(is_hidden: true, data_icon: 'eye-light', text: 'Show Field')

      # show by indicator
      pot = page.find('.advanced-calculation-input', exact_text: 'PoT')
      pot.find('[data-icon="eye-light-slash"]').click

      expect_sync_hidden_state(is_hidden: false, data_icon: 'eye-light-slash', text: 'Hide Field')
    end
  end
end

def hide_field_by_context_menu(table:, field_label:)
  open_context_menu_by_name(element: table, header_name: field_label)
  safe_click('[data-icon="eye-light-slash"]')
  wait_for_all_ajax_requests
end

def sort_field_by_context_menu(table:, field_label:)
  open_context_menu_by_name(element: table, header_name: field_label)
  page.find('[data-icon="exchange-arrow"]').hover
  safe_click('[data-icon="arrow-up"]')
end

def hide_measure_pop_field_by_context_menu(table:)
  open_context_menu_by_name(element: table, header_name: 'Sum of Discount')
  safe_click('[data-icon="eye-light-slash"]')
end

def hide_field_by_viz_field_dropdown(field_label:)
  field = page.find('.ci-field-info', exact_text: field_label)

  field.click
  page.find('.hui-select-option', text: 'Hide Field').click
end

def show_field_by_viz_field_dropdown(field_label:)
  field = page.find('.ci-field-info', exact_text: field_label)

  field.click
  page.find('.hui-select-option', text: 'Show Field').click
end

def hide_all_fields_by_dropdown(field_labels:)
  field_labels.each { |field_label| hide_field_by_viz_field_dropdown(field_label: field_label) }
end

def show_field_by_viz_field_dropdown(field_label:)
  created_at = page.find('.ci-field-info', exact_text: field_label)

  created_at.click
  page.find('.hui-select-option', text: 'Show Field').click
end

def remove_all_fields(total_fields:)
  total_fields.times do
    safe_click('.ci-normal-field [data-icon="cancel"]')
  end
end

def expect_header_names_and_hidden_indicator(is_hidden:, field_label:, is_advanced_calculation: false)
  header_names = page.all('.ag-header-cell').map(&:text)

  expect(header_names.include?(field_label)).not_to be is_hidden

  created_at =
    if is_advanced_calculation
      page.find('.advanced-calculation-input', exact_text: field_label)
    else
      page.find('.ci-field-info', exact_text: field_label)
    end
  expect(created_at.all('[data-icon="eye-light-slash"]').empty?).not_to be is_hidden
end

def expect_no_toast
  expect(page.find('[data-ci="ci-toasts-top"]').has_no_css?('[data-ci="ci-toast"]')).to be true
end

def expect_pop_toast_text(text)
  expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text).to match(text)
end

def expect_empty_banner
  expect(page.find('[data-ci="ci-all-hidden-fields-banner"]').text).to eq "All columns are set to hidden. Unhide columns to display your data.\nUnhide all"
end

def expect_no_hidden_banner
  expect(page.all('[data-ci="ci-all-hidden-fields-banner"]').size).to eq 0
end

def expect_out_of_sync_banner
  wait_expect(true) do
    page.has_css?('[data-ci="out-of-sync-banner"]')
  end
end

def expect_no_hide_option_for_pop_column(table:)
  open_context_menu_by_name(element: table, header_name: 'Sum of Discount (Prev. 1y)')

  expect(page.all('[data-icon="eye-light-slash"]').size).to eq 0
end

def expect_sync_hidden_state(is_hidden:, data_icon:, text:)
  wait_expect(!is_hidden) do
    page.all('.ag-header-cell').map(&:text).include?('PoT')
  end
  wait_expect(data_icon) do
    page.find('[data-ci="ci-toggle-hide-advanced-calculation-field"] span')['data-icon']
  end
  wait_expect(text) do
    page.find('[data-ci="ci-toggle-hide-advanced-calculation-field"] div').text
  end
end

def expect_change_viz_type_and_show_banner
  safe_click('.ci-viz-type-line_chart')
  expect_out_of_sync_banner
  safe_click('.ci-viz-type-data_table')
  expect_out_of_sync_banner
end

def expect_transposed_header_same_width_with_spanned_cell(element:)
  wait_expect(true) do
    quarter_header = find_header_element_by_name(element: element, header_name: 'Quarter Created At')
    quarter_header_width = quarter_header.try(:[], :style)&.split(/; |;/)&.select do |style|
      style.include?('width')
    end&.[](0)&.match(/(\d+)/)&.[](1).to_i

    cell_total_title_element = page.first('.h-header-total-title-cell')
    cell_total_width = cell_total_title_element.try(:[], :style)&.split(/; |;/)&.select do |style|
      style.include?('width')
    end&.[](0)&.match(/(\d+)/)&.[](1).to_i

    quarter_header_width == cell_total_width
  end
end

def expect_header_same_width_with_spanned_cell(element:)
  wait_expect(true) do
    quarter_header = find_header_element_by_name(element: element, header_name: 'Quarter Created At')
    cell_total_title_element = page.find('.h-header-total-title-cell')

    header_width = quarter_header.try(:[], :style)&.split(/; |;/)&.select { |style| style.include?('width') }
    cell_total_width = cell_total_title_element.try(:[], :style)&.split(/; |;/)&.select do |style|
      style.include?('width')
    end

    !header_width.nil? && header_width == cell_total_width
  end
end
