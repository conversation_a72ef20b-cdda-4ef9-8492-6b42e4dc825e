# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'range selection in AG-Grid', js: true do
  let(:admin) { get_test_admin }

  context 'range selection in exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('pivot:transpose', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-collapse-panel')
      Capybara.current_window.resize_to 1600, 1000
      page.find('.ci-viz-type-pivot_table').click
    end

    context 'in pivot table' do
      let!(:metrics_on_row) { false }

      let(:pivot) { page.find('[data-ci="ci-ag-grid-pivot-table"]') }

      shared_examples 'range selection in pivot table' do
        it 'select and remove the select correctly' do
          create_pivot_viz_setting

          col_id = metrics_on_row ? 4 : 3
          header_cell = find_header_elements(element: pivot)[col_id]
          header_cell.click

          check_range_selection_style(row_id: 0, col_id: col_id, has_range_selection: true)

          # remove range selection
          safe_click('.__header.subheader')
          check_range_selection_style(row_id: 0, col_id: col_id, has_range_selection: false)
        end

        it 'selects the correct range of total column' do
          create_pivot_viz_setting

          col_id = metrics_on_row ? 6 : 7
          header_cell = find_header_elements(element: pivot)[col_id]
          header_cell.click

          check_range_selection_style(row_id: 0, col_id: col_id, has_range_selection: true)
        end
      end

      describe 'metrics on row' do
        before do
          safe_click('[data-ci="ci-metrics-as-row-field"]')
          safe_click('.ci-metrics-as-row-field-rows')
        end

        it_behaves_like 'range selection in pivot table' do
          let!(:metrics_on_row) { true }
        end
      end
    end
  end

  context 'when re-ordering columns' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('table:reorder_columns', true)
      FeatureToggle.toggle_global('pop:enabled', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
      safe_click('.ci-collapse-panel')
    end

    it 'hightlight and copy-paste the range correctly' do
      create_data_table_viz_setting
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      reorder_columns(table)
      check_viz_setting_order(['Id', 'Created At', 'Is Deleted', 'Product id', 'Discount'])
      select_range(table)
      check_table_range_selection(table)
    end

    it 'check the order in POP case' do
      create_pop_data_table_viz_setting
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      reorder_and_check_pop_columns(table)
      remove_and_add_new_column(columns_to_remove: ['Id', 'Is Deleted', 'Sum of Product id'], columns_to_add: ['Quantity'])
      reorder_pop_columns(table)
    end

    it 'drag and drop column outside the table' do
      create_data_table_viz_setting
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      reorder_columns_outside_the_table(table)
      check_viz_setting_order(
        [
          'Discount',
          'Id',
          'Created At',
          'Is Deleted',
          'Product id',
        ]
      )
    end
  end

  context 'when opening context menu' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('table:freeze_columns', true)
      safe_login(admin, "/datasets/#{data_set.id}")
      Capybara.current_window.resize_to 1600, 1000
      safe_click('.ci-collapse-panel')
    end

    it 'highlight the column' do
      click_on_first_fields(6)
      safe_click('[data-ci="ci-explorer-control-get-results"]')

      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      created_at_column = find_header_element_by_name(element: table, header_name: 'Created At')
      created_at_column.click

      cell = find_cell_element(table_element: table, row_id: '0', col_index: 2)
      expect(cell[:class].include?('h-range-selection-cell')).to be true
    end
  end
end

def check_range_selection_style(row_id:, col_id:, has_range_selection:)
  pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
  cell = find_cell_element(table_element: pivot, row_id: row_id, col_index: col_id + 1)

  wait_expect(has_range_selection) { cell[:class].include?('h-left-border') }
  wait_expect(has_range_selection) { cell[:class].include?('h-right-border') }
  wait_expect(has_range_selection) { cell[:class].include?('h-top-border') }
  wait_expect(has_range_selection) { cell[:class].include?('h-background-color') }
end

def reorder_columns(table)
  id_column = find_header_element_by_name(element: table, header_name: 'Id')
  created_at_column = find_header_element_by_name(element: table, header_name: 'Created At')

  id_column.drag_to created_at_column
  wait_for_all_ajax_requests

  discount_column = find_header_element_by_name(element: table, header_name: 'Discount')
  product_id = find_header_element_by_name(element: table, header_name: 'Product id')

  wait_for_all_ajax_requests
  discount_column.drag_to product_id
end

def reorder_columns_outside_the_table(table)
  created_at_column = find_header_element_by_name(element: table, header_name: 'Created At')

  drag_to_coordinates(created_at_column, right: 180, bottom: 200)
end

def reorder_and_check_pop_columns(table)
  sum_of_discount_column = find_header_element_by_name(element: table, header_name: 'Sum of Discount')
  year_created_at_column = find_header_element_by_name(element: table, header_name: 'Year Created At')

  sum_of_discount_column.drag_to year_created_at_column
  expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text).to match(/Restriction on re-ordering Period Comparison columns/)
  expect(page.all('.ci-viz-field .field-label').map(&:text)).to eq [
    'Sum of Discount',
    'Year Created At',
    'Id',
    'Is Deleted',
    'Sum of Product id',
  ]

  safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
  pop_discount_column = find_header_element_by_name(element: table, header_name: 'Sum of Discount (Prev. 1y)')

  year_created_at_column.drag_to pop_discount_column
  expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text).to match(/Restriction on re-ordering Period Comparison columns/)
  safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
  expect(page.all('.ci-viz-field .field-label').map(&:text)).to eq [
    'Sum of Discount',
    'Year Created At',
    'Id',
    'Is Deleted',
    'Sum of Product id',
  ]
end

def remove_and_add_new_column(columns_to_remove:, columns_to_add:)
  columns_to_remove.each do |column|
    page.find('.field-info-popover', text: column).find('[data-icon="cancel"]').click
  end

  columns_to_add.each do |column|
    page.find('[data-ci="ci-data-model-field"]', text: column).click
  end
  safe_click('[data-ci="ci-explorer-control-get-results"]')
  if page.has_css?('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
    safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
  end
  wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
end

def reorder_pop_columns(table)
  quantity_column = find_header_element_by_name(element: table, header_name: 'Quantity')
  sum_of_discount_column = find_header_element_by_name(element: table, header_name: 'Sum of Discount (Prev. 1y)')

  quantity_column.drag_to sum_of_discount_column
  expect(page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text).to match(/Restriction on re-ordering Period Comparison columns/)
  expect(page.all('.ci-viz-field .field-label').map(&:text)).to eq [
    'Sum of Discount',
    'Year Created At',
    'Quantity',
  ]
end

def check_viz_setting_order(expected_order)
  sleep 1
  expect(page.all('.field-label').map(&:text)).to eq expected_order
end

def select_range(table)
  start_cell = find_cell_element(table_element: table, row_id: 0, col_index: 4)
  end_cell = find_cell_element(table_element: table, row_id: 3, col_index: 2)

  start_cell.drag_to end_cell
end

def check_table_range_selection(table)
  row_number_cell = find_cell_element(table_element: table, row_id: 0, col_index: 1)
  expect(row_number_cell[:class].include?('h-row-number-cell')).to be true
  expect(row_number_cell[:class].include?('h-left-border')).to be false

  start_cell = find_cell_element(table_element: table, row_id: 0, col_index: 4)
  expect(start_cell[:class].include?('h-background-color')).to be true
  expect(start_cell[:class].include?('h-left-border')).to be false
  expect(start_cell[:class].include?('h-top-border')).to be true
  expect(start_cell[:class].include?('h-right-border')).to be true

  middle_cell = find_cell_element(table_element: table, row_id: 2, col_index: 3)
  expect(middle_cell[:class].include?('h-left-border')).to be false
  expect(middle_cell[:class].include?('h-top-border')).to be false
  expect(middle_cell[:class].include?('h-background-color')).to be true

  end_cell = find_cell_element(table_element: table, row_id: 3, col_index: 2)
  expect(end_cell[:class].include?('h-right-border')).to be false
  expect(end_cell[:class].include?('h-left-border')).to be true
  expect(end_cell[:class].include?('h-top-border')).to be false
  expect(end_cell[:class].include?('h-background-color')).to be true
end
