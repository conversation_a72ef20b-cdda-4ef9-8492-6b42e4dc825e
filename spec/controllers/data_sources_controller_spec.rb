# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe DataSourcesController do
  let(:admin) { users(:admin) }
  let(:current_user) { admin }
  before do
    sign_in current_user
    FeatureToggle.toggle_global('data_source:enable_schema_info', true)
  end
  let(:cache_time) { 60 * 60 * 12 + 1 } # 12 hours + 1 secs

  describe '#show' do
    let!(:ds) { FactoryBot.create(:data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json) }

    context 'user is admin' do
      let(:current_user) { admin }
      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
      end

      it 'admin can read the  data source' do
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(200)
      end

      it 'should always omit the password' do
        ds.dbconfig['password'] = ds.dbconfig[:password]
        ds.save!
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(200)
        result = JSON.parse(response.body)
        expect(result['dbconfig']['password'].present?).to be_falsy
      end
    end

    context 'admin share business user read datasource permission' do
      let(:business_user) { FactoryBot.create(:user, name: 'user', role: 'user') }
      let(:current_user) { business_user }

      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
      end

      it 'business_user cannot read the data source' do
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(403)
        parsed = JSON.parse(response.body)
        expect(parsed['errors'].size).to eq 1
        expect(parsed['errors'].first).to match(/do not have permission/)
      end
    end

    context 'admin share analyst user read datasource permission' do
      let(:analyst) { FactoryBot.create(:user, name: 'analyst', role: 'analyst') }
      let(:current_user) { analyst }

      before do
        admin.share(analyst, :read, ds)
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
      end

      it 'user can read data_source' do
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(200)
      end
    end

    context 'admin share group of analyst and business user read data_source permission' do
      let(:test_tenant) { get_holistics_tenant }
      let!(:ds) do
        FactoryBot.create(:data_source,
                           dbtype: 'postgresql',
                           dbconfig: dbconfig_rails_test_env.to_json,
                           tenant_id: test_tenant.id,)
      end
      let(:admin) { FactoryBot.create(:user, name: 'admin', role: 'admin', tenant_id: test_tenant.id) }
      let(:analyst) { FactoryBot.create(:user, name: 'analyst', role: 'analyst', tenant_id: test_tenant.id) }
      let(:business_user) { FactoryBot.create(:user, name: 'user', role: 'user', tenant_id: test_tenant.id) }
      let(:test_group) { FactoryBot.create(:group, name: 'test group', tenant_id: test_tenant.id) }
      let!(:group_membership1) { FactoryBot.create(:group_membership, user: analyst, group: test_group, tenant_id: test_group.tenant_id) }
      let!(:group_membership2) { FactoryBot.create(:group_membership, user: business_user, group: test_group, tenant_id: test_group.tenant_id) }

      before do
        admin.share(test_group, :read, ds)
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
      end

      it 'analyst can read the data_source' do
        sign_in analyst
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(200)
      end

      it 'business user is not allowed to read the data_source' do
        sign_in business_user
        get :show, format: :json, params: { id: ds.id }
        expect(response.status).to eq(403)
        parsed = JSON.parse(response.body)
        expect(parsed['errors'].size).to eq 1
        expect(parsed['errors'].first).to match(/do not have permission/)
      end
    end

    context 'with big query json credentials' do
      let(:bq_ds) do
        create(:data_source, dbtype: 'bigquery', name: 'bigquery_test', dbconfig: dbconfig_bigquery_test_env.to_json)
      end

      context 'when turning on FT' do
        before do
          FeatureToggle.toggle_global(::DataSource::FT_HIDE_JSON_CREDENTIALS, true)
        end

        it 'should hide the json credentials' do
          get :show, format: :json, params: { id: bq_ds.id }
          expect(response.status).to eq(200)
          result = JSON.parse(response.body)
          expect(result['dbconfig']['cred_json']).not_to be_present
        end
      end

      context 'when turning off FT' do
        before do
          FeatureToggle.toggle_global(::DataSource::FT_HIDE_JSON_CREDENTIALS, false)
        end

        it 'should show the json credentials' do
          get :show, format: :json, params: { id: bq_ds.id }
          expect(response.status).to eq(200)
          result = JSON.parse(response.body)
          expect(result['dbconfig']['cred_json']).to be_present
        end
      end
    end
  end

  describe '#integration_connection_list' do
    let!(:sftp_source) { FactoryBot.create :test_ds_sftp }
    let!(:sftp_source2) { FactoryBot.create :test_ds_sftp, name: 'sftp2' }
    let!(:sftp_dest) {
      FactoryBot.create :sftp_dest, data_source_id: sftp_source.id, tenant_id: sftp_source.tenant_id, format: 'csv', file_path: 'asd'
    }
    let!(:data_schedule) {
      FactoryBot.create :email_schedule, source: query_report, dest: sftp_dest, tenant_id: sftp_source.tenant_id
    }
    let!(:query_report) { FactoryBot.create :query_report }
    it 'lists out SFTP sources and their usages' do
      get :integration_connection_list, format: :json, params: { type: 'sftp' }
      assert_success_response!
      res = JSON.parse(response.body)
      expect(res.size).to eq(2)
      expect(res.map { |ds| [ds['id'], ds['name'], ds['usage_count']] }).to match_array(
        [
          [sftp_source.id, sftp_source.name, 1],
          [sftp_source2.id, sftp_source2.name, 0],
        ],
      )
    end
  end

  describe '#tables' do
    it 'returns a list of tables for specified data sources' do
      ds = FactoryBot.create :data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json

      get :tables, format: :json, params: { id: ds.id }
      expect(response.status).to eq(200)
      parsed = JSON.parse(response.body)
      tables = parsed['tables']
      expect(tables).to be_present
      expect(tables.size).to be > 0

      # Expect the table list are sorted
      # TODO: This test is green on local, but failed on CircleCI. Neet to double check
      # sorted = tables.each_cons(2).all?{ |left, right| left <= right }
      # expect(sorted).to be true
    end

    context 'when current user is a business user' do
      let(:current_user) { users(:bizuser) }

      it 'should not returns a list of tables for specified data sources' do
        ds = FactoryBot.create :data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json

        get :tables, format: :json, params: { id: ds.id }
        expect(response).to have_http_status :forbidden
      end
    end
  end

  describe '#columns' do
    it 'returns list of columns for specified table' do
      ds = FactoryBot.create :data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json

      get :columns, params: { id: ds.id, full_table_name: 'public.data_sources' }, format: :json
      expect(response.status).to eq(200)
      parsed = JSON.parse(response.body)
      expect(parsed['column_names']).to be_present
      expect(parsed['column_names'].size).to be > 0
    end
  end

  describe '#all_columns' do
    let(:test_table) do
      FQName.parse('public.test_cache_table')
    end
    let(:test_column) do
      'test_cache_column'
    end
    let(:ds) do
      FactoryBot.create :data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json
    end
    let(:connector) do
      Connectors.from_ds(ds)
    end

    def expect_column_exists(table_fqname, column_name, present = true)
      get :all_columns, params: { id: ds.id }, format: :json
      columns = JSON.parse(response.body).rsk
      table_new = columns.find { |c| c[:table_fqname] == table_fqname.to_unquoted_s && c[:column_name] == column_name }
      expect(table_new).to (present ? be_present : be_nil)
    end

    it 'returns all columns and cache the results' do
      ds_drop_table(connector: connector, fqname: test_table)
      Cache.clear(ds.cacher.all_columns_cache_key)

      # get columns => cache created
      now = Time.now
      Timecop.freeze(now) do
        get :all_columns, params: { id: ds.id }, format: :json
        columns = JSON.parse(response.body).rsk
        cache_data = Cache.fetch(ds.cacher.all_columns_cache_key).rsk

        expect(cache_data[:last_retrieved]).to eq now.to_s
        expect(cache_data[:body]).to eq columns
      end

      columns = [
        { column_name: 'id', data_type: 'integer' },
        { column_name: test_column, data_type: 'varchar(10)' }
      ]
      ds_create_table(connector: connector, fqname: test_table, columns: columns)

      # get columns again
      expect_column_exists(test_table, test_column, false)
      old_cache_data = Cache.fetch(ds.cacher.all_columns_cache_key).rsk

      # 30 minutes later, get columns again
      Timecop.freeze(now + cache_time) do
        # return old result and execute job to fetch new result
        expect_column_exists(test_table, test_column, false)
        # a moment later, get again => result updated
        sleep 1
        expect_column_exists(test_table, test_column, true)
      end

      # test with no_cache parameter
      Cache.set(ds.cacher.all_columns_cache_key, 0, old_cache_data)
      get :all_columns, params: { id: ds.id, no_cache: true }, format: :json
      columns = JSON.parse(response.body).rsk
      table_new = columns.find { |c| c[:table_fqname] == test_table.to_unquoted_s && c[:column_name] == test_column }
      expect(table_new).to be_present

      # drop test table
      ds_drop_table(connector: connector, fqname: test_table)
    end
  end

  describe '#reports' do
    let(:test_ds) { get_test_ds }

    it 'render JSON list of associated reports' do
      r1 = FactoryBot.create :query_report, data_source: test_ds
      r2 = FactoryBot.create :query_report, data_source: test_ds

      get :reports, params: { id: test_ds.id }

      is_expected.to respond_with 200
      parsed = JSON.parse(response.body)
      expect(parsed.size).to eq 2

      ids = parsed.map { |e| e['id'] }
      expect(ids).to match_array [r1.id, r2.id]
    end
  end

  describe '#data_models' do
    let(:test_ds) { get_test_ds }
    let(:test_tenant) { get_test_tenant }
    let(:admin) { FactoryBot.create(:user, name: 'admin', role: 'admin', tenant_id: test_tenant.id) }
    let(:analyst) { FactoryBot.create(:user, name: 'analyst', role: 'analyst', tenant_id: test_tenant.id) }
    let(:business_user) { FactoryBot.create(:user, name: 'user', role: 'user', tenant_id: test_tenant.id) }
    let!(:model1) { FactoryBot.create :data_model, backend: FactoryBot.create(:table_model), data_source: test_ds }
    let!(:model2) { FactoryBot.create :data_model, backend: FactoryBot.create(:table_model), data_source: test_ds }

    it 'render JSON list of associated reports' do
      get :data_models, params: { id: test_ds.id }

      is_expected.to respond_with 200
      parsed = JSON.parse(response.body)

      ids = parsed.map { |e| e['id'] }
      expect(ids).to match_array [model1.id, model2.id]
    end

    it 'analyst can only read data models if data source is shared' do
      admin.share(analyst, :read, test_ds)
      sign_in analyst

      get :data_models, params: { id: test_ds.id }

      is_expected.to respond_with 200
      parsed = JSON.parse(response.body)

      ids = parsed.map { |e| e['id'] }
      expect(ids).to match_array [model1.id, model2.id]
    end

    it 'analyst cannot see the data_models list if data source is not shared' do
      sign_in analyst
      get :data_models, params: { id: test_ds.id }
      expect(response.status).to eq(403)
    end

    it 'business user cannot see the data_models list' do
      sign_in business_user
      get :data_models, params: { id: test_ds.id }
      expect(response.status).to eq(403)
    end
  end

  describe '.set_defaults' do
    it 'works' do
      put :set_default, params: { ds_id: get_test_ds.id }

      admin = get_test_admin
      expect(admin.tenant.settings[:default_ds_id]).to eq get_test_ds.id
    end
  end

  describe '#status' do
    it 'returns true when ds connection is good' do
      ds = get_test_ds
      get :status, params: { id: ds.id }
      expect(JSON.parse(response.body)['tested']).to eq true
    end

    it 'returns false when connection is not good' do
      dbconfig = get_test_ds.dbconfig.merge(user: 'wrong')
      ds = FactoryBot.create :data_source, dbtype: 'postgresql', dbconfig: dbconfig

      get :status, params: { id: ds.id }
      expect(JSON.parse(response.body)['tested']).to eq false
    end
  end

  describe '#index' do
    it 'returns data sources only for reporting purpose' do
      ds2 = mongo_testdb_ds
      get :index, params: { isSql: true }, format: :json

      ls = JSON.parse(response.body)
      expect(ls.map { |ds| ds['dbtype'] }).to_not include('mongodb')
    end

    it 'only returns non-deleted data source' do
      deleted_ds = FactoryBot.create :data_source, name: 'delete', dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json

      deleted_ds.soft_delete!
      get :index, format: :json, params: { isSql: true }
      results = JSON.parse(response.body)
      expect(results.size).to eq(2)
    end
  end

  describe 'async schemas' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    let(:connector) { Connectors.from_ds(ds) }
    let (:cached_data) do
      { last_retrieved: (Time.now + cache_time).to_s,
        body: ['public'] }
    end

    before do
      ds.cacher.clear_cache(:schema_names)
    end

    after do
      ds.cacher.clear_cache(:schema_names)
    end

    it 'can submit job to load schemas' do
      post :submit_schemas, params: { id: ds.id }
      job_info = JSON.parse(response.body)

      expect(job_info['status']).to eq('created')
      job = Job.find_by(id: job_info['job_id'])
      expect(job.status).to eq('success')

      get :get_schemas_results, params: { job_id: job.id }
      schemas = JSON.parse(response.body)['schemas']
      expect(schemas).to include('public')
    end

    it "can't get schemas result of another tenant" do
      o_tenant = create(:tenant)
      o_ds = create(:data_source, tenant_id: o_tenant.id)
      o_admin = create(:user, tenant_id: o_tenant.id, role: 'admin')
      sign_in o_admin

      post :submit_schemas, params: { id: o_ds.id }
      job_info = JSON.parse(response.body)

      expect(job_info['status']).to eq('created')
      job = Job.find_by(id: job_info['job_id'])
      expect(job.status).to eq('success')

      @controller = described_class.new

      sign_in admin

      get :get_schemas_results, params: { job_id: job.id }
      expect(response).to have_http_status(:forbidden)

      @controller = nil
    end

    it 'fetch cache for table names' do
      Cache.set(ds.cacher.cache_key_for(:schema_names), 43_200, cached_data)
      post :submit_schemas, params: { id: ds.id }
      result = JSON.parse(response.body)
      expect(result['status']).to eql('cached')
      expect(result['schemas']).to include('public')
    end
  end

  describe 'default schema' do
    let(:ds) do
      create :data_source,
             dbtype: 'postgresql',
             dbconfig: dbconfig_rails_test_env.to_json
    end

    it 'can set default schema' do
      put :set_default_schema, params: { id: ds.id, default_schema: 'public' }
      body = JSON.parse(response.body)
      expect(body['status']).to eq('OK')
      expect(DataSource.find(ds.id).settings[:default_schema]).to eq('public')
    end

    it 'can get default schema' do
      put :set_default_schema, params: { id: ds.id, default_schema: 'public' }
      get :default_schema, params: { id: ds.id }
      body = JSON.parse(response.body)
      expect(body['default_schema']).to eq('public')
    end
  end

  describe 'async tables' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    let (:cntor) { Connectors.from_ds(ds) }
    let (:cached_data) do
      { last_retrieved: (Time.now + cache_time).to_s,
        body: ['public.a_table'] }
    end

    before do
      ds.cacher.clear_cache(:table_names)
    end

    after do
      ds.cacher.clear_cache(:table_names)
    end

    it 'can submit job to load table names' do
      post :submit_tables, params: { id: ds.id }
      job_info = JSON.parse(response.body)

      # check job status
      expect(job_info['status']).to eq('created')
      job = Job.find_by(id: job_info['job_id'])
      expect(job.status).to eq('success')

      # check job result
      get :get_tables_results, params: { job_id: job.id }
      tables = JSON.parse(response.body)['tables']
      expect(tables.include?('public.users')).to be true
    end

    it 'fetch cache for table names' do
      Cache.set(ds.cacher.cache_key_for(:table_names), 43_200, cached_data)
      post :submit_tables, params: { id: ds.id }
      result = JSON.parse(response.body)
      expect(result['status']).to eql('cached')
      expect(result['tables']).to include('public.a_table')
    end
  end

  describe 'async columns' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end

    it 'can submit job to load columns' do
      Cache.clear(ds.cacher.all_columns_cache_key)
      post :submit_columns, params: { id: ds.id }
      job_info = JSON.parse(response.body)

      expect(job_info['status']).to eq('created')
      job = Job.find_by(id: job_info['job_id'])
      expect(job.status).to eq('success')

      get :get_columns_results, params: { job_id: job.id }
      columns = JSON.parse(response.body)['columns']
      expect(columns.any? do |col|
               col['table_fqname'] == 'public.users' &&
                           col['column_name'] == 'tenant_id'
             end).to be true
    end

    it "can't get columns result of another tenant" do
      o_tenant = create(:tenant)
      o_ds = create(:data_source, tenant_id: o_tenant.id)
      o_admin = create(:user, tenant_id: o_tenant.id, role: 'admin')
      sign_in o_admin

      Cache.clear(ds.cacher.all_columns_cache_key)
      post :submit_columns, params: { id: o_ds.id }
      job_info = JSON.parse(response.body)

      expect(job_info['status']).to eq('created')
      job = Job.find_by(id: job_info['job_id'])
      expect(job.status).to eq('success')

      @controller = described_class.new

      sign_in admin

      get :get_columns_results, params: { job_id: job.id }
      expect(response).to have_http_status(:forbidden)

      @controller = nil
    end
  end

  describe 'reverse tunnel ds endpoints' do
    let(:tunnel_params) do
      {
        host: 'localhost',
        port: '3000'
      }
    end
    describe '#submit_reverse_tunnel_ds' do
      let(:ds_params) do
        {
          dbtype: 'postgresql',
          dbconfig: {
            host: 'localhost',
            port: '3000',
            dbname: 'db',
            user: 'user',
            password: ''
          }
        }
      end

      before do
        FeatureToggle.toggle_global('data_sources:automated_reverse_tunnel', true)
      end

      it 'creates a new reverse tunnel job and returns the command to execute installation script' do
        post :submit_reverse_tunnel_ds, params: { tunnel_params: tunnel_params }

        json = JSON.parse(response.body)
        rt_job = ReverseTunnelJob.find(json['reverse_tunnel_job_id'])
        data_ds_params = rt_job.ds_params
        expect(data_ds_params.rsk[:port]).to eq ds_params.rsk[:port]
        expect(!rt_job.tunnel_params[:tunnel_token].empty?).to be true
        expect(rt_job.status).to eq 'created'
      end
    end

    describe '#create' do
      let(:ds_params) do
        {
          name: 'test_rt_db', dbtype: 'postgresql',
          dbconfig: dbconfig_rails_test_env.merge({ host: 'localhost' }),
        }
      end

      let(:ds_with_strange_url_params) do
        {
          name: 'test_rt_db', dbtype: 'postgresql',
          dbconfig: dbconfig_rails_test_env.merge({ host: 'file://danielmalaton' }),
        }
      end

      before do
        Rails.env.stub(production?: true)
      end

      it 'can not create a data source with localhost in production env' do
        post :create, params: { ds: ds_params }

        expect(response.body).to match(/You are not allowed.*Holistics' local hosts.*tunnel connection/)
        expect(response.status).to eql 422
      end

      # it 'can not create a data source with strange url in production env' do
      #   post :create, params: { ds: ds_with_strange_url_params }

      #   expect(response.body).to match(/Invalid URI scheme/)
      #   expect(response.status).to eql 422
      # end
    end

    describe '#create_reverse_tunnel_ds' do
      let(:ds_params) do
        {
          name: 'test_rt_db', dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env
        }
      end

      before do
        @initial_ds_count = DataSource.count

        client_public_key = FactoryBot.create :client_public_key
        FactoryBot.create :client_public_key_usage, client_public_key_id: client_public_key.id, tunnel_port: 5432, tenant_id: client_public_key.tenant_id
        FeatureToggle.toggle_global('data_sources:automated_reverse_tunnel', true)

        post :submit_reverse_tunnel_ds, params: { tunnel_params: tunnel_params }
      end

      it 'creates new data source, updates job and updates usage' do
        rt_job = ReverseTunnelJob.last
        rt_job.update_status 'tunnel_running'
        ReverseTunnelDsService.save_tunnel_port_to_rt_job(rt_job, 5432)

        post :create_reverse_tunnel_ds, params: { ds: ds_params, reverse_tunnel_job_id: rt_job.id }
        expect(response.status).to eq 200
        expect(DataSource.count).to eq(@initial_ds_count + 1)
        expect(ClientPublicKeyUsage.last.data_source_id).to eq DataSource.last.id
        expect(ClientPublicKeyUsage.last.client_port).to_not eq 5432
        expect(rt_job.reload.status).to eq 'success'
      end

      it 'rejects if the tunnel port is not available' do
        rt_job = ReverseTunnelJob.last
        rt_job.update_status 'tunnel_running'

        ReverseTunnelDsService.save_tunnel_port_to_rt_job(rt_job, 9999)
        post :create_reverse_tunnel_ds, params: { ds: ds_params, reverse_tunnel_job_id: rt_job.id }
        expect(DataSource.count).to eq(@initial_ds_count)

        ReverseTunnelDsService.save_tunnel_port_to_rt_job(rt_job, 5432)
        ClientPublicKeyUsage.last.update(data_source_id: 1)
        post :create_reverse_tunnel_ds, params: { ds: ds_params, reverse_tunnel_job_id: rt_job.id }
        expect(DataSource.count).to eq(@initial_ds_count)
        expect(rt_job.reload.status).to eq 'failure'
      end

      it 'rejects if tunnel job belongs to another tenant' do
        rt_job = ReverseTunnelJob.last
        rt_job.update_status 'tunnel_running'
        another_tenant = FactoryBot.create :tenant
        rt_job.update(tenant_id: another_tenant.id)

        ReverseTunnelDsService.save_tunnel_port_to_rt_job(rt_job, 9999)
        post :create_reverse_tunnel_ds, params: { ds: ds_params, reverse_tunnel_job_id: rt_job.id }
        expect(response.status).to eq 403
      end
    end

    describe '#update_reverse_tunnel_ds' do
      let(:ds_params) do
        {
          name: 'test_rt_db', dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env
        }
      end
      let(:ds) do
        FactoryBot.create :data_source
      end

      before do
        ds
        @initial_ds_count = DataSource.count

        client_public_key = FactoryBot.create :client_public_key
        old_usage = FactoryBot.create :client_public_key_usage, client_public_key_id: client_public_key.id, tunnel_port: 5432, data_source_id: ds.id, tenant_id: client_public_key.tenant_id
        @client_public_key_usage = FactoryBot.create :client_public_key_usage, client_public_key_id: client_public_key.id, tunnel_port: 5433, tenant_id: client_public_key.tenant_id
        FeatureToggle.toggle_global('data_sources:automated_reverse_tunnel', true)

        post :submit_reverse_tunnel_ds, params: { tunnel_params: tunnel_params }
        @rt_job = ReverseTunnelJob.last
        @rt_job.update_status 'tunnel_running'
        ReverseTunnelDsService.save_tunnel_port_to_rt_job(@rt_job, 5433)
      end

      it 'updates data source, job and usage' do
        expect(ds.name).to_not eq ds_params[:name]
        post :update_reverse_tunnel_ds, params: { id: ds.id, reverse_tunnel_job_id: @rt_job.id, ds: ds_params }
        expect(response.status).to eq 200
        expect(ds.reload.name).to eq ds_params[:name]
        expect(DataSource.count).to eq(@initial_ds_count)
        expect(@client_public_key_usage.reload.data_source_id).to eq ds.id
        expect(@client_public_key_usage.client_port).to_not eq 5432
        expect(@client_public_key_usage.client_host).to_not eq nil
        expect(ClientPublicKeyUsage.where.not(data_source_id: nil).count).to eq 1
        expect(@rt_job.reload.status).to eq 'success'
      end

      it 'frees old public key usages' do
        @client_public_key_usage.now_used_by!(data_source: ds, reverse_tunnel_params: { client_port: 5432, client_host: 'localhost' })
        new_key = FactoryBot.create :client_public_key, public_key: 'hehe'
        new_usage = FactoryBot.create :client_public_key_usage, client_public_key_id: new_key.id, tunnel_port: 5434, tenant_id: new_key.tenant_id
        ReverseTunnelDsService.save_tunnel_port_to_rt_job(@rt_job, 5434)
        ds.reload
        post :update_reverse_tunnel_ds, params: { id: ds.id, reverse_tunnel_job_id: @rt_job.id, ds: ds_params }
        expect(ClientPublicKeyUsage.where(data_source_id: nil).count).to eq 2
      end
    end
  end

  describe '#synced_tables' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    context 'tables are synchronized' do
      before do
        FeatureToggle.toggle_global('schema_synchronization', true)
      end
      it 'lists all synced tables' do
        get :synced_tables, params: { id: ds.id }
        tables = JSON.parse(response.body)['tables']
        expect(tables.size).to eq ds.current_version.tables.count
        expect(tables).to include('"public"."data_sources"')
      end
    end

    context 'ds is not synced' do
      it 'returns error' do
        get :synced_tables, params: { id: ds.id }
        expect(response.status).to eq 422
      end
    end
  end

  describe '#synced_schemas' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    context 'schemas are synchronized' do
      before do
        FeatureToggle.toggle_global('schema_synchronization', true)
      end
      it 'lists all synced schemas' do
        get :synced_schemas, params: { id: ds.id }
        schemas = JSON.parse(response.body)['schemas']
        expect(schemas.size).to eq ds.current_version.schema_names.count
        # simple spec teng teng teng
        expect(schemas).to include('public')
      end
    end

    context 'ds is not synced' do
      it 'returns error' do
        get :synced_schemas, params: { id: ds.id }
        expect(response.status).to eq 422
      end
    end
  end

  describe '#synced_columns' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    context 'columns are synchronized' do
      before do
        FeatureToggle.toggle_global('schema_synchronization', true)
      end
      it 'lists all synced columns' do
        get :synced_columns, params: { id: ds.id }
        columns = JSON.parse(response.body)['columns']
        expect(columns.size).to eq ds.current_version.columns.count
        # simple spec teng teng teng
        columns.find { |c| c['table_fqname'] == 'public.data_sources' }.should_not be_nil
        columns.find { |c| c['column_name'] == 'id' }.should_not be_nil
      end
    end

    context 'ds is not synced' do
      it 'returns error' do
        get :synced_columns, params: { id: ds.id }
        expect(response.status).to eq 422
      end
    end
  end

  describe '#get_sync_job' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end
    context 'ds is synced but not via a job' do
      before do
        ds.synchronize_schema
      end
      it 'return current version' do
        get :get_sync_job, params: { id: ds.id }
        assert_success_response!
        json = JSON.parse(response.body)
        expect(json['last_run_job']).to be_nil
        expect(json['last_finish_job']).to be_nil
        expect(json['current_version']).to be_truthy
      end
    end
    context 'ds is being synced via a job' do
      before do
        FeatureToggle.toggle_global('schema_synchronization', true)
      end
      it 'returns the last run job, last finish job and current version' do
        get :get_sync_job, params: { id: ds.id }
        assert_success_response!
        json = JSON.parse(response.body)
        expect(json['last_run_job']['id']).to eq Job.last.id
        expect(json['last_finish_job']['id']).to eq Job.last.id
        expect(json['current_version']['id']).to eq ds.current_version.id
      end
    end
    context 'ds is not synced' do
      it 'returns empty last_run_job, last_finish_job, current_version' do
        get :get_sync_job, params: { id: ds.id }
        assert_success_response!
        json = JSON.parse(response.body)
        expect(json['current_version']).to be_nil
        expect(json['last_run_job']).to be_nil
        expect(json['last_finish_job']).to be_nil
      end
    end
  end

  describe '#submit_synchronize_schema' do
    let(:ds) { get_test_ds }
    before do
      FeatureToggle.toggle_global('schema_synchronization', true)
      allow_any_instance_of(DataSource).to receive(:synchronize_schema).and_return true
    end
    it 'returns the synchronization job' do
      post :submit_synchronize_schema, params: { id: ds.id }
      expect(JSON.parse(response.body)['job_id']).to eq Job.last.id
    end

    describe 'belongs to correct job queue' do
      it 'belongs to data_source queue' do
        post :submit_synchronize_schema, params: { id: ds.id }
        job = assert_success_async_response!
        expect(job.tag).to eq('data_source')
      end
    end

    context 'ds has synchronization disabled' do
      before do
        ds.settings[:enable_schema_info] = false
        ds.save!
      end
      it 'returns error' do
        post :submit_synchronize_schema, params: { id: ds.id }, format: :json
        expect(response.status).to eq 422
        expect(response.body).to match(/disabled/)
      end
    end
  end

  describe '#rename' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end

    it 'able to rename' do
      put :rename, format: :json, params: { id: ds.id, ds: { name: 'new-ds-name' } }
      expect(response.status).to eq(200)
      parsed = JSON.parse(response.body)
      expect(parsed['name']).to eq('new-ds-name')
    end
  end

  describe '#soft-delete' do
    let(:ds) do
      FactoryBot.create :data_source,
                         dbtype: 'postgresql',
                         dbconfig: dbconfig_rails_test_env.to_json
    end

    it 'able to soft-delete' do
      post :destroy, format: :json, params: { id: ds.id }
      expect(response).to be_successful
      ds.reload
      expect(ds.deleted_at).to be_present
      expect(ds.dbconfig).to eq({})
    end
  end

  describe '#admin_list' do
    context 'data_source do not have any dependencies' do
      let(:tenant) { get_test_tenant }
      let!(:data_source) { FactoryBot.create :data_source, tenant: tenant, name: 'data_source' }

      it 'delelable and do not have delete_restrict_message' do
        get :admin_list, format: :json

        expect(response).to be_successful
        body = JSON.parse(response.body)

        expect(body.size).to eq 3
        ds = body.find { |v| v['name'] == 'data_source' }

        expect(ds['deletable']).to eq true
        expect(ds['delete_restrict_message']).to eq ''
      end

      context 'Data Source is dest of a Data Import, source_config is nil' do
        let!(:data_import) do
          FactoryBot.create(
            :data_import,
            dest_ds_id: data_source.id,
            dest_schema_name: 'public',
            owner_id: admin.id,
            tenant_id: tenant.id,
            source_type: 'zendesk',
            source_config: nil,
          )
        end

        it 'deletable is true' do
          get :admin_list, format: :json

          expect(response).to be_successful
          body = JSON.parse(response.body)

          ds = body.find { |v| v['name'] == data_source.name }
          expect(ds['deletable']).to eq true
        end
      end
    end

    before do
      FeatureToggle.toggle_global('data_connection:enabled', true)
    end

    context 'data_source have a data_model' do
      include_context 'data_modeling_schema_with_data'

      let(:tenant) { get_test_tenant }
      let(:data_source) { FactoryBot.create :data_source, tenant: tenant, name: 'data_source' }

      before do
        DataSourceVersions::SchemaSynchronizationService.new(data_source).execute
      end

      it 'delelable is false, show proper delete_restrict_message' do
        DataModel.create_from_ds(data_source, owner_id: admin.id)

        get :admin_list, format: :json

        expect(response).to be_successful
        body = JSON.parse(response.body)

        expect(body.size).to eq 3
        ds = body.find { |v| v['name'] == 'data_source' }

        expect(ds['deletable']).to eq false
        expect(ds['delete_restrict_message']).to eq 'Cannot delete since this has data models. '
      end
    end

    context 'one data_source is a source of a Data Import, another one is a dest of a Data Import' do
      let(:tenant) { get_test_tenant }
      let(:data_source_one) { FactoryBot.create :data_source, tenant: tenant, name: 'data_source_one', dbtype: 'zendesk' }
      let(:data_source_two) { FactoryBot.create :data_source, tenant: tenant, name: 'data_source_two' }

      let(:source_config) { { :dbtable => { :ds_id => data_source_one.id, :fqname => 'public.tenants' } } }
      let!(:data_import) {
        FactoryBot.create :data_import,
          dest_ds_id: data_source_two.id,
          dest_schema_name: 'public',
          owner_id: admin.id,
          tenant_id: tenant.id,
          source_type: 'zendesk',
          source_config: source_config
      }

      it 'ds1 is not deletable, ds2 is deletable' do
        get :admin_list, format: :json

        expect(response).to be_successful
        body = JSON.parse(response.body)

        expect(body.size).to eq 4
        ds1 = body.find { |v| v['name'] == 'data_source_one' }
        ds2 = body.find { |v| v['name'] == 'data_source_two' }

        expect(ds1['deletable']).to eq false
        expect(ds1['delete_restrict_message']).to eq 'Cannot delete since this is being used in other sources. '

        expect(ds2['deletable']).to eq true
        expect(ds2['delete_restrict_message']).to eq ''
      end
    end

    context 'data_source have a query report' do
      let(:tenant) { get_test_tenant }
      let(:data_source) { FactoryBot.create :data_source, tenant: tenant, name: 'data_source' }
      let!(:query_report) {
        FactoryBot.create :query_report, owner_id: admin.id, tenant_id: tenant.id, data_source_id: data_source.id
      }

      it 'delelable is false, show proper delete_restrict_message' do
        get :admin_list, format: :json

        expect(response).to be_successful
        body = JSON.parse(response.body)

        expect(body.size).to eq 3
        ds = body.find { |v| v['name'] == 'data_source' }

        expect(ds['deletable']).to eq false
        expect(ds['delete_restrict_message']).to eq 'Cannot delete since this has reports or metrics. '
      end
    end
  end

  describe '#update' do
    let!(:ds) { FactoryBot.create(:data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json) }
    let(:param) {
      {
        name: 'quicksell',
        dbtype: 'postgresql',
        dbconfig: ds.dbconfig.except(:password),
        id: ds.id,
        settings: {
          timezone: 'America/La_Paz',
        },
      }
    }

    it 'can update and reuse existing dbconfig if dbconfig is unchanged' do
      original_dbconfig = ds.dbconfig
      put :update, format: :json, params: { id: ds.id, ds: param }
      expect(response.status).to eq(200)

      data_source = DataSource.find(ds.id)
      expect(data_source.dbconfig).to eq(original_dbconfig.merge(private_key: nil))
      expect(data_source.settings[:timezone]).to eq(param[:settings][:timezone])
    end

    it 'can update password' do
      update_param = param.merge(dbconfig: param[:dbconfig].merge(password: 'ahihi'))
      put :update, format: :json, params: { id: ds.id, ds: update_param }
      expect(response.status).to eq(200)

      data_source = DataSource.find(ds.id)
      expect(data_source.dbconfig).to eq(update_param[:dbconfig].merge(private_key: nil))
    end

    def expect_empty_pw
      data_source = DataSource.find(ds.id)
      expect(data_source.dbconfig[:password].present?).to be_falsy
    end

    it 'when update dbconfig and provide no password, allow update and change the current password to empty' do
      update_param = param.merge(dbconfig: param[:dbconfig].except(:password).merge(host: 'other_host'))
      put :update, format: :json, params: { id: ds.id, ds: update_param }
      expect(response.status).to eq(200)

      data_source = DataSource.find(ds.id)
      expect(data_source.dbconfig[:host]).to eq('other_host')
      expect_empty_pw
    end

    it 'supply nil password should remove current password' do
      update_param = param.merge(dbconfig: param[:dbconfig].merge(password: nil, host: 'other_host'))
      put :update, format: :json, params: { id: ds.id, ds: update_param }
      expect(response.status).to eq(200)
      expect_empty_pw
    end

    it 'supply empty string password should remove current password' do
      update_param = param.merge(dbconfig: param[:dbconfig].merge(password: '', host: 'other_host'))
      put :update, format: :json, params: { id: ds.id, ds: update_param }
      expect(response.status).to eq(200)
      expect_empty_pw
    end
  end

  describe '#upload_dbt_job_id' do
    let(:admin) { users(:admin) }
    let(:current_user) { admin }
    let!(:ds) { FactoryBot.create(:data_source, dbtype: 'postgresql', dbconfig: dbconfig_rails_test_env.to_json) }
    let(:dbt_job_id) { get_test_dbt_job_id.to_i }
    let(:account_id) { get_test_dbt_account_id }
     let(:params) do
      {
        id: ds.id,
        dbt_job_id: dbt_job_id
      }
    end

    shared_examples 'update dbt credentials' do
      it 'can upload' do
        allow_any_instance_of(DataSourcesController).to receive(:_validate_dbt_job_id!).and_return(true)
        post :upload_dbt_job_id, params: params

        expect(response.status).to eq 200
        integration = DbtIntegration.find_by(data_source_id: ds.id, tenant_id: admin.tenant_id)
        expect(integration.dbt_job_id).to eq dbt_job_id
      end

      it 'cannot upload with invalid job id' do
        allow_any_instance_of(DataSourcesController).to receive(:_validate_dbt_job_id!).and_raise(Holistics::InvalidRequest)
        post :upload_dbt_job_id, params: params

        expect(response.status).not_to eq 200
        expect(DbtIntegration.count).to eq 0
      end

      it 'can upload with right job' do
        VCR.use_cassette('dbt_get_correct_job') do
          allow_any_instance_of(Tenant).to receive(:has_dbt_credentials?).and_return(true)
          expect(DbtIntegration.count).to eq 0
          post :upload_dbt_job_id, params: params

          expect(response.status).to eq 200
          integration = DbtIntegration.find_by(data_source_id: ds.id, tenant_id: admin.tenant_id)
          expect(integration.dbt_job_id).to eq dbt_job_id
        end
      end

      let(:wrong_job_id) { 70 }

      it 'cannot upload with no credentials' do
        allow_any_instance_of(Tenant).to receive(:has_dbt_credentials?).and_return(false)
        params.merge!(
          dbt_job_id: wrong_job_id
        )
        post :upload_dbt_job_id, params: params

        expect(response.status).not_to eq 200
        expect(DbtIntegration.count).to eq 0
      end

      it 'cannot upload because cannot retrieve manifest.json file from dbt cloud' do
        allow_any_instance_of(DataSourcesController).to receive(:_validate_dbt_job_id!).and_raise(Holistics::InvalidParameter)
          params.merge!(
            dbt_job_id: wrong_job_id
          )
          post :upload_dbt_job_id, params: params

          expect(response.status).not_to eq 200
          expect(DbtIntegration.count).to eq 0
      end

      it 'can upload but does not update job_id due to same as old job id' do
        allow_any_instance_of(Tenant).to receive(:has_dbt_credentials?).and_return(true)
        integration = DbtIntegration.find_or_create_by(
          data_source_id: ds.id,
          tenant_id: admin.tenant_id,
          dbt_job_id: dbt_job_id
        )
        post :upload_dbt_job_id, params: params

        expect(response.status).to eq 200
        integration.reload
        expect(integration.dbt_job_id).to eq dbt_job_id
      end

      let(:integration) { create(:dbt_integration, data_source_id: ds.id)}

      it 'create new record and not overwrite existing dbt_job_id' do
        allow_any_instance_of(DataSourcesController).to receive(:_validate_dbt_job_id!).and_return(true)
        old_dbt_job_id = integration.dbt_job_id
        params.merge!(dbt_job_id: '99')
        post :upload_dbt_job_id, params: params

        expect(response.status).to eq 200
        integration.reload
        expect(integration.dbt_job_id).to eq old_dbt_job_id

        expect(DbtIntegration.count).to eq 2

        new_integration = DbtIntegration.find_by(dbt_job_id: 99)
        expect(new_integration).not_to be_nil
      end
    end

    context 'upload to tenant settings' do
      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
        current_user.tenant.dbt_token = 'TOKEN'
        current_user.tenant.dbt_account_id = account_id
        current_user.tenant.save!
      end

      it_behaves_like 'update dbt credentials'
    end

    context 'upload to aml project' do
      include_context 'aml_studio_dataset'

      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, true)
        project = proj_repo.project
        project.dbt_token = 'TOKEN'
        project.dbt_account_id = account_id
        project.save!
      end

      it_behaves_like 'update dbt credentials'
    end
  end

  describe '#sync_metadata_from_dbt_cloud' do
    let(:admin) { users(:admin) }
    let(:current_user) { admin }
    let(:current_tenant) { get_test_tenant }
    let(:ds) { get_test_ds }
    let(:dbt_job_id) { get_test_dbt_job_id.to_i }
    let(:account_id) { get_test_dbt_account_id }
    let!(:integration) do
      DbtIntegration.create(
        tenant_id: current_tenant.id,
        data_source_id: ds.id,
        dbt_job_id: dbt_job_id,
        user_id: current_user.id,
      )
    end
    let(:params) { { id: ds.id, dbt_job_id: dbt_job_id } }

    shared_examples 'sync' do
      it 'can sync with new run' do
        VCR.use_cassette('sync_metadata_service/get_run_id_and_artifact') do
          expect { put :sync_metadata_from_dbt_cloud, params: params }.to change { Job.count }.by(1)
          expect(response.status).to eq 200
          expect(integration.upload_method).to eq DbtIntegration::DBT_CLOUD_SYNC_METHOD
        end
      end

      it 'active sync always trigger a new job' do
        integration.update(dbt_latest_run_id: ********)

        VCR.use_cassette('dbt_get_latest_run') do
          expect { put :sync_metadata_from_dbt_cloud, params: params }.to change { Job.count }.by(1)
          expect(response.status).to eq 200
          expect(integration.upload_method).to eq DbtIntegration::DBT_CLOUD_SYNC_METHOD
        end
      end

      it 'cannot sync due to missing dbt_job_id' do
        integration.update!(dbt_job_id: nil)
        put :sync_metadata_from_dbt_cloud, params: params

        expect(response.status).not_to eq 200
      end

      it 'cannot sync due to missing dbt credentials' do
        allow_any_instance_of(Tenant).to receive(:has_dbt_credentials?).and_return(false)
        allow_any_instance_of(AmlStudio::Project).to receive(:has_dbt_credentials?).and_return(false)
        put :sync_metadata_from_dbt_cloud, params: params

        expect(response.status).not_to eq 200
      end
    end

    context 'use credentials in tenant settings' do
      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
        current_user.tenant.dbt_token = 'TOKEN'
        current_user.tenant.dbt_account_id = account_id
        current_user.tenant.save!
      end

      it_behaves_like 'sync'
    end

    context 'use credentials in aml project' do
      include_context 'aml_studio_dataset'

      before do
        sign_in current_user
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS, true)
        project = proj_repo.project
        project.dbt_token = 'TOKEN'
        project.dbt_account_id = account_id
        project.save!
      end

      it_behaves_like 'sync'
    end
  end

  describe '#submit_test_connect' do
    let!(:ds) { get_test_ds }
    let(:param) {
      {
        name: 'quicksell',
        dbtype: 'postgresql',
        dbconfig: ds.dbconfig.except(:password),
        id: ds.id,
      }
    }

    describe 'belongs to correct job queue' do
      before do
        stub_const('ENV', ENV.to_hash.merge({'ON_PREMISE' => false}))
      end

      it 'belongs to data_source queue' do
        post :submit_test_connect, format: :json, params: { test_type: DataSource::TESTTYPE_CONNECT, ds: param  }
        job = assert_success_async_response!
        expect(job.tag).to eq 'data_source'
      end
    end
  end
end
