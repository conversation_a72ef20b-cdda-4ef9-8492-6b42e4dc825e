# typed: false

require 'rails_helper'

describe 'Canal integration', type: :request, otel: true do
  let!(:ds) do
    ds = get_test_ds.dup
    ds.id = 999
    ds.name = 'test_ds'
    ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
    ds.settings[:enable_holistics_canal] = true
    ds.save!
    ds
  end
  let(:current_user) do
    u = get_test_admin
    u.update!(id: 1000)
    u
  end
  let(:dataset_uname) { 'demo_ecommerce' }
  let(:db_dataset) do
    deploy_result # make sure deployment is already done
    d = DataSet.find_by!(uname: dataset_uname)
    d.update!(owner: current_user)
    d
  end

  let(:viz_type) { 'pivot_table' }
  let(:pivot_fields) do
    {
      rows: [
        { path_hash: { field_name: 'country', model_id: 'sales' } },
        { path_hash: { field_name: 'product', model_id: 'sales' } },
      ],
      columns: [
        { path_hash: { field_name: 'month', model_id: 'sales' } },
      ],
      values: [
        { path_hash: { field_name: 'amount', model_id: 'sales' }, aggregation: 'avg' },
      ],
    }
  end
  let(:vs_fields) do
    { 'pivot_data' => pivot_fields }
  end
  let(:adhoc_fields) { [] }
  let(:vs_filters) do
    []
  end
  let(:other_settings) do
    {
      row_total: true,
      column_total: true,
      sub_total: true,
    }
  end
  let(:sort_settings) do
    [
      { field_index: 3, order_direction: 'desc' },
    ]
  end
  let(:aggregation_settings) { nil }
  let(:viz_setting) do
    create(
      :viz_setting,
      source: db_dataset,
      viz_type: viz_type,
      fields: vs_fields,
      settings: {
        'misc' => { 'pagination_size' => 25, 'show_row_number' => true, 'row_limit' => 5000 },
        'sort' => sort_settings,
        'aggregation' => { 'show_total' => false, 'show_average' => false },
        'conditional_formatting' => {},
        aggregation: aggregation_settings,
        others: other_settings,
      },
      filters: vs_filters,
      adhoc_fields: adhoc_fields,
      format:
        { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
    )
  end
  let(:options) do
    {
      page: 1,
    }
  end
  let(:params) do
    {
      viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
      data_set_id: db_dataset.id,
      options: options,
    }
  end
  let(:non_select_query) { false }
  let(:headers) do
    {
      ControllerConstants::Headers::USE_SESSION_AUTH => 1,
      'Accept' => 'application/json',
    }
  end

  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/data_testing'
    end
  end

  before do
    FeatureToggle.toggle_global(Tenant::FT_NEW_TIMEZONE_CONFIG, true)
    FeatureToggle.toggle_global(DataModel::FT_AQL, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_TABLE_V2, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_PIVOT_V2, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_LIMIT_100_PIVOT_COLUMNS, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_LIMIT_100_CHART_PIVOT_COLUMNS, true)
    FeatureToggle.toggle_global(DataModel::FT_PROCESS_DYNAMIC_MODELS, true)
    FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, true)
    FeatureToggle.toggle_global(Canal::Constants::FT_SNOWFLAKE_USE_ARROW_DOWNLOADER, true)
    FeatureToggle.toggle_global(Canal::Constants::FT_CONNECTION_POOL, true)
    FeatureToggle.toggle_global(Canal::Constants::FT_QUERY_API_STREAM_RESPONSE, true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
    FeatureToggle.toggle_global(QueryRun::FT_QUERY_RUNS_WRAPPER, true)
    ENV['TZ'] = 'Etc/UTC'

    sign_in current_user
  end

  def snapshot_test_values!(ex, name, values)
    SnapshotTest.test!(rspec_example: ex, snapshot_name: "#{name}.json") do
      [
        '[',
        values.map do |row|
          "  #{Oj.dump(row, mode: :json)}"
        end.join(",\n"),
        ']',
      ].join("\n")
    end
  end

  def snapshot_test_data!(ex, name, data)
    snapshot_test_values!(ex, name, data.values)
  end

  def snapshot_test_metadata!(ex, name, data)
    SnapshotTest.test!(rspec_example: ex, snapshot_name: "#{name}.metadata.json") do
      if data.is_a?(Viz::Data::Vl::ExplorePaginateResult)
        {
          fields: data.fields,
          column_types: data.column_types,
        }
      else
        {
          fields: data.fields,
          result_info: data.data[2].to_h,
          pivot_opts: data.pivot_opts.as_json,
        }
      end
    end
  end

  def snapshot_test!(ex, name)
    Timecop.freeze('2024-08-30T12:34:56.789101Z')

    post '/viz_data/submit_generate', params: params, as: :json
    job = assert_success_async_response!
    data = Viz::Data::GeneratorService.async_result(job)

    canal_lake_query_spans = otel_finished_spans.select { |s| s.name.include? 'CanalLakeConnector#exec_sql' }
    canal_lake_query_spans.each_with_index do |span, i|
      SnapshotTest.test!(
        span.attributes['h.query'],
        rspec_example: ex,
        snapshot_name: "#{name}.canal_lake_query#{i}.sql",
      )
    end

    snapshot_test_data!(ex, name, data)
    snapshot_test_metadata!(ex, name, data)
    SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: "#{name}.logical_entries")
    SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: "#{name}.physical_entries")

    # test refresh cache
    expect do
      post '/viz_data/submit_generate', params: params.merge(options: options.merge(bust_cache: true)), as: :json
      assert_success_async_response!
    end.to(
      change { Job.count }.by(1)
      .and(not_change { Canal::Cache.count_logical_entries })
      .and(change { Canal::Cache.count_physical_entries }.by(1))
      .and(change { QueryRun.count }.by(1)),
    )
    snapshot_test_data!(ex, name, data)
    snapshot_test_metadata!(ex, name, data)
    SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: "#{name}.refresh.logical_entries")
    SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: "#{name}.refresh.physical_entries")

    # test different cache entry
    different_params = params.merge(
      viz_setting: params[:viz_setting].merge(
        filters: [
          {
            path_hash: { model_id: 'sales', field_name: 'country' },
            operator: 'is',
            values: ['singapore'],
          },
        ],
      ),
    )
    expect do
      post '/viz_data/submit_generate', params: different_params, as: :json
      assert_success_async_response!
    end.to(
      change { Job.count }.by(1)
      .and(change { Canal::Cache.count_logical_entries }.by(1))
      .and(change { Canal::Cache.count_physical_entries }.by(1))
      .and(change { QueryRun.count }.by(1)),
    )
    snapshot_test_data!(ex, "#{name}_filtered", data)
    snapshot_test_metadata!(ex, "#{name}_filtered", data)
    SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: "#{name}.filtered.logical_entries")
    SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: "#{name}.filtered.physical_entries")

    # test use cached data
    expect do
      post '/viz_data/submit_generate', params: params, as: :json
      assert_success_response!
    end.to(
      not_change { Job.count }
      .and(not_change { Canal::Cache.count_logical_entries })
      .and(not_change { Canal::Cache.count_physical_entries })
      .and(not_change { QueryRun.count }),
    )

    cached_response = Oj.load(response.body)
    expect(cached_response['status']).to eq('success')
    if data.is_a?(Viz::Data::Vl::ExplorePaginateResult)
      expect(cached_response.dig('data', 'generated', 'result', 'values')).to eq(data.values.as_json)
    else
      expect(cached_response.dig('data', 'generated', 'result', 'data')[1]).to eq(data.values.as_json)
    end
    SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: "#{name}.filtered.logical_entries")
    SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: "#{name}.filtered.physical_entries")

    # test cache expired
    Timecop.freeze('2024-09-01T12:34:56.789101Z')
    expect do
      post '/viz_data/submit_generate', params: params, as: :json
      assert_success_response!
    end.to(
      change { Job.count }.by(1)
      .and(not_change { Canal::Cache.count_logical_entries })
      .and(change { Canal::Cache.count_physical_entries }.by(1))
      .and(change { QueryRun.count }.by(1)),
    )
    snapshot_test_data!(ex, name, data)
    snapshot_test_metadata!(ex, name, data)
    SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: "#{name}.expired.logical_entries")
    SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: "#{name}.expired.physical_entries")

    # test non-canal not re-using canal cached data
    FeatureToggle.toggle_global(Canal::Constants::FT_ENABLED, false)
    expect do
      post '/viz_data/submit_generate', params: params, as: :json
    end.to(
      change { Job.count }.by(1)
      .and(not_change { Canal::Cache.count_logical_entries })
      .and(not_change { Canal::Cache.count_physical_entries }),
    )
    job = assert_success_async_response!
    non_canal_data = Viz::Data::GeneratorService.async_result(job)
    snapshot_test_data!(ex, "non_canal/#{name}", non_canal_data)
    snapshot_test_metadata!(ex, "non_canal/#{name}", non_canal_data)
    expect(data.meta[:cache_key]).to eq(Canal.make_canal_cache_key(non_canal_data.meta[:cache_key]))
  end

  def execute_adhoc_query(sql, ds, assert_success: true)
    params = {
      adhoc: {
        query: sql,
        data_source_id: ds.id,
        non_select_query: non_select_query,
      },
    }
    post '/adhoc', params: params, headers: headers
    assert_success_response!
    query_id = JSON.parse(response.body)['id']

    get "/adhoc/#{query_id}/job", headers: headers
    job =
      if assert_success
        assert_success_async_response!
      else
        assert_async_response!
      end

    adhoc_query = job.source

    [adhoc_query, job]
  end

  describe 'run explore' do
    it 'works' do |ex|
      snapshot_test!(ex, 'works')
    end

    context 'disabled connection pool' do
      before do
        FeatureToggle.toggle_global(Canal::Constants::FT_CONNECTION_POOL, false)
      end

      it 'works' do |ex|
        snapshot_test!(ex, 'works')
      end
    end

    context 'data table' do
      let(:viz_type) { 'data_table' }
      let(:vs_fields) do
        { 'table_fields' => pivot_fields.values.flatten(1) }
      end
      let(:sort_settings) do
        [
          { field_index: 3, order_direction: 'desc' },
          { field_index: 0, order_direction: 'asc' },
          { field_index: 1, order_direction: 'asc' },
          { field_index: 2, order_direction: 'asc' },
        ]
      end
      let(:aggregation_settings) do
        {
          show_total: true,
          show_average: true,
        }
      end

      it 'works' do |ex|
        snapshot_test!(ex, 'data_table_works')
      end

      context 'more data types' do
        let(:vs_fields) do
          {
            'table_fields' => [
              *pivot_fields.values.flatten(1),
              { path_hash: { field_name: 'date', model_id: 'sales' }, transformation: 'datetrunc month' },
            ],
          }
        end

        it 'works' do |ex|
          snapshot_test!(ex, 'data_table_more_data_types')
        end
      end
    end

    context 'corrupted cache' do
      it 'works when retry' do
        post '/viz_data/submit_generate', params: params, as: :json
        assert_success_async_response!

        physical_entry = Canal::Cache.all_physical_entries.last
        entry = Canal::Cache.fetch_entry(physical_entry['logical_key'])

        Canal::QueryApi.new.call(
          '/do_lake_action',
          :post,
          params: {
            type: 'drop',
            body: {
              cache_keys: [entry.physical_key],
            },
          },
        )

        post '/viz_data/submit_generate', params: params, as: :json
        assert_response_status!(422)
        expect(response.body).to match(/please try again/i)

        post '/viz_data/submit_generate', params: params, as: :json
        assert_success_async_response!
      end
    end
  end

  describe 'export explore' do
    around do |ex|
      S3::ExportAndDownload.mocked = true
      ex.run
    ensure
      S3::ExportAndDownload.mocked = false
    end

    before do
      @results = nil
      allow(Viz::Exporting::ExporterService).to receive(:export_to_filepath).and_wrap_original do |method, *args, **kwargs|
        @results = method.call(*args, **kwargs)
      end
    end

    let(:params) do
      {
        viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
        data_set_id: db_dataset.id,
        export_format: 'csv',
        include_chart: false,
      }
    end

    let(:sort_settings) do
      [
        { field_index: 3, order_direction: 'desc' },
        { field_index: 0, order_direction: 'asc' },
        { field_index: 1, order_direction: 'asc' },
        { field_index: 2, order_direction: 'asc' },
      ]
    end

    it 'exports csv' do |ex|
      post '/viz_data/submit_export', params: params, as: :json, headers: headers
      assert_success_async_response!

      SnapshotTest.test!(rspec_example: ex, snapshot_name: 'export.csv') do
        @results
      end
    end

    it 'exports xlsx' do |ex|
      expect do
        post '/viz_data/submit_export', params: params.merge(export_format: 'xlsx'), as: :json, headers: headers
        assert_success_async_response!
        expect(@results).to eq(:no_error)
      end.to(
        change { Canal::Cache.count_physical_entries }.by(1)
        .and(change { Canal::Cache.count_logical_entries }.by(1))
        .and(change { QueryRun.count }.by(1)),
      )

      # test using cache
      expect do
        post '/viz_data/submit_export', params: params.merge(export_format: 'xlsx'), as: :json, headers: headers
        assert_success_async_response!
        expect(@results).to eq(:no_error)
      end.to(
        not_change { Canal::Cache.count_physical_entries }
        .and(not_change { Canal::Cache.count_logical_entries }),
      )
    end

    describe 'export widget' do
      let(:report) do
        FactoryBot.create :query_report, data_set_id: db_dataset.id, tenant_id: current_user.tenant_id
      end
      let(:dashboard) do
        FactoryBot.create :dashboard, tenant_id: current_user.tenant_id
      end
      let(:report_widget) do
        FactoryBot.create :dashboard_widget, source: report, dashboard: dashboard, tenant_id: current_user.tenant_id
      end

      before do
        viz_setting.update!(source: report)

        expect_any_instance_of(QueryReports::ExportFromCache).to receive(:export_to_io).once.and_call_original
      end

      let(:params) do
        {
          widget_id: report_widget.id,
          format: 'csv',
        }
      end

      it 'exports csv' do |ex|
        post '/dashboard_widgets/submit_export', params: params, as: :json, headers: headers
        job = assert_success_async_response!
        debug_log = assert_job_log!(job, /^S3_FILE_INFO:/, level: 'DEBUG')
        SnapshotTest.test!(rspec_example: ex, snapshot_name: 'widget_csv_s3_file_info.json') do
          # NOTE: S3 does not allow requests that are not in current time -> cannot use Timecop.freeze
          debug_log
            .message
            .gsub('S3_FILE_INFO: ', '')
            .gsub(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, '2024-08-01 12:34:56')
            .gsub(/-\d+\.csv/, '-<saved_at>.csv')
        end

        # test using cache
        expect do
          post '/dashboard_widgets/submit_export', params: params, as: :json, headers: headers
          job = assert_success_async_response!
          assert_job_log!(job, /csv file exists/, level: 'INFO')
        end.to(
          not_change { Canal::Cache.count_physical_entries }
          .and(not_change { Canal::Cache.count_logical_entries }),
        )
      end

      it 'exports png', require_puppeteer: true do |ex|
        post '/dashboard_widgets/submit_export', params: params.merge(format: 'png'), as: :json, headers: headers
        job = assert_success_async_response!
        debug_log = assert_job_log!(job, /^S3_FILE_INFO:/, level: 'DEBUG')
        SnapshotTest.test!(rspec_example: ex, snapshot_name: 'widget_png_s3_file_info.json') do
          # NOTE: S3 does not allow requests that are not in current time -> cannot use Timecop.freeze
          debug_log
            .message
            .gsub('S3_FILE_INFO: ', '')
            .gsub(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, '2024-08-01 12:34:56')
            .gsub(/-\d+\.png/, '-<saved_at>.png')
        end

        # test using cache
        expect do
          post '/dashboard_widgets/submit_export', params: params.merge(format: 'png'), as: :json, headers: headers
          job = assert_success_async_response!
          assert_job_log!(job, /png file exists/, level: 'INFO')
        end.to(
          not_change { Canal::Cache.count_physical_entries }
          .and(not_change { Canal::Cache.count_logical_entries }),
        )
      end
    end
  end

  describe 'dashboard' do
    let(:report) do
      FactoryBot.create :query_report, data_set_id: db_dataset.id, tenant_id: current_user.tenant_id
    end
    let(:dashboard) do
      FactoryBot.create :dashboard, tenant_id: current_user.tenant_id
    end
    let!(:report_widget) do
      FactoryBot.create :dashboard_widget, source: report, dashboard: dashboard, tenant_id: current_user.tenant_id
    end

    before do
      viz_setting.update!(source: report)
    end

    describe 'data schedule' do
      let(:email_schedule) do
        dest = FactoryBot.create :email_dest
        dest.options[:attachment_formats] = ['png', 'csv']
        dest.save!
        FactoryBot.create :email_schedule, source: dashboard, dest: dest, tenant_id: current_user.tenant_id
      end

      it 'sends email', require_puppeteer: true do |ex|
        post "/email_schedules/#{email_schedule.id}/execute"
        assert_success_async_response!

        mail = ActionMailer::Base.deliveries.last
        expect(mail).to be_present
        SnapshotTest.test!(rspec_example: ex, snapshot_name: 'email_attachment_metadata.json') do
          mail.attachments.map do |a|
            {
              name: a.filename,
              mime: a.mime_type,
            }
          end
        end
      end
    end

    describe 'data alert' do
      let(:data_alert) do
        dest = FactoryBot.create :email_dest
        FactoryBot.create :data_alert, source: report_widget, dest: dest, creator: current_user, tenant_id: current_user.tenant_id
      end
      let!(:condition) do
        create(
          :viz_condition,
          source: data_alert,
          field_path: {
            model_id: 'sales',
            field_name: 'country',
          },
          condition: {
            operator: 'not_null',
            values: [],
          },
          user_id: current_user.id,
          tenant_id: current_user.tenant_id,
        )
      end

      it 'sends alert', require_puppeteer: true do |ex|
        post "/api/v2/data_alerts/#{data_alert.id}/submit_execute", headers: headers
        assert_success_async_response!(['job', 'id'])

        mail = ActionMailer::Base.deliveries.last
        expect(mail).to be_present
        expect(mail.text_part.body.decoded).to match(
          /Your data has met the Alert Conditions:[\s\S]*- Country: not NULL/,
        )
      end
    end
  end

  describe 'adhoc query' do
    let(:sql) do
      <<~SQL
        WITH t(a,b,c) AS (
          VALUES
            (123, 12345678901234567890123456789.12345678901234567890123456789, 'england'),
            (456, 12345678901234567890123456789.0, 'vietnam'),
            (1234567890123, 0.12345678901234567890123456789, 'singapore'),
            (null, null, null)
        )
        SELECT * FROM t
      SQL
    end
    let(:params) do
      {
        adhoc: {
          query: sql,
          data_source_id: ds.id,
        },
      }
    end

    it 'runs adhoc query' do |ex|
      Timecop.freeze('2024-08-30T12:34:56.789101Z')

      expect do
        post '/adhoc', params: params, headers: headers
        assert_success_response!
      end.to(
        change { Job.count }.by(1)
        .and(change { Canal::Cache.count_logical_entries }.by(1))
        .and(change { Canal::Cache.count_physical_entries }.by(1))
        .and(change { QueryRun.count }.by(1)),
      )
      query_id = JSON.parse(response.body)['id']

      get "/adhoc/#{query_id}/job", headers: headers
      job = assert_success_async_response!

      get '/adhoc/results', params: { job_id: job.id }, headers: headers
      assert_success_response!
      json = JSON.parse(response.body)
      expect(json['status']).to eq('success')
      cache_model_id = json['data_model']['id']

      viz_setting = FactoryBot.create(:viz_setting)
      explore_params = {
        viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
        root_model_id: json['data_model']['id'],
        options: { page: 1 },
      }
      expect do
        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!
      end.to(
        not_change { Job.count }
        .and(change { Canal::Cache.count_logical_entries }.by(1))
        .and(change { Canal::Cache.count_physical_entries }.by(1))
        .and(change { QueryRun.count }.by(0)),
      )
      result_json = JSON.parse(response.body)
      snapshot_test_values!(ex, 'adhoc_query', result_json.dig('data', 'generated', 'result', 'values'))
      SnapshotTest.test!(rspec_example: ex, snapshot_name: 'adhoc_query_metadata.json') do
        result_json.dig('data', 'generated', 'result', 'column_types')
      end

      # test cached
      expect do
        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!
      end.to(
        not_change { Job.count }
        .and(not_change { Canal::Cache.count_logical_entries })
        .and(not_change { Canal::Cache.count_physical_entries }),
      )
      result_json = JSON.parse(response.body)
      snapshot_test_values!(ex, 'adhoc_query', result_json.dig('data', 'generated', 'result', 'values'))
      SnapshotTest.test!(rspec_example: ex, snapshot_name: 'adhoc_query_metadata.json') do
        result_json.dig('data', 'generated', 'result', 'column_types')
      end

      # test another viz
      viz_setting = FactoryBot.create(:viz_setting, fields: { 'table_fields' => [{ path_hash: { field_name: 'b' } }] })
      explore_params = {
        viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
        root_model_id: cache_model_id,
        options: { page: 1 },
      }
      expect do
        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!
      end.to(
        not_change { Job.count }
        .and(change { Canal::Cache.count_logical_entries }.by(1))
        .and(change { Canal::Cache.count_physical_entries }.by(1)),
      )
      result_json = JSON.parse(response.body)
      snapshot_test_values!(ex, 'adhoc_query2', result_json.dig('data', 'generated', 'result', 'values'))
      SnapshotTest.test!(rspec_example: ex, snapshot_name: 'adhoc_query_metadata2.json') do
        result_json.dig('data', 'generated', 'result', 'column_types')
      end

      canal_lake_query_spans = otel_finished_spans.select { |s| s.name.include? 'CanalLakeConnector#exec_sql' }
      canal_lake_query_spans.each_with_index do |span, i|
        SnapshotTest.test!(
          span.attributes['h.query'],
          rspec_example: ex,
          snapshot_name: "adhoc_query.canal_lake_query#{i}.sql",
        )
      end

      # test database data
      SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: 'adhoc_query.logical_entries')
      SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: 'adhoc_query.physical_entries')
      SnapshotTest.test!(CanalLakeModel.all.to_a.map(&:as_json), rspec_example: ex, snapshot_name: 'adhoc_query.canal_lake_models')
      SnapshotTest.test!(DataModel.all.to_a.map { |dm| Oj.load(dm.to_json) }, rspec_example: ex, snapshot_name: 'adhoc_query.data_models')

      # test field suggestions
      suggestion_params = {
        data_model_id: cache_model_id,
        field_name: 'c',
      }
      post '/viz_data/submit_fetch_field_suggestions', headers: headers, params: suggestion_params
      assert_success_response!
      suggestions = JSON.parse(response.body)['data']
      field_path_model_id = suggestions['field_path'].delete('model_id')
      expect(field_path_model_id).to eq(cache_model_id)
      SnapshotTest.test!(suggestions, rspec_example: ex, snapshot_name: 'adhoc_query.suggestions')
    end

    it 'exports adhoc query' do
      expect do
        post '/adhoc', params: params, headers: headers
        assert_success_response!
      end.to(
        change { Job.count }.by(1)
        .and(change { Canal::Cache.count_logical_entries }.by(1))
        .and(change { Canal::Cache.count_physical_entries }.by(1)),
      )
      query_id = JSON.parse(response.body)['id']

      # NOTE: S3 does not allow requests that are not in current time -> cannot use Timecop.freeze
      expect do
        post "/adhoc/#{query_id}/submit_export", headers: headers
        assert_success_async_response!(['job', 'id'])
      end.to(
        change { Job.count }.by(1)
        .and(not_change { Canal::Cache.count_logical_entries })
        .and(not_change { Canal::Cache.count_physical_entries }),
      )
    end

    context 'query-run restricted' do
      let(:tenant) { current_user.tenant }
      let(:plan) { FactoryBot.create :query_run_plan, base_query_runs: 1 }
      let!(:tenant_subscription) { FactoryBot.create :tenant_subscription, tenant: tenant, plan: plan, max_restriction_level: 'hard', created_at: 1.day.ago }

      before do
        FeatureToggle.toggle_global(QueryRuns::HardRestriction::FT_BILLING_HARD_RESTRICTION, true)

        usage_rule = FactoryBot.create :usage_rule, name: 'AdhocQuery#execute'
        FactoryBot.create :plan_usage_rule, plan: plan, usage_rule: usage_rule, per_additional_usage: 100
      end

      it 'raises error' do
        # run once to reach limit
        post '/adhoc', params: params, headers: headers
        assert_success_response!
        query_id = JSON.parse(response.body)['id']
        get "/adhoc/#{query_id}/job", headers: headers
        assert_success_async_response!
        Cache.flushall # to refresh billing usage

        # run after exceeding limit
        post '/adhoc', params: params, headers: headers
        assert_success_response!
        query_id = JSON.parse(response.body)['id']
        get "/adhoc/#{query_id}/job", headers: headers
        job = assert_async_response!
        assert_job_status!(job, 'failure')
        assert_job_log!(job, /exceeded/, level: 'ERROR')
      end
    end
  end


  describe 'data handling' do
    shared_examples 'handles_data_types' do |name|
      it 'works' do |ex|
        _, job = execute_adhoc_query(sql, ds)
        cache_entry = Canal::Cache.fetch_entry(job.data[:cache_key])
        cache_data = Canal::Cache.fetch_data(cache_entry)

        ruby_string_values = cache_data[1][0].map(&:to_s)
        ruby_types = cache_data[1][0].map(&:class)

        SnapshotTest.test!(ruby_string_values, rspec_example: ex, snapshot_name: "data_handling/#{name}/ruby_values")
        SnapshotTest.test!(ruby_types, rspec_example: ex, snapshot_name: "data_handling/#{name}/ruby_types")

        get '/adhoc/results', params: { job_id: job.id }, headers: headers
        assert_success_response!
        json = JSON.parse(response.body)

        viz_setting = FactoryBot.create(:viz_setting)
        explore_params = {
          viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
          root_model_id: json['data_model']['id'],
          options: { page: 1 },
        }

        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!

        result_json = JSON.parse(response.body)
        values = result_json.dig('data', 'generated', 'result', 'values')[0]
        types = values.map(&:class)
        snapshot_test_values!(ex, "data_handling/#{name}/json_values", result_json.dig('data', 'generated', 'result', 'values'))
        SnapshotTest.test!(types, rspec_example: ex, snapshot_name: "data_handling/#{name}/json_types")
        SnapshotTest.test!(rspec_example: ex, snapshot_name: "data_handling/#{name}/field_labels") do
          result_json.dig('data', 'generated', 'extra_details', 'fields').map { |f| f['label'] }
        end
      end
    end

    describe 'postgres sql' do
      let(:sql) do
        <<-SQL
          SELECT
          null "null",
          1 "int2",
          123456 "int4",
          123456789123456789 "int8",
          123456789123456789.123456789123456789::numeric "numeric",
          0.123456789::float4 "float4",
          0.123456789123456789::float8 "float8",
          'Infinity'::float8 "float8",
          0.123456789123456789::money "money",
          '123123123123123'::varchar(20) "varchar(20)",
          '123123131313121'::char(20) "char(20)",
          '12312312313131231'::text "text",
          '\\xDEADBEEF'::bytea "bytea",
          '\\x'::bytea "empty bytea",
          '2024-03-02T09:02:03.123456Z'::timestamp "timestamp",
          '2024-03-02T09:02:03.678123+09:00'::timestamp with time zone "timestamptz",
          '2024-03-02'::date "date",
          '09:02:03.123456'::time "time",
          '09:02:03.678123+03:00'::time with time zone "timetz",
          interval '3 days 2 seconds' "interval",
          interval '-3 days 2 seconds' "negative interval",
          true "bool",
          '(1,2)'::point "point",
          '[(1,2), (3,4)]'::line "line",
          '[(1,2), (3,4)]'::lseg "lseg",
          '((1,2), (3,4))'::box "box",
          '[(1,2), (3,4), (5,6)]'::path "path",
          '((1,2), (3,4), (5,6))'::polygon "polygon",
          '((0,0), 1)'::circle "circle",
          '192.168.0.1'::inet "inet",
          '192.168.0.1/32'::cidr "cidr",
          '08:00:2b:01:02:03'::macaddr "macaddr",
          '08:00:2b:01:02:03:04:05'::macaddr8 "macaddr8",
          B'101'::bit "bit",
          B'101'::varbit(2) "varbit",
          'a fat cat sat on a mat and ate a fat rat'::tsvector "tsvector",
          'fat & rat'::tsquery "tsquery",
          'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'::UUID "uudi",
          '<foo>bar</foo>'::xml "xml",
          '{"hello": ["world"]}'::json "json",
          '["hello", 123, "world"]'::json "json array",
          '{"hello": ["world"]}'::jsonb "jsonb",
          '["hello", 123, "world"]'::jsonb "jsonb array",
          '{"reading": 1.230e-5}'::jsonb "jsonb with numeric",
          '$.track.segments'::jsonpath "jsonpath",
          '{1, 2, 3}'::int4[] "array int4",
          int4range(10, 20) "int4range",
          int8range(123456789123456780, 123456789123456789) "int8range",
          numrange(1.0, 14.0) "numrange",
          '[2010-01-01 14:45, 2010-01-01 15:45)'::tsrange "tsrange",
          '[2010-01-01 14:45+07:00, 2010-01-01 15:45+03:00)'::tstzrange "tstzrange",
          '[2010-01-01, 2010-09-09]'::daterange "daterange",
          123::oid "oid",
          '456'::xid "xid",
          '(0,26)'::tid "tid",
          'postgres=arwdDxt/postgres'::aclitem as "aclitem",
          ROW(1,2)::record as "record",
          'Hello, World!' as "unknown",
          '{[1,10), [15,20)}'::int4multirange as "int4multirange",
          'bacdfas'::name as "name",
          '(1,2)'::complex as "composite"
        SQL
      end
      let(:cntor) { Connectors.from_ds(ds) }

      before do
        cntor.exec_sql('DROP TYPE IF EXISTS complex')
        cntor.exec_sql(
          <<-SQL,
            CREATE TYPE complex AS (
              r       double precision,
              i       double precision
            );
          SQL
        )
      end

      after do
        cntor.exec_sql('DROP TYPE IF EXISTS complex;')
      end

      it 'test Ruby type mapping' do |ex|
        adhoc_query, job = execute_adhoc_query(sql, ds)
	      cache_entry = Canal::Cache.fetch_entry(job.data[:cache_key])
        cache_data = Canal::Cache.fetch_data(cache_entry)

        ruby_string_values = cache_data[1][0].map(&:to_s)
        ruby_types = cache_data[1][0].map(&:class)

        SnapshotTest.test!(ruby_string_values, rspec_example: ex, snapshot_name: 'data_handling/postgres/ruby_values')
        SnapshotTest.test!(ruby_types, rspec_example: ex, snapshot_name: 'data_handling/postgres/ruby_types')
      end

      it 'test JSON type mapping' do |ex|
        _, job = execute_adhoc_query(sql, ds)

        get '/adhoc/results', params: { job_id: job.id }, headers: headers
        assert_success_response!
        json = JSON.parse(response.body)

        viz_setting = FactoryBot.create(:viz_setting)
        explore_params = {
          viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
          root_model_id: json['data_model']['id'],
          options: { page: 1 },
        }

        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!

        result_json = JSON.parse(response.body)
        values = result_json.dig('data', 'generated', 'result', 'values')[0]
        types = values.map(&:class)
        snapshot_test_values!(ex, 'data_handling/postgres/json_values', result_json.dig('data', 'generated', 'result', 'values'))
        SnapshotTest.test!(types, rspec_example: ex, snapshot_name: 'data_handling/postgres/json_types')
      end

      context 'non-select query' do
        let(:non_select_query) { true }

        include_examples('handles_data_types', 'postgres_non_select')
      end

      context '0 columns' do
        context 'non-select query' do
          let(:non_select_query) { true }

          let(:sql) do
            <<~SQL
              DROP TABLE IF EXISTS ahihihi
            SQL
          end

          include_examples('handles_data_types', 'postgres_0_columns_non_select')
        end
      end
    end

    describe 'bigquery' do
      let(:sql) {
        <<-SQL
          #standardSQL
          SELECT

            NULL,

            [true, false],
            [b'123', b'abc', b''],
            [cast('2023-01-01' AS DATE), cast('2024-01-01' AS DATE)],
            [cast('2023-01-01 00:00:00' AS DATETIME), cast('2023-01-01 00:00:00.015599' AS DATETIME)],
            [ST_GEOGFROMTEXT('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GEOGFROMTEXT('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')],
            [INTERVAL 25 MONTH, INTERVAL 2 YEAR, MAKE_INTERVAL(1,1,1,36,120,69)],
            [JSON '{"a": 10}', JSON '{"b": 10}'],
            [1, 2, 3, 4],
            [CAST(1234567890123456789.123456789 AS NUMERIC), CAST(-1234567890123456789.123456789 AS NUMERIC)],
            [CAST(2.900719925474099 AS BIGNUMERIC), CAST(-2.900719925474099 AS BIGNUMERIC)],
            [CAST(0.123456789 AS FLOAT64), CAST(1 AS FLOAT64)],
            ['123', 'abc', ''],
            [STRUCT(1, 2, 3), STRUCT(4 AS a, 5 AS b, 6 AS c)],
            [CAST('15:21:30' AS TIME), CAST('15:21:30.99' AS TIME)],
            [CAST('2014-09-27 12:30:0-08:00' AS TIMESTAMP), CAST('2014-09-27 12:30:00.45-8:00' AS TIMESTAMP)],
            [RANGE<DATE> '[2023-01-01, 2024-01-01)'],
            [RANGE<DATETIME> '[2023-01-01 00:00:00, 2024-01-01 00:00:00)'],
            [RANGE<TIMESTAMP> '[2023-01-01 00:00:00-8:00, 2024-01-01 00:00:00+8:00)'],
            [STRUCT(1, 2), STRUCT(1 as a, 2 as b)],

            true as true_value,
            false as false_value,

            B'abc' AS type_bytes,
            B'' AS type_empty_bytes,

            cast('2023-01-01' AS DATE) AS type_date,

            cast('2023-01-01 00:00:00' AS DATETIME) AS type_datetime,
            cast('2023-01-01 00:00:00.015599' AS DATETIME) AS type_datetime2,

            ST_GEOGFROMTEXT('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))') AS type_geography,

            INTERVAL 25 MONTH AS type_interval,
            INTERVAL 2 YEAR AS type_interval2,
            INTERVAL 32 day AS type_interval3,
            MAKE_INTERVAL(1,1,1,36,120,69) AS type_interval4,
            INTERVAL -25 MONTH AS type_negative_interval,

            JSON '{"a": 10}' AS type_json,

            1 AS type_int_1,
            -9223372036854775808 AS type_int_2,
            9223372036854775807 AS type_int_3,

            CAST(1 AS NUMERIC),
            CAST(-1 AS NUMERIC),
            CAST(0 AS NUMERIC),
            -CAST(0 AS NUMERIC),
            CAST(1234567890123456789.123456789 AS NUMERIC),
            CAST(-1234567890123456789.123456789 AS NUMERIC),
            CAST(123456789.987654321987654321 AS NUMERIC), -- will be rounded
            CAST(-123456789.987654321987654321 AS NUMERIC), -- will be rounded
            CAST(123456789.123456789 AS NUMERIC),
            CAST(-123456789.123456789 AS NUMERIC),
            CAST(9007199254740992.0 AS NUMERIC),
            CAST(-9007199254740992.0 AS NUMERIC), -- 2^53
            CAST(9007199254740993.0 AS NUMERIC),
            CAST(-9007199254740993.0 AS NUMERIC),
            CAST(0.9007199254740992 AS NUMERIC), -- will be rounded
            CAST(-0.9007199254740992 AS NUMERIC), -- will be rounded
            CAST(0.9007199254740993 AS NUMERIC), -- will be rounded
            CAST(-0.9007199254740993 AS NUMERIC), -- will be rounded
            CAST(2.900719925474099 AS NUMERIC), -- will be rounded
            CAST(-2.900719925474099 AS NUMERIC), -- will be rounded
            -- longest value: 30 whole digits + 9 fraction digits + 1 bit for sign
            CAST(12345678901234567890123456789.123456789 AS NUMERIC),
            CAST(-12345678901234567890123456789.123456789 AS NUMERIC),

            CAST(1 AS BIGNUMERIC),
            CAST(-1 AS BIGNUMERIC),
            CAST(0 AS BIGNUMERIC),
            -CAST(0 AS BIGNUMERIC),
            CAST(1234567890123456789.123456789 AS BIGNUMERIC),
            CAST(-1234567890123456789.123456789 AS BIGNUMERIC),
            CAST(123456789.987654321987654321 AS BIGNUMERIC),
            CAST(-123456789.987654321987654321 AS BIGNUMERIC),
            CAST(123456789.123456789 AS BIGNUMERIC),
            CAST(-123456789.123456789 AS BIGNUMERIC),
            CAST(9007199254740992.0 AS BIGNUMERIC),
            CAST(-9007199254740992.0 AS BIGNUMERIC), -- 2^53
            CAST(9007199254740993.0 AS BIGNUMERIC),
            CAST(-9007199254740993.0 AS BIGNUMERIC),
            CAST(0.9007199254740992 AS BIGNUMERIC),
            CAST(-0.9007199254740992 AS BIGNUMERIC),
            CAST(0.9007199254740993 AS BIGNUMERIC),
            CAST(-0.9007199254740993 AS BIGNUMERIC),
            CAST(2.900719925474099 AS BIGNUMERIC),
            CAST(-2.900719925474099 AS BIGNUMERIC),
            -- longest value of type numeric
            CAST(12345678901234567890123456789.123456789 AS BIGNUMERIC),
            CAST(-12345678901234567890123456789.123456789 AS BIGNUMERIC),
            -- longest value of type bignumeric: 39 whole digits, 38 fraction digits
            CAST(123456789012345678901234567890123456789.12345678901234567890123456789012345678 AS BIGNUMERIC),
            CAST(-123456789012345678901234567890123456789.12345678901234567890123456789012345678 AS BIGNUMERIC),

            CAST(0.123456789 AS FLOAT64),
            CAST(1 AS FLOAT64),
            CAST('inf' AS FLOAT64),
            CAST('-inf' AS FLOAT64),
            CAST('NaN' AS FLOAT64),

            RANGE<DATE> '[2023-01-01, 2024-01-01)',
            RANGE<DATETIME> '[2023-01-01 00:00:00, 2024-01-01 00:00:00)',
            RANGE<TIMESTAMP> '[2023-01-01 00:00:00-8:00, 2024-01-01 00:00:00+8:00)',

            'abc',
            '',
            '나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋',

            STRUCT(1, 2, 3),
            STRUCT(1 AS a, 2 AS b, 3 AS c),
            STRUCT(STRUCT(1)),
            STRUCT(STRUCT(1 AS a) AS a, STRUCT(1 AS a) AS b),
            STRUCT([1,2,3]),
            STRUCT([STRUCT(1), STRUCT(2)]),


            CAST('15:21:30' AS TIME),
            CAST('15:21:30.99' AS TIME),

            CAST('2014-09-27 12:30:0-08:00' AS TIMESTAMP),
            CAST('2014-09-27 12:30:00.45-8:00' AS TIMESTAMP)
        SQL
      }
      let(:ds) do
        ds = get_test_bigquery_ds
        ds.name = 'test_ds'
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end
      let(:cntor) { Connectors.from_ds(ds) }
      let(:match_request_on) { [:method, :uri, :host, :path] }

      it 'test Ruby type mapping' do |ex|
        adhoc_query, job = execute_adhoc_query(sql, ds)
        cache_entry = Canal::Cache.fetch_entry(job.data[:cache_key])
        cache_data = Canal::Cache.fetch_data(cache_entry)

        ruby_string_values = cache_data[1][0].map(&:to_s)
        ruby_types = cache_data[1][0].map(&:class)

        SnapshotTest.test!(ruby_string_values, rspec_example: ex, snapshot_name: 'data_handling/bigquery/ruby_values')
        SnapshotTest.test!(ruby_types, rspec_example: ex, snapshot_name: 'data_handling/bigquery/ruby_types')
      end

      it 'test JSON type mapping' do |ex|
        _, job = execute_adhoc_query(sql, ds)

        get '/adhoc/results', params: { job_id: job.id }, headers: headers
        assert_success_response!
        json = JSON.parse(response.body)

        viz_setting = FactoryBot.create(:viz_setting)
        explore_params = {
          viz_setting: VizSettingSerializer.new(viz_setting, root: false).as_json,
          root_model_id: json['data_model']['id'],
          options: { page: 1 },
        }

        post '/viz_data/submit_generate', params: explore_params, as: :json, headers: headers
        assert_success_response!

        result_json = JSON.parse(response.body)
        values = result_json.dig('data', 'generated', 'result', 'values')[0]
        types = values.map(&:class)
        snapshot_test_values!(ex, 'data_handling/bigquery/json_values', result_json.dig('data', 'generated', 'result', 'values'))
        SnapshotTest.test!(types, rspec_example: ex, snapshot_name: 'data_handling/bigquery/json_types')
      end
    end

    describe 'snowflake', require_snowflake: true do
      let(:sql) do
        <<~SQL
          SELECT
            null "null",
            1::tinyint "tinyint",
            2::smallint "smallint",
            123456 "int",
            123456789123456789 "bigint",
            123456789123456789.123456789123456789::numeric "numeric",
            0.123456789::float4 "float4",
            0.123456789123456789::float8 "float8",
            0.123456789123456789::real "real",
            'Infinity'::float8 "float8 infinity",
            '123123123123123'::varchar(20) "varchar(20)",
            '123123131313121'::char(20) "char(20)",
            '12312312313131231'::text "text",
            '나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋'::text "special._text""$$",
            'DEADBEEF'::binary "binary",
            'DEADBEEF'::varbinary "varbinary",
            true "true_boolean",
            false "false_boolean",
            '2024-03-02'::date "date",
            '09:02:03.123456789'::time "time",
            '2024-03-02T09:02:03.123456Z'::timestamp "timestamp",
            '2024-03-02T09:02:03.678123+09:00'::timestamp_tz "timestamp_tz",
            '2024-03-02T09:02:03.678123+09:00'::timestamp_ltz "timestamp_ltz",
            '2024-03-02T09:02:03.678123+09:00'::timestamp_ntz "timestamp_ntz",
            {'asd': 'zxc', 'qwe': 213} "object",
            [123, '456'] "array",
            TO_VARIANT(0.123) "variant",
            ST_POINT(13.4814, 52.5015) "geography"
        SQL
      end
      let(:ds) do
        ds = get_test_snowflake_timezone_ds
        ds.name = 'test_ds'
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end

      include_examples('handles_data_types', 'snowflake')

      context 'disabled arrow downloader' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_SNOWFLAKE_USE_ARROW_DOWNLOADER, false)
        end

        it 'works' do |ex|
          _, job = execute_adhoc_query(sql, ds)
          cache_entry = Canal::Cache.fetch_entry(job.data[:cache_key])
          cache_data = Canal::Cache.fetch_data(cache_entry)

          ruby_string_values = cache_data[1][0].map(&:to_s)
          ruby_types = cache_data[1][0].map(&:class)

          SnapshotTest.test!(ruby_string_values, rspec_example: ex, snapshot_name: 'data_handling/snowflake/json/ruby_values')
          SnapshotTest.test!(ruby_types, rspec_example: ex, snapshot_name: 'data_handling/snowflake/json/ruby_types')
        end
      end

      context 'using private key' do
        it 'works' do
          ds.dbconfig[:password] = ''
          ds.dbconfig[:private_key] = ''
          ds.save!
          _, job = execute_adhoc_query(sql, ds, assert_success: false)
          assert_failure_job!(job, err: /either Password or Private Key/)

          ds.dbconfig[:private_key] = 'hehe'
          ds.save!
          _, job = execute_adhoc_query(sql, ds, assert_success: false)
          assert_failure_job!(job, err: /invalid format/)

          ds.dbconfig[:private_key] = ENV.fetch('TEST_SNOWFLAKE_PRIVATE_KEY')
          ds.save!
          execute_adhoc_query(sql, ds, assert_success: true)
        end
      end
    end

    describe 'mysql' do
      let(:sql) do
        <<~SQL
          SELECT
            null `null`,
            123456 `int`,
            cast(123456 as unsigned) `uint`,
            123456789123456789 `bigint`,
            cast(123456789123456789.123456789123456789 as decimal(32,3)) `numeric`,
            cast(123456789123456789.123456789123456789 as decimal(60,3)) `bignumeric`,
            0.123456789E0 `double`,
            cast('123123123123123' as char(20)) `varchar(20)`,
            '나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' `special_.text"$$`,
            cast(X'DEADBEEF' as binary) `binary`,
            cast('2024-03-02' as date) `date`,
            cast('29:02:03.123456789' as time) `time`,
            cast('2024-03-02T09:02:03.123456Z' as datetime) `datetime`,
            cast('{"asd": "zxc", "qwe": 213}' as json) `json`,
            ST_GeomFromText('POINT(1 1)') `geography`
        SQL
      end
      let(:ds) do
        ds = get_test_mysql_ds
        ds.name = 'test_ds'
        ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end

      include_examples('handles_data_types', 'mysql')
    end

    describe 'sqlserver' do
      let(:sql) do
        <<~SQL
          SELECT
            null "null",
            123456 "int",
            cast(123456789123456789 as bigint) "bigint",
            cast(123456789123456789.123456789123456789 as decimal(32,3)) "numeric",
            0.123456789E0 "float",
            cast('123123123123123' as varchar(20)) "varchar(20)",
            N'나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' "special_.text""$$",
            cast(0xDEADBEEF as binary) "binary",
            cast('2024-03-02' as date) "date",
            cast('09:02:03.123456789' as time) "time",
            cast('2024-03-02T09:02:03.123456Z' as datetime2) "datetime",
            cast('<html></html>' as xml) "xml",
            geography::STGeomFromText('LINESTRING(-122.360 47.656, -122.343 47.656 )', 4326) "geography"
        SQL
      end
      let(:ds) do
        ds = sqlserver_testdb_ds
        ds.name = 'test_ds'
        ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end

      before do
        skip_on_circleci!('TODO: https://www.notion.so/holistics/Sqlserver-Connector-16df89dc7e49806d923efdaa2f41902f?pvs=4#172f89dc7e49809f820ae460e4eb90b6')
      end

      include_examples('handles_data_types', 'sqlserver')
    end

    describe 'athena' do
      include_context 'athena'

      let(:sql) do
        <<~SQL
          SELECT
            cast(-128 as tinyint) "tinyint",
            cast(127 as tinyint) "tinyint2",
            cast(-32768 as smallint) "smallint",
            cast(32767 as smallint) "smallint2",
            -2147483648 "int",
            2147483647 "int2",
            9223372036854775807 "bigint_literal",
            cast(-9223372036854775808 as bigint) "bigint",
            cast(9223372036854775807 as bigint) "bigint2",
            123456789123456789.123456789123456789 "double_literal",
            cast(123456789123456789.123456789123456789 as real) "float",
            cast(123456789123456789.123456789123456789 as double) "double",
            cast('123456789123456789.123456789123456789' as decimal(25,3)) "decimal",
            cast('123456789123456789.123456789123456789' as decimal(38,5)) "decimal2",
            '나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' "special._text""$$",
            cast('나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' as char(10)) "char",
            cast('나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' as varchar) "varchar",
            X'00 01 02',
            cast(X'DEADBEEF' as varbinary),
            DATE '2024-03-25',
            TIME '10:11:12.123456789',
            TIME '10:11:12.345 -06:00',
            cast('10:11:12.123456789' as TIME(9)),
            cast('10:11:12.123456789' as TIME(1)),
            TIMESTAMP '2024-03-25 11:12:13.123456789',
            TIMESTAMP '2024-03-25 11:12:13.456 Asia/Bangkok',
            TIMESTAMP '2024-03-25 11:12:13.456Z',
            TIMESTAMP '2024-03-25 11:12:13.456 UTC',
            -- NOTE: it seems Athena ignores the minute part in tz offset
            TIMESTAMP '2024-03-25 11:12:13.456 +02',
            TIMESTAMP '2024-03-25 11:12:13.456 -0203',
            TIMESTAMP '2024-03-25 11:12:13.456 +02:03',
            CAST('2024-03-25 11:12:13' as TIMESTAMP(8)),
            INTERVAL '3' MONTH, -- INTERVAL YEAR TO MONTH
            INTERVAL '2' MINUTE, -- NTERVAL DAY TO SECOND
            true,
            ARRAY['one', 'two', 'three'],
            MAP(ARRAY['one', 'two', 'three'], ARRAY[1, 2, 3]),
            ROW('one', 'two', 'three'),
            JSON '{"one":1, "two": 2, "three": 3}',
            UUID '12345678-90ab-cdef-1234-567890abcdef',
            IPADDRESS '********'
          UNION ALL
          SELECT
            #{"null,\n" * 40}
            null
          ORDER BY 1 NULLS LAST
        SQL
      end

      let(:ds) do
        dbconfig = { aws_athena: db_config }
        ds = FactoryBot.create(:data_source, name: 'athena', dbtype: DataSource::DBTYPE_AWS_ATHENA, dbconfig: dbconfig)
        ds.name = 'test_ds'
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end

      include_examples('handles_data_types', 'athena')
    end

    describe 'clickhouse' do
      let(:sql) do
        <<~SQL
          SELECT * FROM (
            SELECT
              0::UInt8 as t1,
              0::UInt16 as t2,
              0::UInt32 as t3,
              0::UInt64 as t4,
              0::UInt128 as t5,
              0::UInt256 as t6,
              0::Int8 as t7,
              0::Int16 as t9,
              0::Int32 as t10,
              0::Int64 as t11,
              0::Int128 as t12,
              0::Int256 as t13,
              0.123456789::Float32 as t14,
              'inf'::Float32 as t15,
              'NaN'::Float32 as t16,
              0.123456789::Float64 as t17,
              'inf'::Float64 as t18,
              'NaN'::Float64 as t19,
              1234567890.3456::DECIMAL as t20,
              '1234567890123456789012345678901234567890123456789012345678901234567890123456'::DECIMAL(76) as t21,
              123456789012.3456::DECIMAL(15,3) as t22,
              123456789::DECIMAL32(0) as t23,
              1.23456789::DECIMAL32(8) as t24,
              123456789012345678::DECIMAL64(0) as t25,
              1.23456789012345678::DECIMAL64(17) as t26,
              '12345678901234567890123456789012345678'::DECIMAL128(0) as t27,
              '1.2345678901234567890123456789012345678'::DECIMAL128(37) as t28,
              '1234567890123456789012345678901234567890123456789012345678901234567890123456'::DECIMAL256(0) as t29,
              '1.234567890123456789012345678901234567890123456789012345678901234567890123456'::DECIMAL256(75) as t30,
              'sm' as t31,
              'lt 나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋' as t32,
              '2019-01-01'::Date as t34,
              '0001-01-01'::Date as t35,
              '2150-06-06'::Date as t36,
              '2019-01-01'::Date32 as t37,
              '0001-01-01'::Date32 as t38,
              '2150-06-06'::Date32 as t39,
              toDateTime('2019-01-01 00:00:00', 'UTC') as t40,
              toDateTime('2019-01-01 00:00:00', 'Asia/Ho_Chi_Minh') as t41,
              toDateTime('1960-01-01 00:00:00', 'UTC') as t42,
              toDateTime('3000-01-01 00:00:00', 'UTC') as t43,
              toDateTime64('2019-01-01 00:00:00.123', 0, 'UTC') as t44,
              toDateTime64('2019-01-01 00:00:00.123', 0, 'Asia/Ho_Chi_Minh') as t45,
              toDateTime64('2019-01-01 00:00:00.12345678901', 9, 'Asia/Ho_Chi_Minh') as t46,
              toDateTime64('0001-01-01 00:00:00', 0, 'UTC') as t47,
              toDateTime64('3000-01-01 00:00:00', 0, 'UTC') as t48,
              '61f0c404-5cb3-11e7-907b-a6006ad3dba0'::UUID as t49,
              '**************'::IPv4 as t50,
              '0.0.0.0'::IPv4 as t51,
              '***************'::IPv4 as t52,
              '0000:0000:0000:0000:0000:0000:0000:0000'::IPv6 as t53,
              'FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF'::IPv6 as t54,
              toLowCardinality('1') as t55,
              toLowCardinality(0) as t,
              toLowCardinality(toDateTime('2019-01-01')) as t56,
              '(10, 10)'::Point as t57,
              toIntervalYear(1) as t58,
              toIntervalMonth(1) as t59,
              toIntervalWeek(1) as t60,
              toIntervalDay(1) as t61,
              toIntervalHour(1) as t62,
              toIntervalMinute(1) as t63,
              toIntervalSecond(1) as t64,
              CAST(([1, 2, 3], [X'DEADBEEF', 'Steady', 'Go']), 'Map(UInt8, String)') as t65,
              [toInt8(0), null] as t66,
              [toInt256('-57896044618658097711785492504343953926634992332820282019728792003956564819968'), null] as t67,
              [toFloat32(0.123456789), null] as t68,
              [1.23456789012345678::DECIMAL64(17), null] as t69,
              ['12345678901234567890123456789012345678'::DECIMAL128(0), null] as t70,
              [
                'sm',
                'lt 나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋',
                null
              ] as t71,
              ['2019-01-01'::Date, null] as t72,
              ['2150-06-06'::Date32, null] as t73,
              [toDateTime64('2019-01-01 00:00:00.123', 0, 'UTC'), null] as t74,
              [toDateTime('3000-01-01 00:00:00', 'UTC'), null] as t75,
              [tuple(1, 'a')] as t76,
              [CAST(([1, 2], ['Steady', 'Go']), 'Map(UInt8, String)')] as t77
              UNION ALL
              SELECT
                #{"NULL,\n" * 55}
                '(10, 10)'::Point,
                #{"NULL,\n" * 7}
                CAST(([1, 2, 3], [X'DEADBEEF', 'Steady', 'Go']), 'Map(UInt8, String)') as t65,
                [toInt8(0), null] as t66,
                [toInt256('-57896044618658097711785492504343953926634992332820282019728792003956564819968'), null] as t67,
                [toFloat32(0.123456789), null] as t68,
                [1.23456789012345678::DECIMAL64(17), null] as t69,
                ['12345678901234567890123456789012345678'::DECIMAL128(0), null] as t70,
                [
                  'sm',
                  'lt 나는 유리를 먹을 수 있어요. 그래도 아프지 않아요	👨‍🔬 Ljœr ye caudran créneþ ý jor cẃran.	✋',
                  null
                ] as t71,
                ['2019-01-01'::Date, null] as t72,
                ['2150-06-06'::Date32, null] as t73,
                [toDateTime64('2019-01-01 00:00:00.123', 0, 'UTC'), null] as t74,
                [toDateTime('3000-01-01 00:00:00', 'UTC'), null] as t75,
                [tuple(1, 'a')] as t76,
                [CAST(([1, 2], ['Steady', 'Go']), 'Map(UInt8, String)')] as t77
            )
            ORDER BY t1 NULLS LAST
        SQL
      end
      let(:ds) do
        ds = clickhouse_testdb_ds
        ds.name = 'test_ds'
        ds.settings[:enable_holistics_canal] = true
        ds.dbconfig[:port] = 9000
        ds.save!
        ds
      end
      before do
        skip_on_circleci!('TODO: https://www.notion.so/holistics/Clickhouse-connector-158f89dc7e49802081cfc13199ada174?pvs=4#17cf89dc7e4980729a75cbcce7cd87e4')
      end

      include_examples('handles_data_types', 'clickhouse')
    end
  end

  describe 'cache cleaner' do
    let(:sql) do
      <<~SQL
        SELECT 1
      SQL
    end
    let(:adhoc_params) do
      {
        adhoc: {
          query: sql,
          data_source_id: ds.id,
        },
      }
    end

    before do
      Timecop.freeze('2024-08-01T12:34:56.789101Z')
      post '/viz_data/submit_generate', params: params, as: :json
      assert_success_async_response!

      post '/adhoc', params: adhoc_params, headers: headers

      Timecop.freeze('2024-09-01T12:34:56.789101Z')
      post '/viz_data/submit_generate', params: params.merge(options: options.merge(bust_cache: true)), as: :json
      assert_success_async_response!

      post '/adhoc', params: adhoc_params, headers: headers
    end

    it 'deletes expired records' do |ex|
      SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: 'before_clean.logical_entries')
      SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: 'before_clean.physical_entries')
      SnapshotTest.test!(CanalLakeModel.all.to_a.map(&:as_json), rspec_example: ex, snapshot_name: 'before_clean.canal_lake_models')

      Timecop.freeze('2024-09-01T12:35:56.789101Z')
      expect do
        Canal::CacheCleaner.new.clean
      end.to(
        change { Canal::Cache.count_logical_entries }.by(-1)
        .and(change { Canal::Cache.count_physical_entries }.by(-2))
        .and(change { CanalLakeModel.count }.by(-1))
        .and(change { DataModel.count }.by(-1)),
      )

      SnapshotTest.test!(Canal::Cache.all_logical_entries, rspec_example: ex, snapshot_name: 'after_clean.logical_entries')
      SnapshotTest.test!(Canal::Cache.all_physical_entries, rspec_example: ex, snapshot_name: 'after_clean.physical_entries')
      SnapshotTest.test!(CanalLakeModel.all.to_a.map(&:as_json), rspec_example: ex, snapshot_name: 'after_clean.canal_lake_models')
    end
  end

  describe 'bust data source exploration cache' do
    it 'clears cache' do
      Timecop.freeze('2024-08-01T12:34:56.789101Z')

      post '/viz_data/submit_generate', params: params, as: :json
      assert_success_async_response!

      different_params = params.merge(
        viz_setting: params[:viz_setting].merge(
          filters: [
            {
              path_hash: { model_id: 'sales', field_name: 'country' },
              operator: 'is',
              values: ['singapore'],
            },
          ],
        ),
      )
      post '/viz_data/submit_generate', params: different_params, as: :json
      assert_success_async_response!

      Timecop.freeze('2024-08-01T16:34:56.789101Z')

      different_params2 = params.merge(
        viz_setting: params[:viz_setting].merge(
          filters: [
            {
              path_hash: { model_id: 'sales', field_name: 'country' },
              operator: 'is',
              values: ['vietnam'],
            },
          ],
        ),
      )
      post '/viz_data/submit_generate', params: different_params2, as: :json
      assert_success_async_response!

      Timecop.freeze('2024-08-01T12:40:56.789101Z')
      # verify that the explorations are cached
      expect(Canal::Cache.unexpired_physical_entries.size).to eq(3)
      expect do
        post '/viz_data/submit_generate', params: params, as: :json
        assert_success_response!

        post '/viz_data/submit_generate', params: different_params2, as: :json
        assert_success_response!
      end.not_to change(Job, :count)

      # bust first 2 entries
      post "/api/v2/data_sources/#{ds.id}/bust_exploration_cache", headers: headers
      job = assert_success_async_response!(['job', 'id'])
      expect(Canal::Cache.unexpired_physical_entries.size).to eq(1)
      assert_job_log!(job, 'Busted 2 cache entries', level: 'INFO')
      expect do
        post '/viz_data/submit_generate', params: params, as: :json
        assert_success_async_response!
      end.to change(Job, :count).by(1)
      expect do
        post '/viz_data/submit_generate', params: different_params2, as: :json
        assert_success_response!
      end.not_to change(Job, :count)

      # bust last entry
      Timecop.freeze('2024-08-01T16:40:56.789101Z')
      expect(Canal::Cache.unexpired_physical_entries.size).to eq(1)
      post "/api/v2/data_sources/#{ds.id}/bust_exploration_cache", headers: headers
      job = assert_success_async_response!(['job', 'id'])
      expect(Canal::Cache.unexpired_physical_entries.size).to eq(0)
      assert_job_log!(job, 'Busted 1 cache entry', level: 'INFO')
      expect do
        post '/viz_data/submit_generate', params: different_params2, as: :json
        assert_success_async_response!
      end.to change(Job, :count).by(1)
    end
  end

  describe 'test connection' do
    it 'tests connection with Canal' do
      post '/data_sources/submit_test_connect', params: { test_type: 'connect', ds: ds.as_json }, headers: headers
      assert_success_async_response!

      canal_query_spans = otel_finished_spans.select { |s| s.name.include? 'Canal::QueryClient' }
      expect(canal_query_spans).to be_present
      expect(canal_query_spans.first.attributes['h.records_count']).to eq('1')
      expect(canal_query_spans.first.attributes['h.fields_count']).to eq('1')
    end

    it 'tests process timezone with Canal' do
      post '/data_sources/submit_test_connect', params: { test_type: 'timezone', ds: ds.as_json }, headers: headers
      assert_success_async_response!

      canal_query_spans = otel_finished_spans.select { |s| s.name.include? 'Canal::QueryClient' }
      expect(canal_query_spans).to be_present
    end
  end

  describe(
    'cancel query',
    retry: 3,
    truncation_mode: true, # use truncation_mode to read job from different Threads
  ) do
    let(:error_message) do
      /canceling statement due to user/
    end

    let(:sql) do
      <<~SQL
        SELECT pg_sleep(100)
      SQL
    end

    let(:waiter) do
      proc do |_job|
        sleep 1 # wait a bit for the query to be submitted to db
      end
    end

    shared_examples 'cancel query' do
      it 'works' do
        # Immediate queuing would lead to
        #   * blocking
        #   * unrealistic database transactions -> race conditions or wrong scenarios that wouldn't happen in actual usage
        # -> We will queue the job manually
        ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '1'

        _, job = execute_adhoc_query(sql, ds, assert_success: false)
        t = Thread.new do
          job._send_to_workers
        end

        waiter.call(job)

        cancel_job = Job.find(job.id).cancel! # find() to make another job instance to avoid potential in-memory race conditions
        cancel_job._send_to_workers
        assert_success_job!(cancel_job)

        t.join

        job.reload
        job.logs.reload
        assert_job_status!(job, 'cancelled')
        assert_job_log!(job, error_message)

        if FeatureToggle.active?(Canal::Constants::FT_REPLACE_RUBY_DRIVERS, job.tenant_id)
          handle_stream_span = otel_finished_spans.select { |s| s.name.include? 'Canal::QueryApi#handle_response_stream' }
          expect(handle_stream_span).to be_present
        end
      end
    end

    context 'postgres' do
      include_examples 'cancel query'

      context 'replace ruby driver' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_REPLACE_RUBY_DRIVERS, true)
          Connectors::PostgresConnector.any_instance.stub(:get_connection)
        end

        include_examples 'cancel query'
      end
    end

    context(
      'snowflake',
      require_snowflake: true,
      skip_on_circleci: true, # could not configure canal image to connect to Holistics DB via ENV yet -> cannot save query id to job
    ) do
      let(:ds) do
        ds = get_test_snowflake_timezone_ds
        ds.name = 'test_ds'
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end
      let(:sql) do
        <<~SQL
          SELECT system$wait(10)
        SQL
      end
      let(:waiter) do
        proc do |job|
          wait_expect(true) do
            Job.where(id: job.id).where("data->'running'->'snowflake'->>'query_id' is not null").exists?
          end
        end
      end
      let(:error_message) do
        /execution canceled/
      end

      include_examples 'cancel query'

      context 'replace ruby driver' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_REPLACE_RUBY_DRIVERS, true)
          Connectors::SnowflakeConnector.any_instance.stub(:get_connection)
        end

        include_examples 'cancel query'
      end
    end

    context 'mysql' do
      let(:ds) do
        ds = get_test_mysql_ds
        ds.name = 'test_ds'
        ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end
      let(:sql) do
        <<~SQL
          SELECT sleep(100)
        SQL
      end
      let(:error_message) do
        # when using KILL, mysql drops the whole connection
        /invalid connection/
      end

      include_examples 'cancel query'

      context 'replace ruby driver' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_REPLACE_RUBY_DRIVERS, true)
          Connectors::MysqlConnector.any_instance.stub(:get_connection)
        end

        include_examples 'cancel query'
      end
    end

    context 'sqlserver' do
      let(:ds) do
        ds = sqlserver_testdb_ds
        ds.name = 'test_ds'
        ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
        ds.settings[:enable_holistics_canal] = true
        ds.save!
        ds
      end
      let(:sql) do
        <<~SQL
          WAITFOR DELAY '00:01:00'
        SQL
      end
      let(:error_message) do
        /SQL Server had internal error/
      end

      before do
        skip_on_circleci!('TODO: https://www.notion.so/holistics/Sqlserver-Connector-16df89dc7e49806d923efdaa2f41902f?pvs=4#172f89dc7e49809f820ae460e4eb90b6')
      end

      include_examples 'cancel query'

      context 'replace ruby driver' do
        before do
          FeatureToggle.toggle_global(Canal::Constants::FT_REPLACE_RUBY_DRIVERS, true)
          Connectors::SqlserverConnector.any_instance.stub(:get_connection)
        end

        include_examples 'cancel query'
      end
    end
  end

  describe 'force running canal' do
    context 'enable FT canal:force_enable_for_successfully_connected' do
      before do
        FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_FOR_SUCCESSFULLY_CONNECTED, true)
      end

      it 'forces run canal correctly' do
        ds = FactoryBot.create :data_source, name: 'test'
        ds.settings[:enable_holistics_canal] = true
        ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?

        disable_canal_ds = FactoryBot.create :data_source, name: 'test_2'
        disable_canal_ds.settings[:enable_holistics_canal] = false
        disable_canal_ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?

        # ds enable canal and have no flag
        expect(Canal.usable_for?(ds)).to eq(true)
        # ds enable canal and have flag
        ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now()
        expect(Canal.usable_for?(ds)).to eq(true)

        # ds disable canal and have no flag
        disable_canal_ds.settings[:enable_holistics_canal] = false
        expect(Canal.usable_for?(disable_canal_ds)).to eq false

        # ds disable canal and have flag
        disable_canal_ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now()
        expect(Canal.usable_for?(disable_canal_ds)).to eq true
      end
    end

    context 'disable FT canal:force_enable_for_successfully_connected' do
      before do
        FeatureToggle.toggle_global(Canal::Constants::FT_FORCE_ENABLE_FOR_SUCCESSFULLY_CONNECTED, false)
      end
      it 'does not run canal even if has flag _has_successfully_connected_canal' do
        disable_canal_ds = FactoryBot.create :data_source, name: 'test'
        disable_canal_ds.settings[:enable_holistics_canal] = false
        disable_canal_ds.dbconfig[:host] = `hostname -i`.strip if on_circleci?
        disable_canal_ds.settings[Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG.to_sym] = DateTime.now()

        expect(Canal.usable_for?(disable_canal_ds)).to eq false
      end
    end
  end
end
