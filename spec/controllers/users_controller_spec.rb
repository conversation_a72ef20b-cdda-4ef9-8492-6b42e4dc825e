# typed: false
# frozen_string_literal: true

require 'rails_helper'
require 'securerandom'

describe UsersController do
  let(:current_user) { get_test_admin }
  let(:grab) { get_tenant('grab') }
  let(:it_admin) { FactoryBot.create(:user, role: User::ROLE_IT_ADMIN, tenant: grab) }

  before { sign_in current_user }

  describe '#show' do
    shared_examples 'show user' do
      it 'returns user correctly for user in the same tenant' do
        user =
          if current_user.admin?
            # admin can get all users in the same tenant
            create(:user, tenant_id: current_user.tenant_id)
          else
            # Analyist/bizuser only can get themself
            current_user
          end
        get :show, format: :json, params: { id: user.id }
        expect(response).to have_http_status(:ok)
        expect(response.body).to be_present
      end

      it 'does not return user of another tenant' do
        user = create(:user, tenant_id: create(:tenant).id)
        get :show, format: :json, params: { id: user.id }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'when current user is an admin' do
      include_examples 'show user'
    end

    context 'when current user is an analyst' do
      let(:current_user) { users(:analyst) }

      include_examples 'show user'
    end

    context 'when current user is a business user' do
      let(:current_user) { users(:bizuser) }

      it 'does not return user for user in the same tenant' do
        user = create(:user, tenant_id: current_user.tenant_id)
        get :show, params: { id: user.id }
        expect(response).to have_http_status(:forbidden)
        expect(response.body).not_to be_present
      end

      it 'does not return user of another tenant' do
        user = create(:user, tenant_id: create(:tenant).id)
        get :show, params: { id: user.id }
        expect(response).to have_http_status(:forbidden)
        expect(response.body).not_to be_present
      end
    end
  end

  describe '#update' do
    context 'when all fields are valid' do
      it 'updates the user password to new password' do
        old_password = users(:admin).encrypted_password
        put :update_password,
            params: { user: { old_password: 'admin', password: 'Beets123', password_confirmation: 'Beets123' } }
        expect(users(:admin).reload.encrypted_password).not_to eq(old_password)
      end

      it 'logs out the user' do
        put :update_password,
            params: { user: { old_password: 'admin', password: 'Beets123', password_confirmation: 'Beets123' } }
        expect(response.status).to be 200
        expect(controller.current_user).to be_nil
      end

      context '2FA is enforced' do
        let(:current_user) { get_test_admin }

        before do
          TwoFactorAuthHelper.enforce_2fa!(current_user.tenant, Time.zone.parse('2024-01-01'))
        end

        it 'returns error require 2FA' do
          FeatureToggle.toggle_global('user:allow_edit_user_email', true)
          Timecop.freeze('2024-02-20') do
            put :update, params: { id: current_user.id, user: { email: '<EMAIL>' } }, format: :json

            assert_response_status!(422)
            expect(response.body).to match(/You must set up Two-factor Authentication \(2FA\) to continue using Holistics/)
          end
        end
      end

      context 'when email changed' do
        let(:user) { create(:user, tenant_id: current_user.tenant.id) }

        it 'invalidates session if exists' do
          FeatureToggle.toggle_global('user:allow_edit_user_email', true)
          expect do
            put :update, params: { id: user.id, user: { email: '<EMAIL>' } }
            user.reload
          end.to change(user, :session_token)

          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'feature toggle user:allow_edit_user_email' do
      let(:user) { create(:user, tenant_id: current_user.tenant.id) }

      it 'feature toggle is on' do
        FeatureToggle.toggle_global('user:allow_edit_user_email', true)
        expect do
          put :update, params: { id: user.id, user: { email: '<EMAIL>' } }
          user.reload
        end.to change(user, :email)
        assert_success_response!
      end

      it 'feature toggle is off' do
        FeatureToggle.toggle_global('user:allow_edit_user_email', false)
        expect do
          put :update, params: { id: user.id, user: { email: '<EMAIL>' } }
          user.reload
        end.not_to change(user, :email)
        assert_response_status!(403)
        expect(response.body).not_to be_present
      end
    end

    context "when the old password does not match user's current password" do
      render_views

      it 'displays a error message' do
        put :update_password,
            params: { user: { old_password: 'carrrrot', password: 'Beets123', password_confirmation: 'Beets123' } }
        expect(response.body).to have_content('Password does not match current password')
      end
    end

    context 'when password has incorrect format' do
      render_views

      it 'displays a error message when password is too short' do
        put :update_password, params: { user: { old_password: 'admin', password: '123', password_confirmation: '123' } }
        expect(response.body).to have_content('Password is too short, or too long, or the format is invalid')
      end

      it 'displays a error message when password is too long' do
        super_long_password = '123456' * 128
        put :update_password,
            params: { user: { old_password: 'admin', password: super_long_password,
                              password_confirmation: super_long_password, } }
        expect(response.body).to have_content('Password is too short, or too long, or the format is invalid')
      end
    end

    context 'when password contains user email' do
      render_views

      it 'displays a error message' do
        put :update_password,
            params: { user: { old_password: 'admin', password: 'adMInxxfW12897',
                              password_confirmation: 'adMInxxfW12897', } }
        expect(response.body).to have_content('Password cannot contain email or tenant')
      end
    end

    context 'when password contains user tenant' do
      render_views

      it 'displays a error message' do
        put :update_password,
            params: { user: { old_password: 'admin', password: 'teSTsddwqwe12897',
                              password_confirmation: 'teSTsddwqwe12897', } }
        expect(response.body).to have_content('Password cannot contain email or tenant')
      end
    end

    context 'when the password confirmation does not match password' do
      render_views

      it 'displays a error message' do
        put :update_password,
            params: { user: { old_password: 'admin', password: 'Beets123', password_confirmation: 'Beets123sss' } }
        expect(response.body).to have_content('Password confirmation doesn\'t match Password')
      end
    end

    context 'when only grab has it_admin role' do
      render_views # allow rspec controller render view

      it 'rejects an update with this role for all other tenants' do
        put :update, params: { id: current_user.id, user: { role: User::ROLE_IT_ADMIN } }

        expect(response.status).to be 422
        expect(response.body).to match(/Role.*not permitted in your tenant/)
      end

      it 'does not update role of other IT Admin' do
        sign_out current_user
        sign_in it_admin

        put :update, params: { id: it_admin.id, user: { role: User::ROLE_ADMIN } }

        it_admin.reload
        expect(it_admin.it_admin?).to be true
      end
    end

    context 'feature toggle user:explorer_role' do
      it 'feature toggle is off' do
        put :update, params: { id: current_user.id, user: { role: User::ROLE_EXPLORER } }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'feature toggle is on' do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        put :update, params: { id: current_user.id, user: { role: User::ROLE_EXPLORER } }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'soft delete' do
    it 'does not allow soft-deleted user to log in' do
      user = FactoryBot.create(:user)
      user.soft_delete!

      expect(user.active_for_authentication?).to be false

      user.undelete!
      expect(user.active_for_authentication?).to be true
    end
  end

  describe 'activate' do
    let(:activation_data) do
      {
        name: 'test user',
        email: '<EMAIL>',
        role: 'analyst',
        tenant_id: get_test_tenant.id,
        password: 'Yoyo6969',
        password_confirmation: 'Yoyo6969',
        title: 'Others',
      }
    end

    let(:user_activation_code) do
      FactoryBot.create(
        :user_activation_code,
        activatable_data: {
          name: 'trial admin',
          role: 'admin',
          email: '<EMAIL>',
          tenant_id: get_test_tenant.id,
        },
        activation_type: :trial_signup,
        tenant_id: get_test_tenant.id,
      )
    end

    it 'allows user activation' do
      FactoryBot.create(:tenant_subscription, tenant_id: current_user.tenant_id, expired_at: DateTime.now + 2.weeks)

      activation_code = FactoryBot.create(:user_activation_code, activatable_data: activation_data)

      post :activate, xhr: true, params: {
        code: activation_code.code,
        user: {
          password: activation_data[:password],
          password_confirmation: activation_data[:password_confirmation],
        },
      }

      expect(response).to have_http_status(:ok)

      user = User.where(email: activation_data[:email]).first
      activity = ActivityLog.find_by(key: 'user.activate')
      expect(user.name).to eq(activation_data[:name])
      expect(user.title).to eq(activation_data[:title])
      expect(activity.trackable_id).to eq(user.id)
      expect(activity.owner_id).to be(current_user.id)
    end

    it 'returns error when invalid code is submitted' do
      before_count = User.all.count

      post :activate, params: { code: SecureRandom.hex(20) }, xhr: true
      expect(response.body).to have_content('Activation code is invalid')

      expect(User.all.count).to eq before_count
    end

    it 'can render set password page' do
      resp = get :activate, params: { code: user_activation_code }

      expect(resp).to render_template('activate')
    end

    it 'redirect to sign in page when user already exists' do
      FactoryBot.create(:user, email: '<EMAIL>', tenant_id: get_test_tenant.id, password: 'password')

      resp = get :activate, params: { code: user_activation_code }

      expect(resp).to redirect_to(new_user_session_path)
    end
  end

  describe 'restore' do
    context 'When user was deleted we will restore that user deleted' do
      it 'restore deleted user will success' do
        newUser = FactoryBot.create(:user, role: 'admin', tenant_id: 1)
        newUser.soft_delete!

        post :restore, params: { id: newUser.id }
        expect(response).to be_successful
      end
    end

    context 'When user was not deleted but we restore it' do
      it 'restore user was not deleted will be throw error' do
        newUser = FactoryBot.create(:user, role: 'admin', tenant_id: 1)

        post :restore, params: { id: newUser.id }
        expect(response.status).to eq(422)
      end
    end

    context 'usage over limit' do
      include_context 'billing/hard_restriction'
      it 'restrict activate user' do
        newUser = FactoryBot.create(:user, role: 'admin', tenant_id: 1)
        newUser.soft_delete!

        post :restore, params: { id: newUser.id }
        assert_response_status!(422)
      end
    end
  end

  describe '#invite' do
    let(:invite_params) do
      { name: 'test user', email: '<EMAIL>', role: 'analyst' }
    end

    it 'tracks invitation' do
      post :invite, params: { user: invite_params }
      activity = ActivityLog.find_by(key: 'user.invite')
      new_user = User.find_by(email: '<EMAIL>')
      expect(activity.owner_id).to eq(current_user.id)
      expect(activity.trackable_id).to eq(new_user.id)
      expect(activity.trackable_type).to eq('User')
    end

    context 'feature toggle user:explorer_role' do
      it 'feature toggle is off' do
        put :invite, params: { user: { **invite_params, role: User::ROLE_EXPLORER } }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'feature toggle is on' do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        put :invite, params: { user: { **invite_params, role: User::ROLE_EXPLORER } }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when only grab has it_admin role' do
      render_views # allow rspec controller render view

      it 'rejects invitation with this role for all other tenants' do
        post :invite, params: { user: { role: User::ROLE_IT_ADMIN } }

        expect(response.status).to be 422
        expect(response.body).to match(/Role.*not permitted in your tenant/)
      end

      it 'works with Grab' do
        sign_out current_user
        sign_in it_admin

        post :invite, params: { user: { role: User::ROLE_IT_ADMIN, name: 'test user', email: '<EMAIL>' } }

        expect(response.status).to be 200
      end
    end

    describe '#resend_invite' do
      it 'resends invitation email for pending user' do
        new_user = UserInvitation.new(current_user).invoke(**invite_params)
        acode = ActivationCode.last

        expect do
          post :resend_invite, params: { id: new_user.id }
        end.to change { ActionMailer::Base.deliveries.count }.by(1)
      end
    end

    describe '#invite multiple tenant' do
      let(:invite_params) do
        { name: 'Duplicated user', email: '<EMAIL>', role: 'analyst' }
      end

      let!(:duplicated_user) do
        FactoryBot.create(:user, email: '<EMAIL>', role: 'analyst', tenant: current_user.tenant)
      end

      before do
        FactoryBot.create(:tenant_domain, tenant_id: it_admin.tenant_id, domain_name: 'be.holistics.io')
        FactoryBot.create(:tenant_domain, tenant_id: current_user.tenant_id, domain_name: 'grab.holistics.io')
      end

      it 'allow same email being invited for different tenant when tenant domain is available' do
        sign_out current_user
        sign_in it_admin

        post :invite, params: { user: invite_params }
        expect(response.status).to be 200
      end

      it 'raises error when user is already existed in the tenant when tenant_domain is available' do
        post :invite, params: { user: invite_params }
        expect(response.status).to be 422
      end
    end
  end

  describe 'PATCH #add_nux_item' do
    it 'updates one single item successfully' do
      patch :add_nux_item, params: { item: 'data_manager', item_list_name: 'seen_feature_docs' }
      expect(current_user.reload.nux[:seen_feature_docs]).to contain_exactly('data_manager')
    end

    it 'updates a list of items successfully' do
      patch :add_nux_item, params: { item: %w[data_manager data_import], item_list_name: 'seen_feature_docs' }
      expect(current_user.reload.nux[:seen_feature_docs]).to match_array %w[data_manager data_import]
    end
  end

  describe '#request_trial_demo_tenant' do
    let(:admin) do
      FactoryBot.create(:user,
                        email: "hisoka_#{SecureRandom.hex(12)}@gmail.com",
                        name: 'Hisoka Morow',
                        role: User::ROLE_ADMIN,
                        tenant: tenant,)
    end

    let(:tenant) { FactoryBot.create(:tenant, uname: 'test_tenant') }
    let(:demo_tenant) { FactoryBot.create(:tenant, uname: 'trial_demo_tenant') }

    context 'can request demo tenant account' do
      before do
        sign_in admin
      end

      context 'global config is set' do
        before do
          GlobalConfig.set_if_nil('trial_demo_tenant_unames', [demo_tenant.uname])
        end

        it 'email is sent successfully' do
          post :request_access_trial_demo_acount
          expect(response.status).to be 200
          demo_account = User.find_by_email("demo+#{admin.id}@holistics.io")
          expect(demo_account.role).to eq User::ROLE_ANALYST
        end
      end

      context 'global config is not set' do
        before do
          expect(TrialMailer).not_to receive(:request_trial_demo_account).with(
            { id: admin.id, email: admin.email, name: admin.name }, demo_tenant.id,
          )
        end

        it 'error is raised and email is not sent' do
          post :request_access_trial_demo_acount
          expect(response.status).to be 422
        end
      end
    end

    context 'cannot request demo tenant account' do
      before do
        FeatureToggle.toggle_global(Tenant::FT_TRIAL_DEMO_TENANT, true)
        sign_in admin
      end

      context 'global config is set' do
        before do
          GlobalConfig.set_if_nil('trial_demo_tenant_unames', [demo_tenant.uname])
        end

        it 'error is raised and email is not sent' do
          post :request_access_trial_demo_acount
          expect(response.status).to be 403
        end
      end

      context 'global config is not set' do
        before do
          expect(TrialMailer).not_to receive(:request_trial_demo_account).with(
            { id: admin.id, email: admin.email, name: admin.name }, demo_tenant.id,
          )
        end

        it 'error is raised and email is not sent' do
          post :request_access_trial_demo_acount
          expect(response.status).to be 403
        end
      end
    end

    context 'after the trial demo account is soft deleted, user could request to active the account' do
      before do
        sign_in admin
        GlobalConfig.set_if_nil('trial_demo_tenant_unames', [demo_tenant.uname])
      end

      it 're-activate the demo account successfully' do
        post :request_access_trial_demo_acount
        expect(response.status).to be 200

        demo_account = User.find_by_email("demo+#{admin.id}@holistics.io")
        expect(demo_account.role).to eq User::ROLE_ANALYST

        demo_account.soft_delete!

        post :request_access_trial_demo_acount
        expect(response.status).to be 200
        demo_account.reload
        expect(demo_account.role).to eq User::ROLE_ANALYST
        expect(demo_account.is_deleted?).to be false
      end
    end

    context 'every roles that can sign in would request trial demo tenant account' do
      before do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        GlobalConfig.set_if_nil('trial_demo_tenant_unames', [demo_tenant.uname])
      end

      (User::ROLES - [User::ROLE_PUBLIC_USER, User::ROLE_EMBED_USER]).each do |role|
        context "#{role} could request to access the trial demo tenant" do
          let(:user) do
            FactoryBot.create(:user,
                              email: "phuonghang_#{SecureRandom.hex(12)}@gmail.com",
                              name: 'Lady Nguyen Phuong Hang',
                              role: role,
                              tenant: tenant,)
          end

          it 'email is sent successfully' do
            sign_in user

            post :request_access_trial_demo_acount
            expect(response.status).to be 200
            expect(User.find_by_email("demo+#{user.id}@holistics.io").role).to eq User::ROLE_ANALYST
          end
        end
      end
    end
  end

  describe '#info' do
    it 'render serialized the current user' do
      get :info
      json_body = JSON.parse(response.body)
      expect(json_body['id']).to eq current_user.id
      expect(json_body['email']).to eq current_user.email
      expect(json_body['role']).to eq current_user.role
      expect(json_body['dev_mode_enabled']).to eq current_user.dev_mode_enabled
    end
  end

  describe '#toggle_dev_mode' do
    it 'render user serialized with dev mode' do
      get :toggle_dev_mode, params: { dev_mode_enabled: true }
      json_body = JSON.parse(response.body)
      expect(json_body['id']).to eq current_user.id
      expect(json_body['dev_mode_enabled']).to be true

      get :toggle_dev_mode, params: { dev_mode_enabled: false }
      json_body = JSON.parse(response.body)
      expect(json_body['id']).to eq current_user.id
      expect(json_body['dev_mode_enabled']).to be false
    end
  end
end
