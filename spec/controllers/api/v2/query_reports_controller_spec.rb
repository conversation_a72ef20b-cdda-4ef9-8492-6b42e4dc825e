# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Api::V2::QueryReportsController, :api, type: :controller do
  let(:admin) { get_test_admin }
  let(:query) do
    <<-SQL.strip_heredoc
      VALUES ('2013-01-01', 100, 200),
             ('2013-01-02', 200, 400),
             ('2013-01-03', 300, 600)
    SQL
  end
  let(:analyst) { get_test_analyst }
  let(:test_ds) { get_test_ds }
  let(:tenant) { get_test_tenant }
  let(:user) { get_test_user }

  include_context 'data_modeling_schema'

  before do
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_SQL_CREATION, true)
    FeatureToggle.toggle_global('data_source:enable_schema_info', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(DataSet::FT_CREATION_IN_REPORTING, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
    DataSourceVersions::SchemaSynchronizationService.new(test_ds).execute

    admin.update(allow_authentication_token: true)
    analyst.update(allow_authentication_token: true)
    user.update(allow_authentication_token: true)

    set_token_header(admin)
  end

  describe 'SHOW' do
    let!(:order_model) do
      DataModel.from_table(ds_id: test_ds.id, fqname: 'data_modeling.orders')
    end
    let(:dataset) { create(:data_set, root_model_id: order_model.id) }
    let!(:viz) { create(:viz_setting) }
    let!(:qr) do
      create(:query_report, title: 'test', query: 'values({{var_1}},{{var_2}})', data_source_id: test_ds.id,
                            tenant_id: tenant.id, data_set_id: dataset.id, viz_setting: viz,)
    end

    it 'not found item' do
      get :show, params: { id: 9_999_999 }
      assert_response_status!(404)
    end

    it 'able to get' do
      get :show, params: { id: qr.id }
      assert_response_status!(200)
    end

    it 'not permission' do
      set_token_header(analyst)

      get :show, params: { id: qr.id }
      assert_response_status!(403)
      activity = PublicActivity::Activity.last.slice(:denied, :key, :trackable_id, :owner_id)
      expect(activity).to eq({
                               'denied' => true,
                               'key' => 'query_report.read',
                               'trackable_id' => qr.id,
                               'owner_id' => analyst.id,
                             })
    end

    context 'able to get' do
      let!(:expected_response) do
        {
          +'category_id' => 0,
          +'data_set_id' => 1,
          +'id' => 1,
          +'is_adhoc' => false,
          +'personal_item' => nil,
          +'slug' => '1-test',
          +'title' => 'test',
          +'data_source_id' => test_ds.id,
          +'owner_id' => qr.owner_id,
        }
      end

      it 'not include data set' do
        get :show, params: { id: qr.id }
        assert_response_status!(200)
        test_full_hash!(JSON.parse(response.body)['query_report'].except('viz_setting'), expected_response)
      end

      it 'include data set' do
        data_set_response = {
          'data_set' => {
            'category_id' => 0,
            'description' => 'test',
            'from_aml' => false,
            'is_frontend_cached' => false,
            'id' => 1,
            'label' => dataset.title,
            'title' => dataset.title,
            'name' => 'test_data_set',
            'permissions' => {
              'can_crud' => true,
              'can_read' => true,
              'can_explore' => true,
              'can_export_data' => true,
            },
            'project_id' => nil,
            'slug' => '1-test-data-set',
            'tenant_id' => 1,
            'uname' => 'test_data_set',
            'data_source_id' => test_ds.id,
            'owner_id' => qr.owner_id,
            'owner' => qr.owner.name,
          },
        }
        get :show, params: { id: qr.id, include_data_set: true }
        assert_response_status!(200)
        response_object = JSON.parse(response.body)['query_report'].except('viz_setting')
        test_full_hash!(response_object, expected_response.merge(data_set_response))
      end

      it 'not found report' do
        get :show, params: { id: 99_999 }
        assert_response_status!(404)
      end

      it 'not permission' do
        set_token_header(analyst)

        get :show, params: { id: qr.id }
        assert_response_status!(403)
      end
    end

    context 'using different permissions' do
      it 'analyst with unshared data source cannot get query report' do
        set_token_header(analyst)

        get :show, params: { id: qr.id }
        assert_response_status!(403)
      end

      it 'analyst with shared data source can get query report' do
        admin.share(analyst, :read, get_test_ds)

        set_token_header(analyst)

        get :show, params: { id: qr.id }
        assert_response_status!(200)
      end

      it 'user with no permission cannot access' do
        set_token_header(user)

        get :show, params: { id: qr.id }
        assert_response_status!(403)
      end

      it 'user with shared permission can access' do
        admin.share(user, :read, qr)

        set_token_header(user)

        get :show, params: { id: qr.id }
        assert_response_status!(200)
      end
    end
  end
end
