# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe <PERSON>bedControll<PERSON> do
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V1_CREATION, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
  end

  let(:admin) { users(:admin) }

  let(:dashboard) { FactoryBot.create(:dashboard, title: 'Web Performance') }
  let(:sf_fo) { FactoryBot.create(:filter_ownership, var_name: 'fo1', filterable: dashboard) }
  let(:date_sf) { FactoryBot.create(:shared_filter, settings: { type: 'date' }) }
  let(:date_fo) do
    FactoryBot.create(:filter_ownership, var_name: 'date_fo', filterable: dashboard, shared_filter: date_sf)
  end

  let(:dropdown_sf) do
    FactoryBot.create(:shared_filter, settings: { type: 'dropdown',
                                                  records: [%w[sg Singapore], %w[vn Vietnam], %w[th Thailand],
                                                            %w[id Indonesia],],
                                                  value_final: { dropdown: '_all' },
                                                  dropdown_multiselect: true,
                                                  dropdown_source: 'manual',
                                                  dropdown_manual_entries: "sg,Singapore\nvn,Vietnam\nth,Thailand\nid,Indonesia", },)
  end
  let(:dropdown_fo) do
    FactoryBot.create(:filter_ownership, var_name: 'dropdown_fo', filterable: dashboard, shared_filter: dropdown_sf)
  end
  let(:input_sf) { FactoryBot.create(:shared_filter, settings: { type: 'input' }) }
  let(:input_fo) do
    FactoryBot.create(:filter_ownership, var_name: 'input_fo', filterable: dashboard, shared_filter: input_sf, order: 1)
  end
  let(:report) { FactoryBot.create(:query_report) }
  let(:report_input_sf) { FactoryBot.create(:shared_filter, settings: { type: 'input' }) }
  let(:report_input_fo) do
    FactoryBot.create(:filter_ownership, var_name: 'report_input_fo', filterable: report, shared_filter: input_sf)
  end
  let(:user_id) { get_test_admin.id }

  def create_embed_dashboard
    sign_in admin
    fo_hash = sf_fo.to_hash
    fo_hash[:selected_value] = 'nkhdo'
    fo_hash[:settings] = {
      override: true,
      read_only: false,
    }
    date_fo_hash = date_fo.to_hash
    date_fo_hash[:selected_value] = '2018-09-24 Mon'
    date_fo_hash[:settings] = {
      override: false,
      read_only: true,
    }

    filter_values = [fo_hash, date_fo_hash]

    post_request_embed_dashboard(filter_values, [dropdown_fo.id, input_fo.id])
    assert_success_response!

    clear_previous_raw_post_data
    sign_out admin
    @controller = EmbedController.new
    EmbedLink.last
  end

  # [RAILS BUGS]
  # Request params do not change when call multiple actions in single test (https://github.com/rails/rails/issues/31643)
  # TODO: Clean up after upgrading to rails v5.2
  def clear_previous_raw_post_data
    request.env.delete 'RAW_POST_DATA'
    request.env.delete 'CONTENT_LENGTH'
  end

  def post_request_embed_dashboard(fo_hashes, identifier_variable_ids)
    post :create, params: {
      embed_link: {
        title: 'awesome title',
        source_type: 'Dashboard',
        source_id: dashboard.id,
        public_user_id: user_id,
        identifier_variable_ids: identifier_variable_ids,
        filter_values: fo_hashes,
      },
      format: :json,
    }, as: :json
    @controller = described_class.new
    response
  end

  def update_embed_dashboard(link, identifier_variable_ids)
    fo_hash = sf_fo.to_hash
    fo_hash[:selected_value] = 'nkhdo'
    fo_hash[:settings] = {
      override: false,
      read_only: false,
    }

    put :update, params: {
      id: link.id,
      embed_link: {
        **link.as_json.symbolize_keys,
        identifier_variable_ids: identifier_variable_ids,
        filter_values: [fo_hash],
      },
    }
    response
  end

  context 'signed in as admin' do
    before { sign_in admin }

    context 'fail to create embed dashboard' do
      describe 'create' do
        it 'able to create embed link with no identifer variables' do
          post_request_embed_dashboard([], [])
        end

        it 'raise error if create embed link with filters is not dropdown or text input' do
          post_request_embed_dashboard([], [date_fo.id])
          expect(response.status).to eq 422
          error_msgs = JSON.parse(response.body)
          expect(error_msgs['errors'][0]).to eq('Filter type must be either dropdown or text input')
        end

        it 'filters which are not belonged to the report will not added into embed link' do
          post_request_embed_dashboard([], [report_input_fo.id, input_fo.id])
          expect(response.status).to eq 200
          fos = Dashboard.last.filter_ownerships
          expect(fos.length).to eq(1)
          expect(fos[0][:id]).to eq(input_fo[:id])
        end
      end
    end

    context 'create_embed_dashboard' do
      before do
        @link = create_embed_dashboard
        sign_in admin
      end

      describe 'create' do
        it 'can create a new embed link, only save filter value when override setting is true' do
          expect(@link.source).to eq dashboard
          expect(@link.identifier_filter_ownerships.length).to eq(2)
          expect(@link.public_user.public_user?).to eq true
          expect(@link.identifier_filter_ownerships).to contain_exactly(dropdown_fo, input_fo)

          fv = @link.filter_values.find { |_fv| _fv.filter_ownership_id == sf_fo.id }
          expect(fv.settings).to eq({ override: true, read_only: false })
          expect(fv.value[:selected_value]).to eq 'nkhdo'
        end

        it 'can set secret key' do
          secret_key = @link.secret_key
          expect(!secret_key.empty?).to eq(true)
        end

        context 'v3 dashboard' do
          let(:dashboard) { FactoryBot.create(:dashboard, title: 'Web Performance', version: 3) }

          it 'raises error when trying to create another one' do
            expect { create_embed_dashboard }.to raise_error(/already has/)
          end
        end

        context 'Hard restriction' do
          include_context 'billing/hard_restriction'
          it 'restrict create new embed link' do
            expect { create_embed_dashboard }.to raise_error(/exceeded your current plan/)
          end
        end
      end

      describe 'update' do
        it 'can update a new embed link, only save filter value when override setting is true' do
          response = update_embed_dashboard(@link, [dropdown_fo.id])
          expect(response.status).to eq 200
          expect(@link.identifier_filter_ownerships).to eq([dropdown_fo])
          fv = @link.filter_values.find { |_fv| _fv.filter_ownership_id == sf_fo.id }
          expect(fv.settings).to eq({ override: false, read_only: false })
          expect(fv.value[:selected_value]).to eq nil
        end

        it 'can update with empty identifier variables' do
          response = update_embed_dashboard(@link, [])
          expect(response.status).to eq(200)
        end

        it 'filters which are not belonged to the report will not added into embed link' do
          response = update_embed_dashboard(@link, [-1, report_input_fo.id, dropdown_fo.id])
          expect(response.status).to eq 200
          expect(@link.identifier_filter_ownerships).to eq([dropdown_fo])
          fv = @link.filter_values.find { |_fv| _fv.filter_ownership_id == sf_fo.id }
          expect(fv.settings).to eq({ override: false, read_only: false })
          expect(fv.value[:selected_value]).to eq nil
        end
      end
    end

    describe 'GET #index' do
      let(:user) { get_test_user }

      before do
        3.times do
          link = create(:embed_link,
                        hash_code: SecureRandom.base64,
                        source_type: 'Dashboard',
                        source_id: dashboard.id,
                        public_user_id: user_id,
                        params: {},
                        owner_id: user.id,
                        tenant_id: user.tenant_id,)
          link.embed_link_identifier_variables.create!(filter_ownership: dropdown_fo, tenant: user.tenant)
          link.embed_link_identifier_variables.create!(filter_ownership: input_fo, tenant: user.tenant)
          link.reload
        end
      end

      it 'gets all embed links' do
        get :index, format: :json
        parsed = JSON.parse response.body
        expect(parsed.count).to eq(3)

        first = parsed[0]
        record = EmbedLink.find(first['id'])
        expect(first['owner']).to eq(user.name)
        expect(first['hash_code']).to eq(record.hash_code)
        expect(first['url']).to match(/embed/)
        expect(first['url']).to match(/\?_token=<TOKEN>/)
        expect(first['filter_values'][0]['var_name']).to eq(dropdown_fo.var_name)
        expect(first['filter_values'][0]['selected_value']).to eq(nil) # no actual FilterValue record so selected value is nil
        expect(first['filter_values'][1]['var_name']).to eq(input_fo.var_name)
        expect(first['filter_values'][1]['selected_value']).to eq(nil) # no actual FilterValue record so selected value is nil
        expect(first['source_title']).to eq(dashboard.title)
        expect(first['source_id']).to eq(dashboard.id)
        expect(first['title']).to eq(record.title)
        expect(first['identifier_variable_ids'].count).to eq(2)
        expect(first['identifier_variable_ids']).to match_array(record.identifier_filter_ownerships.map(&:id))
      end

      context 'fetching embed links for dashboard v3' do
        let(:dashboard_v3) { create(:dashboard, version: 3) }
        let!(:embed_link_v3) do
          create(:embed_link,
                 hash_code: SecureRandom.base64,
                 source_type: 'Dashboard',
                 source_id: dashboard_v3.id,
                 public_user_id: user_id,
                 params: {},
                 owner_id: user.id,
                 version: 3,
                 filter_ownerships: [],)
        end

        it 'returnses only the v3 link' do
          get :index, format: :json, params: { source_id: dashboard_v3.id, source_type: 'Dashboard' }
          parsed = JSON.parse response.body
          expect(parsed.count).to eq(1)
          expect(parsed.first['id']).to eq(embed_link_v3.id)
          expect(parsed.first['version']).to eq(3)
        end
      end
    end

    describe 'DELETE #index' do
      before do
        @link = create_embed_dashboard
        sign_in admin
      end

      it 'is able to delete embed link' do
        delete :destroy, params: { id: @link.id }
        expect(response.status).to eq(200)
      end
    end
  end

  context 'as public user' do
    before do
      @link = create_embed_dashboard
    end

    describe 'display' do
      let(:identifier_variable_values) do
        {
          dropdown_fo.var_name => [1],
          input_fo.var_name => 1,
        }
      end

      it 'sends and authenticate successfully' do
        sk = @link.secret_key
        token = jwt_encode(sk, identifier_variable_values, Time.now.to_i + (24 * 60 * 60))

        get :display, params: {
          hashcode: @link.hash_code,
          _token: token,
        }, format: :html

        expect(response.status).to eq 200
      end

      context 'when we send wrong token' do
        it 'permission denied' do
          sk = @link.secret_key + 'blah'
          token = jwt_encode(sk, identifier_variable_values, Time.now.to_i + (60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :json

          expect(response.status).to eq 403
        end
      end

      context 'when we send expired token' do
        it 'permission denied' do
          sk = @link.secret_key
          token = jwt_encode(sk, identifier_variable_values, Time.now.to_i + 1)

          Timecop.freeze(Time.now + 1.hour) do
            get :display, params: {
              hashcode: @link.hash_code,
              _token: token,
            }, format: :json

            expect(response.status).to eq 403
            puts "===> response.body: #{response.body.inspect}"
          end
        end
      end

      it 'sends and authenticate successfully with 5 embeded workers' do
        ts = FactoryBot.create(:tenant_subscription, status: 'active')
        ts.update_embed_workers! 5
        sk = @link.secret_key
        token = jwt_encode(sk, identifier_variable_values, Time.now.to_i + (24 * 60 * 60))

        get :display, params: {
          hashcode: @link.hash_code,
          _token: token,
        }, format: :html

        expect(response.status).to eq 200
      end

      context 'embed portal with ssbi' do
        include_context 'embed_portal_with_ssbi'

        it 'automatically create external embed user' do
          expect(ExternalUser.find_by_client_user(client_user_id: client_user_id, client_org_id: client_org_id,
                                                  tenant_id: tenant.id,).present?).to be(false)

          get :display, params: {
            hashcode: embed_link.hash_code,
            _token: embed_token,
          }, format: :html

          expect(response.status).to eq 200
          expect(ExternalUser.find_by_client_user(client_user_id: client_user_id, client_org_id: client_org_id,
                                                  tenant_id: tenant.id,).present?).to be(true)
        end
      end
    end

    describe '#fetch_dashboard' do
      let(:identifier_variable_values) do
        {
          dropdown_fo.var_name => [1],
          input_fo.var_name => 1,
        }
      end
      let!(:link) { create_embed_dashboard }
      let(:sk) { link.secret_key }
      let(:embed_payload) { identifier_variable_values }
      let(:token) { jwt_encode(sk, embed_payload, Time.now.to_i + (24 * 60 * 60)) }
      let!(:dashboard_widget) { FactoryBot.create(:dashboard_widget, dashboard: dashboard, source: report) }

      before { sign_out admin }

      it 'fetch info of embedded dashboard' do
        get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
        expect(response.status).to eq 200
        expect(JSON.parse(response.body)['id']).to eq 1
      end

      context 'embed link v3' do
        let(:embed_payload) do
          {}
        end

        before do
          link.update!(version: 3)
          link.identifier_filter_ownerships.destroy_all
          request.headers['Accept'] = 'application/json'
        end

        shared_examples 'fetch_dashboard_for_embed_user' do
          it 'returns correct info of embedded dashboard' do
            get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
            assert_success_response!
            res = JSON.parse(response.body)
            expect(res['id']).to eq dashboard.id
            expect(res['permissions']['can_export']).to eq true
            expect(res['widgets'].all? { |w| w['permissions']['can_export'] }).to eq true
            expect(res['widgets'].any? { |w| w['permissions']['can_export_data'] }).to eq false
          end

          context 'dashboard date drill setting' do
            context 'when v3 config is set' do
              before do
                dashboard.settings[:date_drill_enabled] = true
                dashboard.save!
              end

              let(:embed_payload) do
                {
                  settings: {
                    enable_export_data: true,
                    enable_date_drill: false,
                  },
                }
              end

              it 'setting will follow embed v3 config' do
                get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
                assert_success_response!
                res = JSON.parse(response.body)
                expect(res['settings']['date_drill_enabled']).to be false
              end
            end

            context 'when v3 config is not set' do
              let(:embed_payload) do
                {
                  settings: {
                    enable_export_data: true,
                  },
                }
              end

              it 'setting will follow dashboard config' do
                dashboard.settings[:date_drill_enabled] = false
                dashboard.save!

                get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
                assert_success_response!
                res = JSON.parse(response.body)
                expect(res['settings']['date_drill_enabled']).to be false

                dashboard.settings[:date_drill_enabled] = true
                dashboard.save!

                get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
                assert_success_response!
                res = JSON.parse(response.body)
                expect(res['settings']['date_drill_enabled']).to be true
              end
            end
          end

          context 'enable_export_data = true, enable_date_drill = false' do
            let(:embed_payload) do
              {
                settings: {
                  enable_export_data: true,
                  enable_date_drill: false,
                },
              }
            end

            it 'returns correct info of embedded dashboard' do
              get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
              assert_success_response!
              res = JSON.parse(response.body)
              expect(res['id']).to eq dashboard.id
              expect(res['permissions']['can_export']).to eq true
              expect(res['widgets'].all? { |w| w['permissions']['can_export'] }).to eq true
              expect(res['widgets'].all? { |w| w['permissions']['can_export_data'] }).to eq true
            end
          end
        end

        context 'use v3_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
          end

          it_behaves_like 'fetch_dashboard_for_embed_user'
        end

        context 'use portal_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
          end

          it_behaves_like 'fetch_dashboard_for_embed_user'
        end
      end

      context 'invalid token' do
        let(:sk) { link.secret_key + 'zxc' }

        it 'raise permission denied' do
          get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
          expect(response.status).to eq 403
        end
      end

      context 'Track acivity log' do
        it 'is tracked' do
          get :fetch_dashboard, params: { hashcode: link.hash_code, _token: token }
          assert_success_response!
          activity_log = ActivityLog.last
          expect(activity_log.trackable_id).to eq(dashboard.id)
        end
      end
    end

    describe 'Legacy Customer filter' do
      before do
        sign_in admin
        response = post_request_embed_dashboard(filter_values, [input_fo.id])
        expect(response.status).to eq 200
        @link = EmbedLink.last
        @link.embed_link_identifier_variables[0].legacy_customer_filter = true
        @link.embed_link_identifier_variables[0].save!
      end

      let(:customer_values) do
        {
          customer_id: 'some custom data',
        }
      end

      it 'is able to display' do
        sign_out admin
        sk = @link.secret_key
        token = jwt_encode(sk, customer_values, Time.now.to_i + (24 * 60 * 60))

        get :display, params: {
          hashcode: @link.hash_code,
          _token: token,
        }, format: :html

        expect(response.status).to eq 200
      end

      it 'raise error when update legacy embed link' do
        response = update_embed_dashboard(@link, [])
        expect(response.status).to eq 422
        error_msgs = JSON.parse(response.body)
        expect(error_msgs['errors'][0]).to eq('Can not update legacy EmbedLink')
      end

      it 'has customer_filter flag when getting list embed link' do
        get :index, format: :json
        expect(response.status).to eq(200)
        data = JSON.parse(response.body)
        expect(data[0]['customer_filter_ownership_id']).to eq(@link.embed_link_identifier_variables[0].filter_ownership_id)
      end
    end
  end

  context 'when users display embed link v3' do
    before do
      @link = create_embed_dashboard
      @link.version = 3
      @link.identifier_filter_ownerships.destroy_all
      @link.save!
    end

    context 'display with v3 embed link' do
      let(:identifier_variable_values) do
        {
          settings: {},
          filters: {},
          permissions: { row_based: [] },
          drillthroughs: {},
        }
      end

      context 'when embeding valid configs' do
        it 'displays successfully' do
          sk = @link.secret_key
          token = jwt_encode(sk, identifier_variable_values, Time.now.to_i + (24 * 60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :html

          expect(response.status).to eq 200
        end
      end

      context 'when embeding invalid configs' do
        it 'raises errors' do
          sk = @link.secret_key
          token = jwt_encode(sk, identifier_variable_values.merge(settings: '{}'), Time.now.to_i + (24 * 60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :json

          expect(response.status).to eq 422
          expect(response.body).to match(/Invalid value for `settings`./)
          expect(response.body).to match(/Expected type: Object./)

          token = jwt_encode(sk, identifier_variable_values.merge(filters: '{}'), Time.now.to_i + (24 * 60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :json

          expect(response.status).to eq 422
          expect(response.body).to match(/Invalid value for `filters`./)
          expect(response.body).to match(/Expected type: Object./)

          token = jwt_encode(sk, identifier_variable_values.merge(permissions: '{}'), Time.now.to_i + (24 * 60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :json

          expect(response.status).to eq 422
          expect(response.body).to match(/Invalid value for `permissions`./)
          expect(response.body).to match(/Expected type: Object./)

          token = jwt_encode(sk, identifier_variable_values.merge(drillthroughs: '{}'), Time.now.to_i + (24 * 60 * 60))

          get :display, params: {
            hashcode: @link.hash_code,
            _token: token,
          }, format: :json

          expect(response.status).to eq 422
          expect(response.body).to match(/Invalid value for `drillthroughs`./)
          expect(response.body).to match(/Expected type: Object./)
        end
      end
    end
  end

  describe 'GET #fetch_identifier_variables' do
    it 'gets identifier variables' do
      @link = create_embed_dashboard
      get :fetch_identifier_variables, format: :json, params: {
        hashcode: @link.hash_code,
        secret_key: @link.secret_key,
      }

      parsed = JSON.parse response.body
      expect(parsed.count).to eq(2)
      hashes = [dropdown_fo.to_hash.recursive_stringify_keys, input_fo.to_hash.recursive_stringify_keys]
      expect(parsed).to match_array(hashes)
    end

    it 'allows CORS from https://www.holistics.io only' do
      @link = create_embed_dashboard
      headers = { 'Origin' => 'https://example.com' }
      request.headers.merge! headers
      get :fetch_identifier_variables, format: :json, params: {
        hashcode: @link.hash_code,
        secret_key: @link.secret_key,
      }
      expect(response.headers['Access-Control-Allow-Origin']).to eq(nil)

      headers = { 'Origin' => 'https://www.holistics.io' }
      request.headers.merge! headers
      get :fetch_identifier_variables, format: :json, params: {
        hashcode: @link.hash_code,
        secret_key: @link.secret_key,
      }
      expect(response.headers['Access-Control-Allow-Origin']).to eq('https://www.holistics.io')
    end
  end
end
