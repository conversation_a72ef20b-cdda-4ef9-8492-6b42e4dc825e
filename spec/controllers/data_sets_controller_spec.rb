# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe DataSetsController do
  let(:ds) { get_test_ds }
  let(:ds2) { get_test_ds2 }
  let(:admin) { users(:admin) }
  let(:admin2) { get_test_admin2 }
  let(:current_user) { admin }
  let(:another_user) { admin2 }
  let!(:dmc1) { create(:data_model_category, tenant: admin.tenant, parent_id: 0) }
  let!(:dmc2) { create(:data_model_category, tenant: admin2.tenant, parent_id: 0) }
  let(:source_model) { create(:data_model, name: 'source_model', tenant_id: admin.tenant_id, category_id: dmc1.id) }
  let(:orders_model) { create(:data_model, name: 'orders_model', tenant_id: admin.tenant_id, category_id: dmc1.id) }
  let(:products_model) { create(:data_model, name: 'products_model', tenant_id: admin.tenant_id, category_id: dmc1.id) }
  let(:orders_model2) { create(:data_model, name: 'orders_model2', tenant_id: admin2.tenant_id, category_id: dmc2.id) }
  let(:data_set_params) do
    {
      data_set: {
        data_source_id: ds.id,
        root_model_id: source_model.id,
        data_model_ids: [orders_model.id, products_model.id],
        title: 'Sample data set',
        description: 'ahihi',
        category_id: 1,
      },
    }
  end

  include_context 'data_modeling_schema'

  before do
    request.content_type = 'application/json'
    FeatureToggle.toggle_global('data_source:enable_schema_info', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(DataSet::FT_CREATION_IN_REPORTING, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
    DataSourceVersions::SchemaSynchronizationService.new(ds).execute
    sign_in current_user
  end

  describe '#index' do
    let!(:ds_2) { create(:data_source) }
    let!(:ds_3) { create(:data_source, tenant: create(:tenant)) }
    let(:data_set_1) { create(:data_set) }
    let(:data_set_2) do
      create(
        :data_set,
        data_source_id: ds_2.id,
        root_model_id: create(:data_model, data_source_id: ds_2.id, tenant_id: admin.tenant_id,
                                           category_id: dmc1.id,).id,
      )
    end
    let(:data_set_3) do
      create(:data_set, tenant_id: ds_3.tenant_id,
                        data_source_id: ds_3.id,
                        root_model_id: create(:data_model, data_source_id: ds_3.id, tenant_id: ds_3.tenant_id, category_id: 0).id,)
    end
    let!(:data_sets) do
      [
        data_set_1,
        data_set_2,
        data_set_3,
      ]
    end

    it 'can return the list of current data sets that belongs to current tenant' do
      get :index, format: :json
      results = JSON.parse(response.body)

      expect(response).to be_successful
      expect(results.length).to eq 2
    end

    context 'with source params' do
      include_context 'query_model_dataset_based_report'
      let(:dashboard) { FactoryBot.create(:dashboard, version: 3) }
      let(:qr) { query_model_dataset_based_report }
      let!(:report_widget) { FactoryBot.create(:dashboard_widget, source: qr, dashboard: dashboard) }

      it 'returns the datasets used in dashboard only' do
        get :index, format: :json, params: { source_id: dashboard.id, source_type: 'Dashboard' }
        results = JSON.parse(response.body)
        expect(results.length).to eq 1
        expect(results.map { |r| r['id'] }).to eq([qr.data_set_id])
      end

      context 'analyst user' do
        let(:current_user) { users(:analyst) }

        it 'returns the accesible datasets used in dashboard only' do
          get :index, format: :json, params: { source_id: dashboard.id, source_type: 'Dashboard' }
          assert_success_response!
          results = JSON.parse(response.body)
          expect(results.length).to eq 0

          admin.share(current_user, :read, qr.data_set)
          @controller = described_class.new

          get :index, format: :json, params: { source_id: dashboard.id, source_type: 'Dashboard' }
          assert_success_response!
          results = JSON.parse(response.body)
          expect(results.length).to eq 1
          expect(results.map { |r| r['id'] }).to eq([qr.data_set_id])
        end
      end

      context 'user cannot access dashboard' do
        let(:current_user) { users(:bizuser) }

        it 'raises permission denied' do
          get :index, format: :json, params: { source_id: dashboard.id, source_type: 'Dashboard' }
          assert_response_status!(403)
        end
      end
    end

    context 'with ids param' do
      let!(:data_set_4) do
        create(
          :data_set,
          data_source_id: ds_2.id,
        )
      end

      it 'returns datasets with requested ids' do
        get :index, format: :json, params: { ids: [data_set_2, data_set_3, data_set_4] }
        assert_success_response!

        results = JSON.parse(response.body)
        expect(results.map { |dataset| dataset['id'] }).to eq(
          [
            data_set_2.id,
            # data_set_3 belongs to another tenant,
            data_set_4.id,
          ],
        )
      end
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'only return datasets that analyst can access' do
        get :index, format: :json
        results = JSON.parse(response.body)

        expect(response).to be_successful
        expect(results.length).to eq 0

        admin.share(current_user, :read, ds_2)
        @controller = described_class.new

        get :index, format: :json
        results = JSON.parse(response.body)

        expect(response).to be_successful
        expect(results.length).to eq 1
      end
    end

    context 'user is an business user' do
      let(:current_user) { users(:bizuser) }

      it 'returns only shared datasets' do
        admin.share(current_user, :read, data_sets[1])
        get :index, format: :json
        results = JSON.parse(response.body)

        expect(response).to be_successful
        expect(results.length).to eq 1
      end
    end
  end

  describe '#create' do
    let(:pgcache_model) do
      report = create(:query_report)
      ReportCache.cache_type = 'postgres'
      PostgresCache.flushall
      job = report.async(execute_inline: true).execute(current_user.id, {})
      get_test_pgcache_model(job.data[:cache_key])
    end

    let(:data_set_params2) do
      {
        data_set: {
          data_source_id: ds.id,
          root_model_id: source_model.id,
          data_model_ids: [orders_model2.id, products_model.id],
          title: 'Sample data set',
          description: 'ahihi',
          category_id: 1,
        },
      }
    end

    it 'can create new dataset from existed model' do
      post :create, params: data_set_params

      expect(response).to be_successful
      expect(DataSet.count).to eq 1
      result = JSON.parse(response.body)
      expect(result['description']).to eq data_set_params[:data_set][:description]
    end

    it 'cannot create new dataset from unauthorized existed model' do
      post :create, params: data_set_params2

      assert_response_status!(403)
      expect(DataSet.count).to eq 0
    end

    context 'with join_configs' do
      include_context 'data_explore_ctx'

      let(:data_set_params) do
        {
          data_set: {
            data_source_id: ds.id,
            root_model_id: orders_model.id,
            data_model_ids: [orders_model.id, countries_model.id],
            title: 'Sample data set',
            description: 'ahihi',
            category_id: 1,
          },
        }
      end

      let(:data_set_params2) do
        {
          data_set: {
            data_source_id: ds.id,
            root_model_id: orders_model.id,
            data_model_ids: [orders_model2.id, countries_model.id],
            title: 'Sample data set',
            description: 'ahihi',
            category_id: 1,
          },
        }
      end

      it 'can create join configs for dataset' do
        join_configs = [
          {
            active: false,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
          {
            active: true,
            source_model_id: cities_model.id,
            direction: 'forward',
            join_id: cities_countries_join.id,
            join_on: '${SOURCE.country_code} = ${DEST.code}',
            link_type: 'many_to_one',
            dest_model_id: countries_model.id,
          },
          {
            active: true,
            source_model_id: users_model.id,
            direction: 'two_way',
            join_id: users_cities_join.id,
            join_on: '${SOURCE.city_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: cities_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs)
        post :create, params: data_set_params

        expect(JoinConfig.count).to eq(3)

        order_products_join_config = JoinConfig.find_by(join_id: orders_products_join.id)
        cities_countries_join_config = JoinConfig.find_by(join_id: cities_countries_join.id)
        users_cities_join_config = JoinConfig.find_by(join_id: users_cities_join.id)

        expect(order_products_join_config.active).to eq(false)
        expect(order_products_join_config.direction).to eq('backward')
        expect(order_products_join_config.source_model_id).to eq(orders_model.id)

        expect(cities_countries_join_config.active).to eq(true)
        expect(cities_countries_join_config.direction).to eq('forward')
        expect(cities_countries_join_config.source_model_id).to eq(cities_model.id)

        expect(users_cities_join_config.active).to eq(true)
        expect(users_cities_join_config.direction).to eq('two_way')
        expect(users_cities_join_config.source_model_id).to eq(users_model.id)
      end

      it 'cannot create join configs for dataset from unauthorized data models' do
        join_configs = [
          {
            active: false,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: orders_model2.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs)
        post :create, params: data_set_params

        assert_response_status!(403)

        expect(JoinConfig.count).to eq(0)
      end
    end

    it 'can create new dataset from a pgcache model' do
      data_set_params[:data_set].merge!(
        data_source_id: pgcache_model.data_source_id,
        root_model_id: pgcache_model.id,
      )

      post :create, params: data_set_params

      expect(response).to have_http_status(:success)
      expect(DataSet.count).to eq 1
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'can create new dataset from a pgcache model' do
        data_set_params[:data_set].merge!(
          data_source_id: pgcache_model.data_source_id,
          root_model_id: pgcache_model.id,
          data_model_ids: [pgcache_model.id],
        )

        post :create, params: data_set_params

        expect(response).to have_http_status(:success)
        expect(DataSet.count).to eq 1
      end

      it 'can only create datasets belongs to a data source that analyst can access' do
        post :create, params: data_set_params

        expect(response).to have_http_status(:forbidden)
        expect(DataSet.count).to eq 0

        admin.share(current_user, :read, ds)
        @controller = described_class.new

        post :create, params: data_set_params

        expect(response).to be_successful
        expect(DataSet.count).to eq 1
      end
    end

    context 'user is an business user' do
      let(:current_user) { users(:bizuser) }

      it 'cannot create new dataset from existed model' do
        post :create, params: data_set_params

        expect(response).to have_http_status(:forbidden)
        expect(DataSet.count).to eq 0
      end
    end
  end

  describe '#show' do
    let!(:data_set) { create(:data_set) }

    it 'is able to retrieve a dataset with its model' do
      get :show, params: { id: data_set.id }, format: :json

      result = JSON.parse(response.body)

      expect(response).to be_successful
      expect(result['title']).to eq 'test_data_set'
      expect(result['owner']).to eq admin.name
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'can only view datasets belongs to a data source that analyst can access' do
        get :show, params: { id: data_set.id }, format: :json

        result = JSON.parse(response.body)

        expect(response).to have_http_status(:forbidden)

        admin.share(current_user, :read, data_set.data_source)
        @controller = described_class.new

        get :show, params: { id: data_set.id }, format: :json

        result = JSON.parse(response.body)

        expect(response).to have_http_status(:success)
        expect(result['title']).to eq 'test_data_set'
        expect(result['owner']).to eq admin.name
      end
    end

    context 'user is an business user' do
      let(:current_user) { users(:bizuser) }

      it 'can only view shared dataset' do
        get :show, params: { id: data_set.id }, format: :json

        result = JSON.parse(response.body)

        expect(response).to have_http_status(:forbidden)

        admin.share(current_user, :read, data_set)
        @controller = described_class.new

        get :show, params: { id: data_set.id }, format: :json

        result = JSON.parse(response.body)

        expect(response).to have_http_status(:success)
        expect(result['title']).to eq 'test_data_set'
        expect(result['owner']).to eq admin.name
      end
    end
  end

  describe '#update' do
    let!(:data_set) { create(:data_set) }

    before do
      data_set.data_models = [orders_model]
      data_set.save!
    end

    it 'is able to update a dataset' do
      put :update,
          params: { id: data_set.id, data_set: { root_model_id: data_set.root_model_id, title: 'Updated name', description: 'This is new description' } }, format: :json

      expect(response).to have_http_status(:success)
      expect(DataSet.find(data_set.id).title).to eq 'Updated name'
      expect(DataSet.find(data_set.id).description).to eq 'This is new description'
    end

    it 'is able to update related models for a dataset' do
      put :update, params: { id: data_set.id, data_set: { data_model_ids: [orders_model.id, products_model.id] } },
                   format: :json

      expect(response).to have_http_status(:success)
      data_set.reload
      expect(data_set.data_models.pluck(:id)).to contain_exactly(orders_model.id, products_model.id)
    end

    it 'is not able to update a dataset with unauthorized data models' do
      put :update,
          params: { id: data_set.id, data_set: { data_model_ids: [orders_model2.id], title: 'Updated name' } }, format: :json

      assert_response_status!(403)
    end

    context 'with join_configs' do
      include_context 'data_explore_ctx'

      let(:data_set_params) do
        {
          data_set: {
            data_source_id: ds.id,
            root_model_id: orders_model.id,
            data_model_ids: [orders_model.id, users_model.id, products_model.id, cities_model.id, countries_model.id],
            title: 'Sample data set',
            description: 'ahihi',
            category_id: 1,
          },
        }
      end

      before do
        join_configs = [
          {
            active: true,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
          {
            active: false,
            source_model_id: cities_model.id,
            direction: 'backward',
            join_id: cities_countries_join.id,
            join_on: '${SOURCE.country_code} = ${DEST.code}',
            link_type: 'many_to_one',
            dest_model_id: countries_model.id,
          },
          {
            active: true,
            source_model_id: users_model.id,
            direction: 'two_way',
            join_id: users_cities_join.id,
            join_on: '${SOURCE.city_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: cities_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs)
        post :create, params: data_set_params
      end

      it 'can update join configs for dataset' do
        join_configs = [
          {
            active: false,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
          {
            active: true,
            source_model_id: users_model.id,
            direction: 'two_way',
            join_id: users_cities_join.id,
            join_on: '${SOURCE.city_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: cities_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs)
        put :update, params: data_set_params.merge(id: DataSet.last.id)

        assert_response_status!(200)

        expect(JoinConfig.count).to eq(2)

        order_products_join_config = JoinConfig.find_by(join_id: orders_products_join.id)
        users_cities_join_config = JoinConfig.find_by(join_id: users_cities_join.id)

        expect(order_products_join_config.active).to eq(false)
        expect(order_products_join_config.direction).to eq('backward')
        expect(order_products_join_config.source_model_id).to eq(orders_model.id)

        expect(users_cities_join_config.active).to eq(true)
        expect(users_cities_join_config.direction).to eq('two_way')
        expect(users_cities_join_config.source_model_id).to eq(users_model.id)
      end

      it 'cannot update join configs for dataset due to unauthorized data models' do
        join_configs = [
          {
            active: false,
            source_model_id: orders_model2.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs,
                                          data_model_ids: [orders_model2.id,
                                                           products_model.id,],)
        put :update, params: data_set_params.merge(id: DataSet.last.id)

        assert_response_status!(403)
        expect(JoinConfig.count).to eq 3

        orders_products_join_config = JoinConfig.find_by(join_id: orders_products_join.id)
        cities_countries_join_config = JoinConfig.find_by(join_id: cities_countries_join.id)
        users_cities_join_config = JoinConfig.find_by(join_id: users_cities_join.id)

        expect(orders_products_join_config.active).to eq(true)
        expect(orders_products_join_config.direction).to eq('backward')
        expect(orders_products_join_config.source_model_id).to eq(orders_model.id)

        expect(cities_countries_join_config.active).to eq(false)
        expect(cities_countries_join_config.direction).to eq('backward')
        expect(cities_countries_join_config.source_model_id).to eq(cities_model.id)

        expect(users_cities_join_config.active).to eq(true)
        expect(users_cities_join_config.direction).to eq('two_way')
        expect(users_cities_join_config.source_model_id).to eq(users_model.id)
      end

      it 'can remove a join_config when removing a source/dest data_model out of the dataset' do
        join_configs = [
          {
            active: true,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
          {
            active: false,
            source_model_id: cities_model.id,
            direction: 'backward',
            join_id: cities_countries_join.id,
            join_on: '${SOURCE.country_code} = ${DEST.code}',
            link_type: 'many_to_one',
            dest_model_id: countries_model.id,
          },
          {
            active: true,
            source_model_id: users_model.id,
            direction: 'two_way',
            join_id: users_cities_join.id,
            join_on: '${SOURCE.city_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: cities_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs,
                                          data_model_ids: [orders_model.id,
                                                           users_model.id, cities_model.id, countries_model.id,],)
        put :update, params: data_set_params.merge(id: DataSet.last.id)

        expect(JoinConfig.count).to eq(2)

        cities_countries_join_config = JoinConfig.find_by(join_id: cities_countries_join.id)
        users_cities_join_config = JoinConfig.find_by(join_id: users_cities_join.id)

        expect(cities_countries_join_config.active).to eq(false)
        expect(cities_countries_join_config.direction).to eq('backward')
        expect(cities_countries_join_config.source_model_id).to eq(cities_model.id)

        expect(users_cities_join_config.active).to eq(true)
        expect(users_cities_join_config.direction).to eq('two_way')
        expect(users_cities_join_config.source_model_id).to eq(users_model.id)
      end

      it 'can remove multiple join_configs when removing multiple source/dest data_models out of the dataset' do
        join_configs = [
          {
            active: true,
            source_model_id: orders_model.id,
            direction: 'backward',
            join_id: orders_products_join.id,
            join_on: '${SOURCE.product_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: products_model.id,
          },
          {
            active: false,
            source_model_id: cities_model.id,
            direction: 'backward',
            join_id: cities_countries_join.id,
            join_on: '${SOURCE.country_code} = ${DEST.code}',
            link_type: 'many_to_one',
            dest_model_id: countries_model.id,
          },
          {
            active: true,
            source_model_id: users_model.id,
            direction: 'two_way',
            join_id: users_cities_join.id,
            join_on: '${SOURCE.city_id} = ${DEST.id}',
            link_type: 'many_to_one',
            dest_model_id: cities_model.id,
          },
        ]
        data_set_params[:data_set].merge!(join_configs: join_configs,
                                          data_model_ids: [cities_model.id,
                                                           countries_model.id,],)
        put :update, params: data_set_params.merge(id: DataSet.last.id)

        expect(JoinConfig.count).to eq(1)

        cities_countries_join_config = JoinConfig.find_by(join_id: cities_countries_join.id)

        expect(cities_countries_join_config.active).to eq(false)
        expect(cities_countries_join_config.direction).to eq('backward')
        expect(cities_countries_join_config.source_model_id).to eq(cities_model.id)
      end
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'can only update datasets belongs to a data source that analyst can access' do
        put :update,
            params: { id: data_set.id, data_set: { root_model_id: data_set.root_model_id, title: 'Updated name' } }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(data_set.reload.title).not_to eq 'Updated name'

        admin.share(current_user, :read, data_set.data_source)
        @controller = described_class.new

        put :update,
            params: { id: data_set.id, data_set: { root_model_id: data_set.root_model_id, title: 'Updated name' } }, format: :json
        expect(response).to have_http_status(:success)
        expect(data_set.reload.title).to eq 'Updated name'
      end
    end

    context 'user is a business user' do
      let(:current_user) { users(:bizuser) }

      it 'is not able to update dataset' do
        put :update, params: { id: data_set.id, data_set: { title: 'Updated name' } }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(data_set.reload.title).not_to eq 'Updated name'
      end
    end
  end

  describe '#related_joins' do
    include_context 'data_explore_ctx'

    let(:data_set_params) do
      {
        data_set: {
          data_source_id: ds.id,
          root_model_id: orders_model.id,
          data_model_ids: [
            orders_model.id,
            countries_model.id,
          ],
          title: 'Sample data set',
          description: 'ahihi',
          category_id: 1,
        },
      }
    end

    it 'returns correct joins for this data set configuration' do
      expected_joins = []

      post :related_joins, params: data_set_params, format: :json

      expect(JSON.parse(response.body).map { |j| j['id'] }).to match_array(expected_joins)

      expected_joins = [
        orders_products_join.id,
        products_merchants_join.id,
        merchants_cities_join.id,
        users_cities_join.id,
        orders_users_join.id,
        cities_countries_join.id,
      ]

      data_set_params[:data_set][:data_model_ids] = [
        orders_model.id,
        products_model.id,
        merchants_model.id,
        cities_model.id,
        users_model.id,
        countries_model.id,
      ]

      post :related_joins, params: data_set_params, format: :json

      expect(JSON.parse(response.body).map { |j| j['id'] }).to match_array(expected_joins)
    end

    it 'does not have N+1 query' do
      data_set_params[:data_set][:data_model_ids] = [
        orders_model.id,
        products_model.id,
        merchants_model.id,
        cities_model.id,
        users_model.id,
        countries_model.id,
      ]
      Prosopite.scan do
        post :related_joins, params: data_set_params, format: :json
      end
    end
  end

  describe '#update_join_configs' do
    include_context 'data_explore_ctx'

    let(:data_set_params) do
      {
        data_set: {
          data_source_id: ds.id,
          root_model_id: orders_model.id,
          data_model_ids: [orders_model.id, countries_model.id],
          title: 'Sample data set',
          description: 'ahihi',
          category_id: 1,
        },
      }
    end

    before do
      join_configs = [
        {
          active: true,
          source_model_id: orders_model.id,
          direction: 'backward',
          join_id: orders_products_join.id,
          join_on: '${SOURCE.product_id} = ${DEST.id}',
          link_type: 'many_to_one',
          dest_model_id: products_model.id,
        },
      ]
      data_set_params[:data_set].merge!(join_configs: join_configs)
      post :create, params: data_set_params
    end

    it 'can update join configs for dataset' do
      join_configs = [
        {
          active: false,
          source_model_id: orders_model.id,
          direction: 'forward',
          join_id: orders_products_join.id,
          join_on: '${SOURCE.product_id} = ${DEST.id}',
          link_type: 'many_to_one',
          dest_model_id: products_model.id,
        },
      ]
      data_set_params[:data_set].merge!(join_configs: join_configs)
      patch :update_join_configs, params: { id: DataSet.last.id, data_set: { join_configs: join_configs } }

      expect(JoinConfig.count).to eq(1)

      order_products_join_config = JoinConfig.find_by(join_id: orders_products_join.id)

      expect(order_products_join_config.active).to eq(false)
      expect(order_products_join_config.direction).to eq('forward')
      expect(order_products_join_config.source_model_id).to eq(orders_model.id)
    end

    it 'cannot update join configs for dataset with unauth models' do
      join_configs = [
        {
          active: false,
          source_model_id: orders_model2.id,
          direction: 'forward',
          join_id: orders_products_join.id,
          join_on: '${SOURCE.product_id} = ${DEST.id}',
          link_type: 'many_to_one',
          dest_model_id: products_model.id,
        },
      ]
      data_set_params[:data_set].merge!(join_configs: join_configs)
      patch :update_join_configs, params: { id: DataSet.last.id, data_set: { join_configs: join_configs } }

      assert_response_status!(403)
      expect(JoinConfig.count).to eq(1)

      order_products_join_config = JoinConfig.find_by(join_id: orders_products_join.id)

      expect(order_products_join_config.active).to eq(true)
      expect(order_products_join_config.direction).to eq('backward')
      expect(order_products_join_config.source_model_id).to eq(orders_model.id)
    end
  end

  describe '#destroy' do
    let!(:data_set) { create(:data_set) }

    it 'is able to delete a dataset' do
      expect(DataSet.count).to eq 1
      post :destroy, params: { id: data_set.id }, format: :json

      expect(response).to be_successful
      expect(DataSet.count).to eq 0
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'can only destroy datasets belongs to a data source that analyst can access' do
        delete :destroy, params: { id: data_set.id }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(DataSet.count).to eq 1

        admin.share(current_user, :read, data_set.data_source)
        @controller = described_class.new

        delete :destroy, params: { id: data_set.id }, format: :json
        expect(response).to have_http_status(:success)
        expect(DataSet.count).to eq 0
      end
    end

    context 'user is a business user' do
      let(:current_user) { users(:bizuser) }

      it 'is not able to delete dataset' do
        expect(DataSet.count).to eq 1
        post :destroy, params: { id: data_set.id }, format: :json

        expect(response).to have_http_status(:forbidden)
        expect(DataSet.count).to eq 1

        admin.share(current_user, :read, data_set)
        @controller = described_class.new

        post :destroy, params: { id: data_set.id }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(DataSet.count).to eq 1
      end
    end

    context 'dataset has dependency' do
      let!(:data_set) { create(:data_set) }
      let!(:report) { create(:query_report, data_set_id: data_set.id) }

      it 'does not delete the dataset when there is a report based on that dataset' do
        post :destroy, params: { id: data_set.id }, format: :json
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['errors'].first).to eq "There are reports created from this #{data_set.identity_with_name_str}"
        expect(DataSet.count).to eq 1
      end
    end
  end

  describe '#rename' do
    let!(:data_set) { create(:data_set) }

    it 'is able to rename a dataset' do
      put :rename, params: { id: data_set.id, data_set: { title: 'Updated name' } }, format: :json

      expect(response).to be_successful
      expect(DataSet.find(data_set.id).title).to eq 'Updated name'
    end

    context 'user is an analyst' do
      let(:current_user) { users(:analyst) }

      it 'can only rename datasets belongs to a data source that analyst can access' do
        put :rename, params: { id: data_set.id, data_set: { title: 'test' } }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(data_set.reload.title).not_to eq 'test'

        admin.share(current_user, :read, data_set.data_source)
        @controller = described_class.new

        put :rename, params: { id: data_set.id, data_set: { title: 'test' } }, format: :json
        expect(response).to have_http_status(:success)
        expect(data_set.reload.title).to eq 'test'
      end
    end

    context 'user is a business user' do
      let(:current_user) { users(:bizuser) }

      it 'is not able to rename dataset' do
        put :rename, params: { id: data_set.id, data_set: { title: 'test' } }, format: :json
        expect(response).to have_http_status(:forbidden)
        expect(data_set.reload.title).not_to eq 'test'
      end
    end
  end

  describe '#add_viz_setting' do
    let!(:data_set) { create(:data_set) }
    let(:adhoc_dimensions) do
      [
        {
          model_id: 'data_modeling_products',
          field: {
            name: 'adhoc_dim',
            sql: 'concat(data_modeling_products.name, data_modeling_products.status)',
            type: 'text',
          },
        },
      ]
    end
    let!(:viz_setting) { create(:viz_setting, amql: { adhoc_fields: adhoc_dimensions }) }

    it 'can add new viz setting' do
      post :add_viz_setting, params: { id: data_set.id, viz_setting: viz_setting.as_json }

      expect(response).to have_http_status(:success)
      expect(data_set.viz_settings.size).to be 1
      expect(data_set.viz_settings[0].amql.adhoc_fields[0]).to be_truthy
    end
  end

  describe '#recents' do
    it 'return the list of recently updated data sets' do
      ds1 = FactoryBot.create(:data_set)
      get :recents, format: :json
      expect(response.status).to eq(200)
      parsed = JSON.parse(response.body)

      expect(parsed.size).to eq(1)
      expect(parsed[0]['id']).to eq(ds1.id)
      expect(parsed[0]['root_model_id']).to eq(ds1.root_model_id)
      expect(parsed[0]['title']).to eq(ds1.title)
    end
  end

  describe '#row_level_permission_rules' do
    let(:admin) { get_test_admin }
    let(:data_set) { query_model_data_set }

    include_context 'simple_query_model_dataset'

    before do
      sign_in admin
      request.content_type = 'application/json'
    end

    describe 'POST' do
      let(:condition) do
        {
          operator: 'is',
          values: ['a'],
        }
      end
      let(:field_path) do
        {
          model_id: query_data_model.id,
          field_name: 'name',
        }
      end
      let!(:rule) do
        FactoryBot.create(
          :row_level_permission_rule,
          data_set_id: data_set.id,
          condition: condition,
          field_path: field_path,
        )
      end
      let!(:rule2) do
        FactoryBot.create(
          :row_level_permission_rule,
          data_set_id: data_set.id,
          condition: condition,
          field_path: field_path,
        )
      end
      let(:new_condition) do
        {
          operator: 'is',
          values: ['ahihi'],
        }
      end
      let(:condition_2) do
        {
          operator: 'is_not',
          values: ['con_meo'],
        }
      end
      let(:params) do
        {
          id: data_set.id,
          row_level_permission_rules: [
            {
              id: -1,
              condition: condition_2,
              field_path: field_path,
            },
            {
              id: rule.id,
              condition: new_condition,
              field_path: field_path,
            },
          ],
        }
      end

      before do
        FeatureToggle.toggle_tenant('aml_studio:enable', admin.tenant.id, true)
        FeatureToggle.toggle_tenant(ModelFieldDependency::FT_MODEL_FIELD_DEPENDENCIES, admin.tenant.id, true)
      end

      after do
        FeatureToggle.toggle_tenant('aml_studio:enable', admin.tenant.id, false)
        FeatureToggle.toggle_tenant(ModelFieldDependency::FT_MODEL_FIELD_DEPENDENCIES, admin.tenant.id, false)
      end

      it 'can update existing rules, create new rules and destroy removed rules' do
        old_ids = data_set.row_level_permission_rules.pluck(:id)
        ModelFieldDependencies::MaintainerService.new(tenant_id: admin.tenant.id,
                                                      dependant_class: RowLevelPermissionRule,)
                                                 .upsert(RowLevelPermissionRule.all.pluck(:id))

        expect do
          post :row_level_permission_rules, params: params, format: :json
          assert_success_response!
        end.not_to(change { data_set.reload.row_level_permission_rules.count })

        expect(rule.reload.condition.as_json.rsk.slice(:operator, :values)).to eq(new_condition)
        expect(RowLevelPermissionRule.where(id: rule2.id).exists?).to eq(false)

        new_ids = data_set.row_level_permission_rules.pluck(:id)
        expect(ModelFieldDependency.exists?(dependant_type: RowLevelPermissionRule.name,
                                            dependant_id: old_ids - new_ids,)).to be(false)
        expect(ModelFieldDependency.where(dependant_type: RowLevelPermissionRule.name,
                                          dependant_id: new_ids,).count).to be(new_ids.count)

        res = JSON.parse(response.body)
        expect(res['row_level_permission_rules']).to eq(
          [
            {
              'id' => rule.id,
              'data_set_id' => data_set.id,
              'condition' => { 'modifier' => nil, 'operator' => 'is', 'values' => ['ahihi'], 'options' => nil },
              'field_path' => {
                'data_set_id' => nil,
                'field_name' => 'name',
                'joins_path' => nil,
                'model_id' => query_data_model.id,
                'is_metric' => false,
              },
              'creator_id' => admin.id,
              'updater_id' => admin.id,
              'from_aml' => false,
            },
            {
              'id' => RowLevelPermissionRule.last.id,
              'data_set_id' => data_set.id,
              'condition' => { 'modifier' => nil, 'operator' => 'is_not', 'values' => ['con_meo'], 'options' => nil },
              'field_path' => {
                'data_set_id' => nil,
                'field_name' => 'name',
                'joins_path' => nil,
                'model_id' => query_data_model.id,
                'is_metric' => false,
              },
              'creator_id' => admin.id,
              'updater_id' => nil,
              'from_aml' => false,
            },
          ],
        )
      end
    end

    describe 'GET' do
      let(:condition) do
        {
          operator: 'is',
          values: ['a'],
        }
      end
      let(:field_path) do
        {
          model_id: query_data_model.id,
          field_name: 'name',
        }
      end
      let(:data_set2) { FactoryBot.create(:data_set, root_model_id: nil) }
      let!(:rule1) do
        FactoryBot.create(
          :row_level_permission_rule,
          data_set_id: data_set.id,
          condition: condition,
          field_path: field_path,
        )
      end
      let!(:rule2) do
        FactoryBot.create(
          :row_level_permission_rule,
          data_set_id: data_set2.id,
          condition: condition,
          field_path: field_path,
        )
      end
      let!(:rule3) do
        FactoryBot.create(
          :row_level_permission_rule,
          data_set_id: data_set.id,
          condition: condition,
          field_path: field_path,
        )
      end
      let(:params) do
        {
          id: data_set.id,
        }
      end

      it 'can list all rules of a dataset' do
        get :row_level_permission_rules, params: params, format: :json
        assert_success_response!

        res = JSON.parse(response.body)
        expect(res['row_level_permission_rules']).to eq(
          [
            {
              'id' => rule1.id,
              'data_set_id' => data_set.id,
              'condition' => { 'modifier' => nil, 'operator' => 'is', 'values' => ['a'], 'options' => nil },
              'field_path' => {
                'data_set_id' => nil,
                'field_name' => 'name',
                'joins_path' => nil,
                'model_id' => query_data_model.id,
                'is_metric' => false,
              },
              'creator_id' => admin.id,
              'updater_id' => nil,
              'from_aml' => false,
            },
            {
              'id' => rule3.id,
              'data_set_id' => data_set.id,
              'condition' => { 'modifier' => nil, 'operator' => 'is', 'values' => ['a'], 'options' => nil },
              'field_path' => {
                'data_set_id' => nil,
                'field_name' => 'name',
                'joins_path' => nil,
                'model_id' => query_data_model.id,
                'is_metric' => false,
              },
              'creator_id' => admin.id,
              'updater_id' => nil,
              'from_aml' => false,
            },
          ],
        )
      end

      describe 'aml rlp' do
        include_context 'aml_studio_embed_row_level_permissions'

        let(:dataset) { DataSet.find_by!(uname: 'ecommerce') }

        def add_test_rlp
          field_path = { model_id: 'users', field_name: 'country_id', data_set_id: dataset.id }
          RowLevelPermissionRule.new(
            field_path: DataModeling::Values::FieldPath.coerce_from_hash(field_path),
            condition: DataModeling::Values::Condition.new(operator: 'is', values: [1]),
            data_set_id: dataset.id,
            tenant_id: user.tenant_id,
            from_aml: false,
          ).save!
        end

        it 'list all aml rlp' do |ex|
          add_test_rlp

          get :row_level_permission_rules, params: { id: dataset.id }
          assert_success_response!

          res = JSON.parse(response.body)
          SnapshotTest.test!(res['row_level_permission_rules'], rspec_example: ex,
                                                                snapshot_name: 'fetch_rlp_controller_with_aml.snap',)
        end
      end
    end
  end

  describe '#create_adhoc_field' do
    include_context 'data_explore_ctx'

    before do
      FeatureToggle.toggle_global('data_sets:custom_expression', true)
    end

    let(:data_set) do
      _data_set = create(
        :data_set,
        root_model_id: orders_model.id, # dummy root
      )

      _data_set.data_models = [
        orders_model,
        products_model,
        users_model,
        cities_model,
        merchants_model,
        countries_model,
        categories_model,
      ]

      create(
        :join_config,
        data_set: _data_set,
        join: merchants_cities_join,
        active: false,
        tenant_id: _data_set.tenant_id,
      )

      _data_set.reload
    end

    it "make an adhoc field that doesn't reference any model" do
      custom_field = {
        name: 'test',
        sql: "'down down down down'",
      }
      post :create_custom_field, format: :json, params: { id: data_set.id, field: custom_field }

      res = JSON.parse(response.body)

      expected_field = {
        aggregation_type: 'custom',
        _external_field: false,
        is_custom: true,
        is_custom_measure: true,
        is_literal: false,
        is_hidden: false,
        label: 'Test',
        name: 'test',
        order: 9999,
        sql: "'down down down down'",
        syntax: "aml",
        type: "text",
        tags: [],
      }
      expect(res.rsk).to eq({ field: expected_field, status: 'success' })

      custom_field = {
        name: 'test',
        sql: '12 / (3 * 2)',
      }
      post :create_custom_field, format: :json, params: { id: data_set.id, field: custom_field }

      res = JSON.parse(response.body)

      expected_field = {
        aggregation_type: 'custom',
        _external_field: false,
        is_custom: true,
        is_custom_measure: true,
        is_literal: false,
        is_hidden: false,
        label: 'Test',
        name: 'test',
        order: 9999,
        sql: "12 / (3 * 2)",
        syntax: "aml",
        type: "number",
        tags: [],
      }
      expect(res.rsk).to eq({ field: expected_field, status: 'success' })
    end

    it 'can create measure' do
      custom_field = {
        name: 'test',
        sql: 'count(users.id)',
      }
      post :create_custom_field, format: :json, params: { id: data_set.id, field: custom_field }

      res = JSON.parse(response.body)

      expected_field = {
        _external_field: false,
        aggregation_type: 'custom',
        is_custom: true,
        is_custom_measure: true,
        is_literal: false,
        is_hidden: false,
        label: 'Test',
        name: 'test',
        order: 9999,
        sql: "count(users.id)",
        syntax: "aml",
        type: "number",
        tags: [],
      }
      expect(res.rsk).to eq({ field: expected_field, status: 'success' })

      custom_field = {
        name: 'test',
        sql: 'sum(data_modeling_products.id) / count(users.id)',
      }
      post :create_custom_field, format: :json, params: { id: data_set.id, field: custom_field }

      res = JSON.parse(response.body)

      expected_field = {
        _external_field: false,
        aggregation_type: 'custom',
        is_custom: true,
        is_custom_measure: true,
        is_literal: false,
        is_hidden: false,
        label: 'Test',
        name: 'test',
        order: 9999,
        sql: "sum(data_modeling_products.id) / count(users.id)",
        syntax: "aml",
        type: "number",
        tags: [],
      }
      expect(res.rsk).to eq({ field: expected_field, status: 'success' })
    end

    it 'can create dimension reference multiple fields' do
      custom_field = {
        name: 'test',
        sql: "concat(data_modeling_products.name, ' ', users.full_name)",
      }
      post :create_custom_field, format: :json, params: { id: data_set.id, field: custom_field }

      res = JSON.parse(response.body)

      expected_field = {
        _external_field: false,
        is_custom: true,
        is_custom_measure: false,
        is_literal: false,
        is_hidden: false,
        label: 'Test',
        name: 'test',
        order: 9999,
        sql: "concat(data_modeling_products.name, ' ', users.full_name)",
        syntax: "aml",
        type: "text",
        tags: [],
      }
      expect(res.rsk).to eq({ field: expected_field, status: 'success' })
    end
  end

  describe '#fetch' do
    context 'with AML datasets' do
      include_context 'aml_studio_dataset'

      let(:cached_datasets_map_params) { {} }
      let(:params) do
        {
          id: data_set.id,
          **cached_datasets_map_params,
        }
      end
      let(:prod_aml_objects_service) do
        DataSets::AmlObjectsService.new(
          current_user: current_user,
          force_mode: AmlStudio::WorkingEnvironment::Mode::Production,
        )
      end
      let!(:committed_version) do
        prod_aml_objects_service.version(data_set)
      end
      let!(:committed_version_timestamp) do
        prod_aml_objects_service.version_timestamp(data_set)
      end
      let(:aml_dataset) do
        prod_aml_objects_service.fetch(data_set)
      end

      before do
        expect(committed_version).to be_present
        expect(committed_version_timestamp).to be_present
      end

      it 'serializes the dataset properly' do
        get :show, params: params, format: :json
        assert_success_response!

        data = JSON.parse(response.body)
        expect(data['id']).to eq(data_set.id)
        expect(data['data_models'].size).to eq(4)
        expect(data['related_joins'].size).to eq(2)
        expect(data['join_configs'].size).to eq(2)
        expect(data['version']).to eq(committed_version)
        expect(data['version_timestamp']).to eq(committed_version_timestamp)
        expect(data['is_frontend_cached']).to eq(false)
        expect(data['permissions']).to be_present
      end

      context 'cached_datasets_map is present but current version is not cached' do
        let(:cached_datasets_map_params) do
          {
            "cached_datasets_map[ahihi][#{data_set.id}]" => 1,
            "cached_datasets_map[#{committed_version}][#{data_set2.id}]" => 1,
          }
        end

        it 'serializes the dataset normally' do
          get :show, params: params, format: :json
          assert_success_response!

          data = JSON.parse(response.body)
          expect(data['id']).to eq(data_set.id)
          expect(data['label']).to eq(aml_dataset.label)
          expect(data['data_models'].size).to eq(4)
          expect(data['related_joins'].size).to eq(2)
          expect(data['join_configs'].size).to eq(2)
          expect(data['version']).to eq(committed_version)
          expect(data['version_timestamp']).to eq(committed_version_timestamp)
          expect(data['is_frontend_cached']).to eq(false)
          expect(data['permissions']).to be_present
        end
      end

      context 'dataset is cached' do
        let(:cached_datasets_map_params) do
          {
            "cached_datasets_map[#{committed_version}][#{data_set.id}]" => 1,
          }
        end

        it 'serializes the metadata only' do
          get :show, params: params, format: :json
          assert_success_response!

          data = JSON.parse(response.body)
          expect(data['id']).to eq(data_set.id)
          expect(data['label']).to eq(aml_dataset.label)
          expect(data['data_models']).to be_blank
          expect(data['related_joins']).to be_blank
          expect(data['join_configs']).to be_blank
          expect(data['version']).to eq(committed_version)
          expect(data['version_timestamp']).to eq(committed_version_timestamp)
          expect(data['is_frontend_cached']).to eq(true)
          expect(data['permissions']).to eq({ 'can_crud' => true, 'can_explore' => true, 'can_read' => true,
                                              'can_export_data' => true, })
        end

        describe 'POST fetch' do
          let(:cached_datasets_map_params) do
            {
              'cached_datasets_map' => {
                committed_version.to_s => [data_set.id],
              },
            }
          end

          it 'works' do
            post :fetch, format: :json, params: params
            assert_success_response!
          end

          it 'raise error if not json format' do
            post :fetch, params: params
            assert_response_status!(422)
          end
        end

        context 'but user is in dev mode' do
          before do
            # enable global dev mode
            # NOTE: must do this **after** setting the `committed_version`
            current_user.update!(dev_mode_enabled: true)
            FeatureToggle.toggle_global(AmlStudio::FetchObjects::FT_ALLOW_DEV_MODE_GLOBALLY, true)
          end

          it 'serializes the dataset freshly' do
            get :show, params: params, format: :json
            assert_success_response!

            data = JSON.parse(response.body)
            expect(data['id']).to eq(data_set.id)
            expect(data['data_models'].size).to eq(4)
            expect(data['related_joins'].size).to eq(2)
            expect(data['join_configs'].size).to eq(2)
            expect(data['version']).to be_nil
            expect(data['version_timestamp']).to be_nil
            expect(data['is_frontend_cached']).to eq(false)
            expect(data['permissions']).to be_present
          end
        end
      end
    end
  end
end
