# frozen_string_literal: true
# typed: false

require 'rails_helper'

RSpec.describe EmbedLinks::Services::BuildActionBasedPermissionFromHash do
  context 'ssbi is disabled' do
    include_context 'embed_portal_context'

    it 'returns default action based permission' do
      embed_payload = {
        embed_org_id: 1,
        embed_user_id: 1,
        permissions: {
          org_workspace_role: 'viewer',
          enable_personal_workspace: false,
        },
      }

      expect(described_class.new(user: public_user, embed_payload: embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
        ),
      )
    end
  end

  context 'ssbi is enabled' do
    include_context 'embed_portal_context'

    before do
      FeatureToggle.toggle_global(EmbedPortal::FT_EMBED_PORTAL_SSBI_ENABLED, true)
      FeatureToggle.toggle_global(AmlStudio::Project::FT_EXPLICIT_GIT, true)
    end

    it 'returns default action based permission when embed payload is empty' do
      empty_embed_payload = {
        permissions: {},
      }
      expect(described_class.new(user: public_user, embed_payload: empty_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
        ),
      )
    end

    it 'returns default action based permission for base embed payload' do
      base_embed_payload = {
        embed_org_id: nil,
        embed_user_id: nil,
        permissions: {
          org_workspace_role: 'no_access',
          enable_personal_workspace: false,
        },
      }
      expect(described_class.new(user: public_user, embed_payload: base_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
        ),
      )
    end

    it 'returns action based permission for viewer embed payload' do
      viewer_embed_payload = {
        embed_user_id: 1,
        embed_org_id: 1,
        permissions: {
          org_workspace_role: 'viewer',
          enable_personal_workspace: false,
        },
      }
      expect(described_class.new(user: public_user, embed_payload: viewer_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: false,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
        ),
      )
    end

    it 'returns action based permission for editor embed payload' do
      editor_embed_payload = {
        embed_user_id: 1,
        embed_org_id: 1,
        permissions: {
          org_workspace_role: 'editor',
          enable_personal_workspace: false,
        },
      }
      expect(described_class.new(user: public_user, embed_payload: editor_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: true,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: false,
            can_edit: false,
          ),
        ),
      )
    end

    it 'returns action based permission for viewer embed payload with personal workspace' do
      viewer_embed_payload = {
        embed_user_id: 1,
        embed_org_id: 1,
        permissions: {
          org_workspace_role: 'viewer',
          enable_personal_workspace: true,
        },
      }
      expect(described_class.new(user: public_user, embed_payload: viewer_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: false,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: true,
          ),
        ),
      )
    end

    it 'returns action based permission for editor embed payload with personal workspace' do
      editor_embed_payload = {
        embed_user_id: 1,
        embed_org_id: 1,
        permissions: {
          org_workspace_role: 'editor',
          enable_personal_workspace: true,
        },
      }
      expect(described_class.new(user: public_user, embed_payload: editor_embed_payload, project: project).call).to eq(
        DataModeling::Values::PermissionRules::ActionBased.new(
          org_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: true,
          ),
          personal_workspace: DataModeling::Values::PermissionRules::ActionBased::WorkspacePermission.new(
            can_view: true,
            can_edit: true,
          ),
        ),
      )
    end

    it 'return error when role is invalid' do
      viewer_embed_payload = {
        embed_user_id: 1,
        permissions: {
          org_workspace_role: 'invalid',
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: viewer_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)
    end

    it 'return error when role is viewer/editor and embed org id or embed user id is not present' do
      viewer_embed_payload = {
        permissions: {
          org_workspace_role: 'viewer',
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: viewer_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)

      editor_embed_payload = {
        permissions: {
          org_workspace_role: 'editor',
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: editor_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)

      editor_embed_payload = {
        client_user_id: 1,
        permissions: {
          org_workspace_role: 'viewer',
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: editor_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)

      editor_embed_payload = {
        embed_org_id: 1,
        permissions: {
          org_workspace_role: 'editor',
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: editor_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)
    end

    it 'return error when enable_personal_workspace is true and embed user id is not present' do
      editor_embed_payload = {
        permissions: {
          enable_personal_workspace: true,
        },
      }
      expect do
        described_class.new(user: public_user,
                            embed_payload: editor_embed_payload, project: project,).call
      end.to raise_error(Holistics::InvalidParameter)
    end
  end
end
