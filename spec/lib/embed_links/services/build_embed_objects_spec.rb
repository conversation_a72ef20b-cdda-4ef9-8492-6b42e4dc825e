# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmbedLinks::Services::BuildEmbedObjects do
  include_context 'embed_portal_context'

  it 'returns embed objects with default settings' do
    embed_payload = {
      settings: {
        allow_to_export_raw_data: false,
        allow_to_change_dashboard_timezone: false,
        default_timezone: nil,
      },
    }

    expect(described_class.new(embed_portal: embed_portal, embed_payload: embed_payload).call).to eq([
      EmbedLinks::Values::EmbedDashboardConfigs.new(
        id: embed_dashboard.id,
        settings: EmbedLinks::Values::EmbedDashboardConfigs::Settings.new(
          enable_export_data: false,
          allow_to_change_timezone: false,
          default_timezone: nil,
        ),
      ),
      EmbedLinks::Values::EmbedDatasetConfigs.new(
        id: dataset_products.id,
        settings: EmbedLinks::Values::EmbedDatasetConfigs::Settings.new(
          enable_export_data: false,
        ),
      ),
    ])
  end

  it 'returns embed objects with custom settings' do
    embed_payload = {
      settings: {
        allow_to_export_raw_data: true,
        allow_to_change_dashboard_timezone: true,
        default_timezone: 'UTC',
      },
    }

    expect(described_class.new(embed_portal: embed_portal, embed_payload: embed_payload).call).to eq([
      EmbedLinks::Values::EmbedDashboardConfigs.new(
        id: embed_dashboard.id,
        settings: EmbedLinks::Values::EmbedDashboardConfigs::Settings.new(
          enable_export_data: true,
          allow_to_change_timezone: true,
          default_timezone: 'UTC',
        ),
      ),
      EmbedLinks::Values::EmbedDatasetConfigs.new(
        id: dataset_products.id,
        settings: EmbedLinks::Values::EmbedDatasetConfigs::Settings.new(
          enable_export_data: true,
        ),
      ),
    ])
  end
end
