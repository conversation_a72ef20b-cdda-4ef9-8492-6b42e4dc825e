# typed: false

require 'rails_helper'

describe ImageExporters::DashboardsV4::Tab do
  let(:admin) { get_test_admin }
  let(:viewer) { get_test_admin }
  let(:job) { FactoryBot.create(:job, user: viewer) }
  let(:widget_viz_conditions_map) do
    {}
  end
  let(:permission_rules) { {} }

  context 'dashboard v4' do
    include_context 'dashboards_v4'
    include_context 'dynamic_dashboard'

    let(:views) { tab_layout }

    describe '#dashboard_definition_with_filter' do
      subject do
        described_class.new(
          dashboard_v4,
          job: job,
          widget_viz_conditions_map: widget_viz_conditions_map,
          permission_rules: permission_rules,
          query_processing_timezone: '',
          tab_unames: ['tab_2'],
        )
      end

      let(:widget_viz_conditions_map) do
        {
          DashboardsV4::Services::MaterializedBlockOperations.generate_block_id(dashboard, 'w1') => [
            Viz::Values::VizCondition.coerce_from(
              condition: { operator: 'is', values: ['alice'], modifier: nil, options: nil },
              path_hash: { field_name: 'name' },
              aggregation: nil,
              label: 'Name',
              dynamic_filter_id: "#{dashboard_v4.id}:filter_1",
            ),
            Viz::Values::VizCondition.coerce_from(
              condition: { operator: 'is', values: ['alice'], modifier: nil, options: nil },
              path_hash: { field_name: 'name' },
              aggregation: nil,
              label: 'Name',
              dynamic_filter_id: "#{dashboard_v4.id}:p1",
            ),
            Viz::Values::VizCondition.coerce_from(
              condition: { operator: 'is', values: ['alice1'], modifier: nil, options: nil },
              path_hash: { field_name: 'name' },
              aggregation: nil,
              label: 'Name',
              dynamic_filter_id: "#{dashboard_v4.id}:d1",
            ),
          ],
        }
      end

      it 'return correct' do |ex|
        result = subject.send(:dashboard_definition_with_filter)
        SnapshotTest.test!(result, rspec_example: ex,
                                   snapshot_name: 'dashboard_definition_with_filter_2.snap',)
      end
    end

    describe '#generate_results_data' do
      include_context 'simple_image_dashboard_v4'

      let(:blocks) { [viz_block_table, viz_block_table_2, filter_block, text_block] }
      let(:views) { tab_layout }

      # Data model products
      # data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
      #     ( VALUES
      #       ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
      #       ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
      #       ('egg', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
      #       ('bread', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z')
      #     )
      let(:tab_unames) { ['tab2'] }
      subject do
        described_class.new(
          dashboard, job: job,
          widget_viz_conditions_map: widget_viz_conditions_map,
          permission_rules: permission_rules,
          query_processing_timezone: '',
          tab_unames: tab_unames,
        )
      end


      let(:user) { get_test_admin }
      let(:expected_results_data) do
        JSON.parse(fixture_read_file('image_exporters/dashboard_renderer_input_v4.json')).rsk
      end
      let(:params) { { query_processing_timezone: '', tab_unames: tab_unames } }
      let(:evb) { dashboard.viz_blocks[0] }
      let(:block_count) { dashboard.tab_layout.blocks(tab_unames).count }

      def check_correct_renderer_input_dashboard_v4(subject, dashboard)
        with_fake_async_context(job) do
          widget_cache_key_map = Dashboards::ExecReportWidgets.new(dashboard: dashboard, user: user,
                                                                   job: job, params: params,).call(bust_cache: true)
          widget_results = subject.send(:generate_results_data, widget_cache_key_map)

          expect(widget_results.size).to eq block_count
          expect(widget_results.all? { |w| w[:result].present? }).to be true
          expect(widget_results.any? { |w| w[:error].present? }).to be false

          widget_results = widget_results.sort_by { |w| w[:widget] }

          widget_results.each_with_index do |result_data, i|
            result = result_data[:result]
            expected_result = expected_results_data.find { |w| w[:widget] == result_data[:widget] }[:result]
            test_image_exporter_renderer_input_result!(result, expected_result)
          end
        end
      end

      it 'can generate data for every widget' do
        check_correct_renderer_input_dashboard_v4(subject, dashboard)
      end

      context 'widget are not accessible' do
        let(:viewer) { get_test_analyst }

        let(:widget_viz_conditions_map) do
          {
            DashboardsV4::Services::MaterializedBlockOperations.generate_block_id(dashboard, 'v1') => [
              Viz::Values::VizCondition.coerce_from(
                condition: { operator: 'greater_than', values: ['3'], modifier: nil, options: nil },
                path_hash: { field_name: 'price', model_id: query_products_model.id, data_set_id: query_data_set.id },
                aggregation: nil,
                label: 'Price',
                dynamic_filter_id: "#{dashboard.id}:text_filter",
              ),
            ],
          }
        end

        it 'does not crash and includes the error in the result data' do
          with_fake_async_context(job) do
            widget_cache_key_map = Dashboards::ExecReportWidgets.new(dashboard: dashboard, user: user, job: job,
                                                                     params: params,).call(bust_cache: true)
            widget_results = subject.send(:generate_results_data, widget_cache_key_map)
            expect(widget_results.size).to eq block_count

            permission_required_widget = widget_results.detect { |w| w[:error] =~ /permission required/i }
            expect(permission_required_widget).to be_present
            expect(permission_required_widget[:result]).not_to be_present

            other_widget = widget_results.detect { |w| w != permission_required_widget }
            expect(other_widget).to be_present
            expect(other_widget[:result]).to be_present
          end
        end
      end

      context 'one widget has error' do
        it 'does not crash and includes the error in the result data' do
          with_fake_async_context(job) do
            expect_any_instance_of(DashboardsV4::Services::Runner::Explore).to receive(:run).and_return(Result.new(nil,
                                                                                                                   Holistics::QueryError.new('syntax error'),))
            widget_cache_key_map = Dashboards::ExecReportWidgets.new(dashboard: dashboard, user: user,
                                                                     job: job, params: params,).call(bust_cache: true, fail_fast: false)
            widget_results = subject.send(:generate_results_data, widget_cache_key_map)
            with_fake_async_context(job) do
              widget_results = subject.send(:generate_results_data, widget_cache_key_map)
              expect(widget_results.size).to eq block_count

              permission_required_widget = widget_results.detect { |w| w[:error] =~ /syntax error/i }
              expect(permission_required_widget).to be_present
              expect(permission_required_widget[:result]).not_to be_present

              other_widget = widget_results.detect { |w| w != permission_required_widget }
              expect(other_widget).to be_present
              expect(other_widget[:result]).to be_present
            end
          end
        end
      end

      context 'applying viz conditions' do
        let(:expected_results_data) do
          JSON.parse(fixture_read_file('image_exporters/dashboard_with_filters_renderer_input_v4.json')).rsk
        end
        let(:viz_block) { dashboard.viz_blocks[0] }
        let(:widget_viz_conditions_map) do
          {
            DashboardsV4::Services::MaterializedBlockOperations.generate_block_id(dashboard, 'v1') => [
              Viz::Values::VizCondition.coerce_from(
                condition: { operator: 'greater_than', values: ['3'], modifier: nil, options: nil },
                path_hash: { field_name: 'price', model_id: query_products_model.id, data_set_id: query_data_set.id },
                aggregation: nil,
                label: 'Price',
                dynamic_filter_id: "#{dashboard.id}:text_filter",
              ),
            ],
          }
        end

        it 'Can apply correctly' do
          check_correct_renderer_input_dashboard_v4(subject, dashboard)
        end

        context 'when using raw Viz Setting' do
          before do
            FeatureToggle.toggle_global(Viz::Constants::FT_EXPORT_RENDER_WITH_RAW_VIZ_SETTING, true)
          end

          context 'when Interactive Control is disabled' do
            let(:expected_results_data) do
              JSON.parse(fixture_read_file('image_exporters/dashboard_with_viz_conditions_ignored_renderer_input_v4.json')).rsk
            end

            it 'does not include viz conditions in renderer input viz setting' do
              check_correct_renderer_input_dashboard_v4(subject, dashboard)
            end
          end

          context 'when Interactive Control is enabled' do
            let(:expected_results_data) do
              JSON.parse(fixture_read_file('image_exporters/dashboard_with_viz_conditions_renderer_input_v4.json')).rsk
            end

            before do
              FeatureToggle.toggle_global(DynamicFilter::FT_INTERACTIVE_CONTROL_POP, true)
            end

            it 'maps the viz conditions into renderer input viz setting correctly' do
              check_correct_renderer_input_dashboard_v4(subject, dashboard)
            end
          end
        end
      end

      describe 'applying permission rules' do
        let!(:user_attribute) { create(:user_attribute, name: 'price', attribute_type: 'text') }

        let(:rule_condition) do
          {
            operator: DataModeling::Constants::ConditionSchemes::MATCHES_USER_ATTRIBUTE.operator,
            values: ['price'],
          }
        end

        let(:rule_field_path) do
          {
            model_id: query_products_model.id,
            field_name: 'price',
          }
        end

        let!(:row_level_permission_rule) do
          create(
            :row_level_permission_rule,
            data_set_id: query_data_set.id,
            condition: rule_condition,
            field_path: rule_field_path,
          )
        end

        let(:permission_rules) do
          DataModeling::Values::PermissionRules::Object.new(
            row_based: [
              DataModeling::Values::PermissionRules::RowBased.new(
                field_path: DataModeling::Values::FieldPath.new(
                  field_name: 'price', model_id: query_products_model.id, data_set_id: query_data_set.id,
                ),
                condition: Modeling::Values::Condition.new(
                  operator: 'greater_than', values: ['3'], modifier: nil, options: nil,
                ),
              ),
            ],
          )
        end

        let(:expected_results_data) do
          JSON.parse(fixture_read_file('image_exporters/dashboard_with_permission_rules_renderer_input_v4.json')).rsk
        end

        context 'analyst' do
          let(:viewer) { get_test_analyst }
          let!(:user_attribute_entry) do
            FactoryBot.create(:user_attribute_entry, user_attribute: user_attribute, subject: viewer, values: ['3'])
          end

          before do
            admin.share(viewer, :read, data_set.data_source)
          end

          it 'applies the permission rules correctly' do
            check_correct_renderer_input_dashboard_v4(subject, dashboard)
          end
        end

        context 'embed user' do
          let(:embed_link) do
            el = FactoryBot.create(:embed_link, source: dashboard, filter_ownerships: [],
                                                tenant: get_test_tenant,)
            el.set_public_user
            el.share_source
            el
          end
          let(:viewer) { embed_link.public_user }

          it 'applies the permission rules correctly' do
            check_correct_renderer_input_dashboard_v4(subject, dashboard)
          end
        end
      end

      context 'embed drillthrough' do
        before do
          FeatureToggle.toggle_global('drillthrough:enabled', true)
          FeatureToggle.toggle_global('dac:drillthrough', true)
        end

        include_context 'aml_studio_deployed' do
          let(:deploy_dashboard_v4) { true }
          let(:isolated_repo) { true }
          let(:project_fixture_folder_path) do
            'spec/fixtures/aml_repos/drillthroughs'
          end
        end

        let(:source_dashboard) do
          deploy_result # make sure deployment is already done
          Dashboard.find_by!(uname: 'source_dashboard')
        end

        let(:dest_dashboard) do
          deploy_result # make sure deployment is already done
          Dashboard.find_by!(uname: 'dest_dashboard')
        end

        let(:dashboard) {
          definition = dest_dashboard.definition
          dest_dashboard.definition = {
            **definition,
            'views' => [
                  {
                    'tabs' => [
                      {
                        'type' => 'CanvasLayout',
                        'label' => 'Untitled',
                        'theme' => nil,
                        'uname' => 'tab2',
                        'width' => 1920,
                        'blocks' => {
                          'f1' => {
                            'layer' => 4,
                            'position' => { 'h' => 100, 'w' => 420, 'x' => 20, 'y' => 20 },
                          },
                          'v1' => {
                            'layer' => 1,
                            'position' => { 'h' => 340, 'w' => 1160, 'x' => 20, 'y' => 150 },
                          },
                        },
                        'height' => 1960,
                        'mobile' => {
                          'mode' => 'auto',
                        },
                        'grid_size' => 20,
                        'default_zoom' => nil,
                      },
                      {
                        'type' => 'CanvasLayout',
                        'label' => 'Untitled',
                        'theme' => nil,
                        'uname' => 'tab3',
                        'width' => 1200,
                        'blocks' => {
                          'v1' => {
                            'layer' => 1,
                            'position' => { 'h' => 460, 'w' => 1160, 'x' => 20, 'y' => 20 },
                          },
                        },
                        'height' => 800,
                        'mobile' => { 'mode' => 'auto' },
                        'grid_size' => 20,
                        'default_zoom' => nil,
                      },
                    ],
                    'type' => 'TabLayout',
                    'label' => 'View 1',
                    'uname' => 'view_1',
                  },
                ]
          }
          dest_dashboard.save!
          dest_dashboard
        }

        let(:embed_link) do
          el = FactoryBot.create(:embed_link, source: source_dashboard, filter_ownerships: [], version: 4, user: admin,
                                              tenant: admin.tenant,)
          el.set_public_user
          el.share_source
          el
        end
        let(:embed_drillthroughs) do
          { dest_dashboard.id => {} }
        end
        let(:embed_token) do
          jwt_encode(embed_link.secret_key, { drillthroughs: embed_drillthroughs }, Time.now.to_i + 3000)
        end
        let(:job) do
          embed_link.public_user.embed_v3_configs = EmbedLinks::Services::BuildEmbedConfigs.new(user: embed_link.public_user).call({ EmbedLink::EMBED_TOKEN_PARAM.to_s => embed_token })
          create(:job, user: embed_link.public_user, tenant: admin.tenant)
        end


        shared_examples 'export_image_with_mebed_dashboard_drillthourgh' do
          it 'works' do
            with_fake_async_context(job) do
              widget_cache_key_map = Dashboards::ExecReportWidgets.new(
                dashboard: dashboard, user: embed_link.public_user, job: job, params: params,
              ).call(bust_cache: true)

              widget_results = subject.send(:generate_results_data, widget_cache_key_map)
                expect(widget_results.size).to be dashboard.tab_layout.blocks(['tab2']).count
              expect(widget_results.all? { |w| w[:result].present? }).to be true
              expect(widget_results.any? { |w| w[:error].present? }).to be false
            end
          end

          context 'without embed drillthroughs' do
            let(:embed_drillthroughs) do
              {}
            end

            it 'renders permission denied' do
              with_fake_async_context(job) do
                widget_cache_key_map = Dashboards::ExecReportWidgets.new(
                  dashboard: dashboard, user: embed_link.public_user, job: job, params: params,
                ).call(bust_cache: true)

                widget_results = subject.send(:generate_results_data, widget_cache_key_map)
                expect(widget_results.size).to be dashboard.tab_layout.blocks(['tab2']).count
                expect(widget_results.all? { |w| w[:result].present? }).to be false
                expect(widget_results.map { |w| w[:error] }.uniq).to eq(['Permission Required'])
              end
            end
          end
        end

        context 'use embed_v3_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
          end

          it_behaves_like 'export_image_with_mebed_dashboard_drillthourgh'
        end

        context 'use embed_portal_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
          end

          it_behaves_like 'export_image_with_mebed_dashboard_drillthourgh'
        end
      end
    end
  end
end
