# typed: ignore

require 'rails_helper'

# NOTE: the timezone case is tested in spec/models/concerns/dashboards/export_spec.rb
describe ImageExporters::Services::WidgetResultGenerator do

  let(:admin) { get_test_admin }
  let(:viewer) { get_test_admin }
  let(:job) { FactoryBot.create(:job, user: viewer) }
  let(:widget_viz_conditions_map) do
    {}
  end
  let(:permission_rules) { {} }

  include_context 'dashboards_v4'
  include_context 'dynamic_dashboard'

  describe '#prepare_widget_data' do
    subject do
      described_class.new(
        widgets: dashboard_v4.viz_blocks,
        job: job,
        widget_viz_conditions_map: widget_viz_conditions_map,
        permission_rules: permission_rules,
        query_processing_timezone: '',
        check_widget_permission: true,
        should_build_table_options: false,
      )
    end

    it 'work with dashboard widget' do
      result = subject.send(:prepare_widget_data, dashboard.dashboard_widgets[0])
      expect(result[:title]).to eq(dashboard.dashboard_widgets[0].title)
    end

    it 'work with explorable block in dashboard v4' do
      result = subject.send(:prepare_widget_data, dashboard_v4.viz_blocks[0])
      expect(result).to eq(dashboard_v4.viz_blocks[0].uname)
    end
  end
end
