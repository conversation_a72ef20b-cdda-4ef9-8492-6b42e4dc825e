# typed: false
# frozen_string_literal: false

require 'rails_helper'

describe AmlStudio::PrWorkflow::EnablePrWorkflow do
  include_context 'aml_studio_explicit'
  let(:remote_project) { AmlStudio::Project.create!(name: 'Remote', tenant_id: admin.tenant_id) }
  let(:remote_repo) do
    remote_project.repositories.create!(
      is_production: true,
      owner_id: admin.id,
      tenant_id: remote_project.tenant_id,
    )
  end
  let(:client) { proj_repo.source }
  let(:remote_client) { remote_repo.source }

  before do
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, true)
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_PR_WORKFLOW, project.tenant_id, true)
    # fake the remote url to local repo
    AmlStudio::Values::GitProvider.any_instance.stub(:normalize_ssh_url).and_return(remote_repo.storage_path)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:token_expiration_date).and_return(Time.zone.parse('1970-01-01T00:00:00Z'))
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_return(123_456)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:webhook_exists?).and_return(true)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:validate_read_pr_permission!).and_return(true)
    AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:delete_webhook).and_return(true)

    # Setup external git connection
    remote_client.init(bare: true)
    AmlStudio::ExternalGit::SetupConnection
      .new(project: project)
      .call('ssh://**************/owner/repo')
  end

  context 'Github' do
    it 'test pr workflow operations' do
      AmlStudio::ExternalGit::DisableExternalIntegration.new.call(project)

      project.reload
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result.error_message).to eq('Project is not integrated with external')

      AmlStudio::ExternalGit::SetupConnection
        .new(project: project)
        .call('ssh://**************/owner/repo')

      project.reload
      # enable pr workflow successfully
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Success)
      expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('abcxyz')
      project.reload
      expect(project.enabled_pr_workflow?).to be(true)
      expect(project.external_git_integration.webhook_secret).not_to be_nil
      old_webhook_secret = project.external_git_integration.webhook_secret

      # then disable it
      AmlStudio::PrWorkflow::DisablePrWorkflow.new(project: project).call
      # token still stays the same
      expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('abcxyz')
      expect(project.enabled_pr_workflow?).to be(false)
      # webhook remains unchanged
      expect(project.external_git_integration.webhook_secret).to eq(old_webhook_secret)

      project.reload

      # update the token while it is disabled
      result = AmlStudio::PrWorkflow::UpdateToken.new(project: project, token: 'abcdef').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Success)
      expect(result.message).to eq('Token validated')
      expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('abcdef')

      # webhook changes since we update the
      expect(project.external_git_integration.webhook_secret).not_to eq(old_webhook_secret)

      # now disable it with delete token
      AmlStudio::PrWorkflow::DisablePrWorkflow.new(project: project, is_delete_token: true).call
      expect(project.external_git_integration.token).to be_nil
      expect(project.enabled_pr_workflow?).to be(false)
      expect(project.external_git_integration.webhook_secret).to be_nil

      expect_any_instance_of(AmlStudio::GitFlows::GitClient::GithubClient).to receive(:create_webhook)
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Success)
      expect(project.external_git_integration.webhook_secret).not_to be_nil
      expect(project.external_git_integration.webhook_secret).not_to eq(old_webhook_secret)
      expect(project.external_git_integration.webhook_id).to eq(123_456)
    end

    it 'raises error when PR workflow is already enabled' do
      AmlStudio::Project.any_instance.stub(:git_repo_name).and_return('test_repo')
      AmlStudio::Project.any_instance.stub(:repo_owner).and_return('test_owner')
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Success)
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Error)
      expect(result.error_message).to eq('PR workflow has already enabled on this project')
    end

    it 'raises error when no token provided and no existing token' do
      # Create a mock integration without using the removed method
      integration = AmlStudio::ExternalGitIntegration.create!(
        provider: 'github',
        repo_url: 'https://github.com/owner/repo',
      )
      project.update!(external_git_integration: integration)

      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project).call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Error)
      expect(result.error_message).to eq('Please provide the token to continue')
    end

    it 'raises error when webhook creation fails' do
      AmlStudio::Project.any_instance.stub(:git_repo_name).and_return('test_repo')
      AmlStudio::Project.any_instance.stub(:repo_owner).and_return('test_owner')
      # Create initial external_git_integration with token
      AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:webhook_exists?).and_return(false)
      AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_raise(Holistics::InvalidOperation.new('Token does not have permission to write webhooks'))
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abc').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Error)
      expect(result.error_message).to eq('Token does not have permission to write webhooks')

      AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_raise(StandardError.new('Something went wrong internal'))
      expect do
        result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abc').call
      end.to raise_error(StandardError, 'Something went wrong internal')
    end

    it 'raises error when missing required permissions' do
      AmlStudio::Project.any_instance.stub(:git_repo_name).and_return('test_repo')
      AmlStudio::Project.any_instance.stub(:repo_owner).and_return('test_owner')
      # Stub the validate_read_pr_permission! to raise the InvalidOperation exception
      AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:validate_read_pr_permission!).and_raise(
        Holistics::InvalidOperation.new('Token does not have permission to read pull requests'),
      )
      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Error)
      expect(result.error_message).to eq('Token does not have permission to read pull requests')
    end

    it 'updates token expiration date when enabling PR workflow' do
      AmlStudio::Project.any_instance.stub(:git_repo_name).and_return('test_repo')
      AmlStudio::Project.any_instance.stub(:repo_owner).and_return('test_owner')
      expiration_date = Time.zone.parse('2024-12-31T00:00:00Z')
      AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:token_expiration_date).and_return(expiration_date)

      result = AmlStudio::PrWorkflow::EnablePrWorkflow.new(project: project, token: 'abcxyz').call
      expect(result).to be_a(AmlStudio::PrWorkflow::Response::JobResult::Success)
      project.reload
      expect(project.external_git_integration.token_expiration_date).to eq(expiration_date)
    end
  end
end
