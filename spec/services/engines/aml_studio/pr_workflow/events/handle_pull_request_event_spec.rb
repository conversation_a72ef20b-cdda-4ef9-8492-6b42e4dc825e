# typed: false
# frozen_string_literal: false

require 'rails_helper'

describe AmlStudio::PrWorkflow::Events::HandlePullRequestEvent do
  include_context 'aml_studio_explicit'
  let(:remote_project) { AmlStudio::Project.create!(name: 'Remote', tenant_id: admin.tenant_id) }
  let(:pull_request) do
    AmlStudio::Values::PullRequest.new(
      number: 123,
      title: 'Fix all the bugs',
      merge_commit_sha: nil,
      head_branch: 'fix-bugs',
      base_branch: 'master',
      state: AmlStudio::Values::PrStatus::Open,
      updated_at: Time.current,
      merged_at: Time.current,
      url: 'https://github.com/owner/repo/pull/123',
    )
  end
  let(:merged_pull_request) do
    AmlStudio::Values::PullRequest.new(
      number: 123,
      title: 'Fix all the bugs',
      merge_commit_sha: 'abc123',
      head_branch: 'fix-bugs',
      base_branch: 'master',
      state: AmlStudio::Values::PrStatus::Merged,
      updated_at: Time.current,
      merged_at: Time.current,
      url: 'https://github.com/owner/repo/pull/123',
    )
  end
  let(:remote_repo) do
    remote_project.repositories.create!(
      is_production: true,
      owner_id: admin.id,
      tenant_id: remote_project.tenant_id,
    )
  end
  let(:client) { proj_repo.source }
  let(:remote_client) { remote_repo.source }
  let(:parsed_data_open_pr) do
    {
      pr_data: pull_request,
      action_type: 'opened',
      open_pr_related_event: true,
      merged_pr_event: false,
    }
  end
  let(:parsed_data_merged_pr) do
    {
      pr_data: merged_pull_request,
      action_type: 'closed',
      open_pr_related_event: false,
      merged_pr_event: true,
    }
  end
  let(:parsed_data_both_events) do
    {
      pr_data: merged_pull_request,
      action_type: 'closed',
      open_pr_related_event: true,
      merged_pr_event: true,
    }
  end

  before do
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, true)
    AmlStudio::Values::GitProvider.any_instance.stub(:normalize_ssh_url).and_return(remote_repo.storage_path)
    remote_client.init(bare: true)

    AmlStudio::ExternalGit::SetupConnection
      .new(project: project)
      .call('ssh://***********/owner/repo')

    AmlStudio::Values::GitProvider.any_instance.stub(:provider).and_return('GitHub')

    # Set production branch to match PR base branch
    allow(project).to receive(:production_branch).and_return('master')
  end

  describe 'handle pull request events' do
    it 'creates pull request event' do
      handler = AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
        parsed_data: parsed_data_open_pr,
        project: project,
      )
      handler.call

      events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )
      event = events.first

      expect(event).not_to be_nil
      expect(event.event_type).to eq(AmlStudio::ProjectEvent::EventType::PullRequest)
      expect(event.tenant_id).to eq(project.tenant_id)
      expect(event.project_id).to eq(project.id)
      expect(event.branch_name).to eq('fix-bugs')

      trigger_data = event.data['trigger']
      expect(trigger_data['type']).to eq('pull_request')
      expect(trigger_data['pull_request']['state']).to eq('open')
      expect(trigger_data['pull_request']['title']).to eq('Fix all the bugs')
    end

    it 'creates deploy event for merged pull request' do
      job = double('job', id: 'job-123')
      allow_any_instance_of(described_class).to receive(:submit_deploy_job).and_return(job)

      handler = AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
        parsed_data: parsed_data_merged_pr,
        project: project,
      )
      handler.call

      events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::Deploy],
      )
      event = events.first

      expect(event).not_to be_nil
      expect(event.event_type).to eq(AmlStudio::ProjectEvent::EventType::Deploy)
      expect(event.tenant_id).to eq(project.tenant_id)
      expect(event.project_id).to eq(project.id)
      expect(event.branch_name).to eq('fix-bugs')

      trigger_data = event.data['trigger']
      expect(trigger_data['type']).to eq('pull_request')
      expect(trigger_data['job_id']).to eq('job-123')
      expect(trigger_data['pull_request']['state']).to eq('merged')
    end

    it 'handles both events for merged pull request' do
      job = double('job', id: 'job-123')
      allow_any_instance_of(described_class).to receive(:submit_deploy_job).and_return(job)

      handler = AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
        parsed_data: parsed_data_both_events,
        project: project,
      )
      handler.call

      pr_events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )
      pr_event = pr_events.first
      expect(pr_event).not_to be_nil
      expect(pr_event.tenant_id).to eq(project.tenant_id)
      expect(pr_event.project_id).to eq(project.id)
      expect(pr_event.branch_name).to eq('fix-bugs')
      expect(pr_event.data['trigger']['type']).to eq('pull_request')

      deploy_events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::Deploy],
      )
      deploy_event = deploy_events.first
      expect(deploy_event).not_to be_nil
      expect(deploy_event.tenant_id).to eq(project.tenant_id)
      expect(deploy_event.project_id).to eq(project.id)
      expect(deploy_event.branch_name).to eq('fix-bugs')
      expect(deploy_event.data['trigger']['job_id']).to eq('job-123')
    end

    it 'does not create events when action type is not in whitelist' do
      data_with_invalid_action = parsed_data_open_pr.merge(action_type: 'unknown_action')

      handler = AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
        parsed_data: data_with_invalid_action,
        project: project,
      )
      handler.call

      events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )
      event = events.first

      expect(event).to be_nil
    end

    it 'does not create events when base branch is not production branch' do
      allow(project).to receive(:production_branch).and_return('main')

      handler = AmlStudio::PrWorkflow::Events::HandlePullRequestEvent.new(
        parsed_data: parsed_data_open_pr,
        project: project,
      )
      handler.call

      events = AmlStudio::ProjectEvent.fetch_latest_events_by_types(
        project_id: project.id,
        branch_name: 'fix-bugs',
        event_types: [AmlStudio::ProjectEvent::EventType::PullRequest],
      )
      event = events.first

      expect(event).to be_nil
    end
  end
end
