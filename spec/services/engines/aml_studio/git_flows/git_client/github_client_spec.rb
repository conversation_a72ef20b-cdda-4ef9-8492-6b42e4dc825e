require 'rails_helper'

RSpec.describe AmlStudio::GitFlows::GitClient::GithubClient do
  let(:token) { 'test_token' }
  let(:repo_owner) { 'test_owner' }
  let(:repo_name) { 'test_repo' }
  let(:domain) { 'github.com' }
  let(:remote_url) { "**************:#{repo_owner}/#{repo_name}.git" }
  let(:git_provider) do
    AmlStudio::ExternalGit::ParseRemoteUrl.new.call(remote_url)
  end
  let(:client) do
    AmlStudio::GitFlows::GitClient::ClientBuilder.new(git_provider: git_provider, token: token).call
  end

  describe '#validate_read_pr_permission!' do
    context 'when token has permission' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:pull_requests).and_return([])
      end

      it 'returns nil' do
        expect(client.validate_read_pr_permission!).to be(T::Private::Types::Void::VOID)
      end
    end

    context 'when token does not have permission' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:pull_requests)
          .and_raise(Octokit::Forbidden)
      end

      it 'raises InvalidOperation error' do
        expect { client.validate_read_pr_permission! }
          .to raise_error(Holistics::InvalidOperation, 'Token does not have permission to read pull requests')
      end
    end
  end

  describe '#token_expiration_date' do
    let(:expiration_date) { Time.current + 1.day }
    let(:headers) do
      { 'github-authentication-token-expiration' => expiration_date.to_s }
    end
    let(:response) { double(headers: headers) }

    context 'when token is valid' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:last_response).and_return(nil)
        allow_any_instance_of(Octokit::Client).to receive(:user)
        allow_any_instance_of(Octokit::Client).to receive(:last_response).and_return(response)
      end

      it 'returns expiration date' do
        expect(client.token_expiration_date.to_i).to eq(expiration_date.to_i)
      end
    end

    context 'when token is invalid' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:user)
          .and_raise(Octokit::Forbidden)
      end

      it 'raises InvalidOperation error' do
        expect { client.token_expiration_date }
          .to raise_error(Holistics::InvalidOperation, 'Invalid token')
      end
    end
  end

  describe '#fetch_latest_open_pr' do
    let(:head_branch) { 'feature_branch' }
    let(:base_branch) { 'main' }
    let(:pr_data) do
      {
        merge_commit_sha: 'sha123',
        title: 'Test PR',
        merged_at: Time.current,
        number: 1,
        updated_at: Time.current,
        head: { ref: head_branch },
        base: { ref: base_branch },
        html_url: 'https://github.com/test/test/pull/1',
      }
    end

    context 'when PR exists' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:pull_requests)
          .with(
            "#{repo_owner}/#{repo_name}",
            state: 'open',
            head: "#{repo_owner}:#{head_branch}",
            base: base_branch,
            per_page: 1,
            sort: 'created',
            direction: 'desc',
          )
          .and_return([pr_data])
      end

      it 'returns PR data' do
        result = client.fetch_latest_open_pr(head_branch, base_branch)
        expect(result).to be_a(AmlStudio::Values::PullRequest)
        expect(result.number).to eq(pr_data[:number])
        expect(result.head_branch).to eq(head_branch)
        expect(result.base_branch).to eq(base_branch)
      end
    end

    context 'when PR does not exist' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:pull_requests)
          .and_return([])
      end

      it 'returns nil' do
        expect(client.fetch_latest_open_pr(head_branch, base_branch)).to be_nil
      end
    end
  end

  describe '#create_webhook' do
    let(:webhook_url) { 'https://test.com/webhook' }
    let(:webhook_secret) { 'secret123' }
    let(:webhook_id) { 1 }
    let(:webhook_params) do
      {
        url: webhook_url,
        content_type: 'json',
        secret: webhook_secret,
        insecure_ssl: 0,
      }
    end

    context 'when successful' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:create_hook)
          .with(
            "#{repo_owner}/#{repo_name}",
            'web',
            webhook_params,
            events: ['pull_request', 'push'],
            active: true,
          )
          .and_return({ id: webhook_id })
      end

      it 'returns webhook id' do
        expect(client.create_webhook(webhook_url, webhook_secret)).to eq(webhook_id)
      end
    end

    context 'when forbidden' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:create_hook)
          .and_raise(Octokit::Forbidden)
      end

      it 'raises InvalidOperation error' do
        expect { client.create_webhook(webhook_url, webhook_secret) }
          .to raise_error(Holistics::InvalidOperation, 'Token does not have permission to write webhooks')
      end
    end
  end

  describe '#delete_webhook' do
    let(:webhook_id) { 1 }

    context 'when webhook exists' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:remove_hook)
          .with("#{repo_owner}/#{repo_name}", webhook_id)
          .and_return(true)
      end

      it 'returns true' do
        expect(client.delete_webhook(webhook_id)).to be true
      end
    end

    context 'when forbidden' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:remove_hook)
          .and_raise(Octokit::Forbidden)
      end

      it 'raises InvalidOperation error' do
        expect { client.delete_webhook(webhook_id) }
          .to raise_error(Holistics::InvalidOperation, 'Token does not have permission to write webhooks')
      end
    end
  end

  describe '#webhook_exists?' do
    let(:webhook_id) { 1 }

    context 'when webhook exists' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:hook)
          .with("#{repo_owner}/#{repo_name}", webhook_id)
          .and_return(double(present?: true))
      end

      it 'returns true' do
        expect(client.webhook_exists?(webhook_id)).to be true
      end
    end

    context 'when webhook does not exist' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:hook)
          .and_raise(Octokit::NotFound)
      end

      it 'returns false' do
        expect(client.webhook_exists?(webhook_id)).to be false
      end
    end

    context 'when forbidden' do
      before do
        allow_any_instance_of(Octokit::Client).to receive(:hook)
          .and_raise(Octokit::Forbidden)
      end

      it 'raises InvalidOperation error' do
        expect { client.webhook_exists?(webhook_id) }
          .to raise_error(Holistics::InvalidOperation, 'Token does not have permission to read webhooks')
      end
    end
  end

  describe '.validate_webhook_signature' do
    let(:payload) { 'test_payload' }
    let(:webhook_secret) { 'test_secret' }
    let(:signature) do
      "sha256=#{OpenSSL::HMAC.hexdigest('sha256', webhook_secret, payload)}"
    end

    context 'when signature is valid' do
      it 'returns true' do
        expect(described_class.validate_webhook_signature(payload, signature, webhook_secret)).to be true
      end
    end

    context 'when signature is invalid' do
      let(:invalid_signature) { 'sha256=invalid' }

      it 'returns false' do
        expect(described_class.validate_webhook_signature(payload, invalid_signature, webhook_secret)).to be false
      end
    end
  end

  describe '.parse_pull_request_event' do
    let(:current_time) { Time.current }
    let(:opened_payload) do
      {
        'action' => 'opened',
        'number' => 1,
        'pull_request' => {
          'url' => 'https://api.github.com/repos/holistics/aml_studio/pulls/1',
          'id' => 1_234_567_890,
          'node_id' => 'PR_kwDOA1234567890',
          'html_url' => 'https://github.com/holistics/aml_studio/pull/1',
          'diff_url' => 'https://github.com/holistics/aml_studio/pull/1.diff',
          'patch_url' => 'https://github.com/holistics/aml_studio/pull/1.patch',
          'issue_url' => 'https://api.github.com/repos/holistics/aml_studio/issues/1',
          'number' => 1,
          'state' => 'open',
          'locked' => false,
          'title' => 'Feature: Add new analytics dashboard',
          'user' => {
            'login' => 'toanle3008',
            'id' => 123_456,
            'type' => 'User',
            'site_admin' => false,
          },
          'body' => "This PR adds a new analytics dashboard with the following features:\n- Real-time data visualization\n- Custom report generation\n- Export functionality",
          'created_at' => current_time.iso8601,
          'updated_at' => current_time.iso8601,
          'closed_at' => nil,
          'merged_at' => nil,
          'merge_commit_sha' => 'abc123def456ghi789',
          'assignee' => nil,
          'assignees' => [],
          'requested_reviewers' => [],
          'requested_teams' => [],
          'labels' => [],
          'milestone' => nil,
          'draft' => false,
          'commits_url' => 'https://api.github.com/repos/holistics/aml_studio/pulls/1/commits',
          'review_comments_url' => 'https://api.github.com/repos/holistics/aml_studio/pulls/1/comments',
          'review_comment_url' => 'https://api.github.com/repos/holistics/aml_studio/pulls/comments{/number}',
          'comments_url' => 'https://api.github.com/repos/holistics/aml_studio/issues/1/comments',
          'statuses_url' => 'https://api.github.com/repos/holistics/aml_studio/statuses/abc123def456ghi789',
          'head' => {
            'label' => 'holistics:feature/analytics-dashboard',
            'ref' => 'feature/analytics-dashboard',
            'sha' => 'abc123def456ghi789',
            'user' => {
              'login' => 'holistics',
              'id' => 654_321,
              'type' => 'Organization',
            },
            'repo' => {
              'id' => 987_654,
              'name' => 'aml_studio',
              'full_name' => 'holistics/aml_studio',
              'private' => true,
            },
          },
          'base' => {
            'label' => 'holistics:main',
            'ref' => 'main',
            'sha' => 'def456ghi789abc123',
            'user' => {
              'login' => 'holistics',
              'id' => 654_321,
              'type' => 'Organization',
            },
            'repo' => {
              'id' => 987_654,
              'name' => 'aml_studio',
              'full_name' => 'holistics/aml_studio',
              'private' => true,
            },
          },
          'merged' => false,
          'mergeable' => true,
          'rebaseable' => true,
          'mergeable_state' => 'clean',
          'merged_by' => nil,
          'comments' => 0,
          'review_comments' => 0,
          'maintainer_can_modify' => true,
          'commits' => 3,
          'additions' => 150,
          'deletions' => 30,
          'changed_files' => 5,
        },
        'repository' => {
          'id' => 987_654,
          'name' => 'aml_studio',
          'full_name' => 'holistics/aml_studio',
          'private' => true,
          'owner' => {
            'login' => 'holistics',
            'id' => 654_321,
            'type' => 'Organization',
          },
          'html_url' => 'https://github.com/holistics/aml_studio',
          'description' => 'AML Studio Repository',
          'fork' => false,
          'created_at' => current_time.iso8601,
          'updated_at' => current_time.iso8601,
          'pushed_at' => current_time.iso8601,
          'git_url' => 'git://github.com/holistics/aml_studio.git',
          'ssh_url' => '**************:holistics/aml_studio.git',
          'clone_url' => 'https://github.com/holistics/aml_studio.git',
          'default_branch' => 'main',
        },
        'sender' => {
          'login' => 'toanle3008',
          'id' => 123_456,
          'type' => 'User',
          'site_admin' => false,
        },
      }
    end

    let(:merged_time) { current_time + 1.hour }
    let(:merged_payload) do
      opened_payload.deep_merge(
        'action' => 'closed',
        'pull_request' => {
          'state' => 'closed',
          'merged' => true,
          'merged_at' => merged_time.iso8601,
          'merged_by' => {
            'login' => 'reviewer1',
            'id' => 789_012,
            'type' => 'User',
            'site_admin' => false,
          },
        },
      )
    end

    let(:draft_payload) do
      opened_payload.deep_merge(
        'action' => 'converted_to_draft',
        'pull_request' => {
          'draft' => true,
        },
      )
    end

    context 'when PR is opened' do
      it 'returns correct data' do
        result = described_class.parse_pull_request_event(opened_payload)

        expect(result[:action_type]).to eq('opened')
        expect(result[:pr_data]).to be_a(AmlStudio::Values::PullRequest)
        expect(result[:pr_data].number).to eq(1)
        expect(result[:pr_data].title).to eq('Feature: Add new analytics dashboard')
        expect(result[:pr_data].head_branch).to eq('feature/analytics-dashboard')
        expect(result[:pr_data].base_branch).to eq('main')
        expect(result[:pr_data].state).to eq(AmlStudio::Values::PrStatus::Open)
        expect(result[:pr_data].merge_commit_sha).to eq('abc123def456ghi789')
        expect(result[:pr_data].url).to eq('https://github.com/holistics/aml_studio/pull/1')
        expect(result[:pr_data].merged_at).to be_nil
        expect(result[:open_pr_related_event]).to be true
        expect(result[:merged_pr_event]).to be false
      end
    end

    context 'when PR is merged' do
      it 'returns correct data' do
        result = described_class.parse_pull_request_event(merged_payload)

        expect(result[:action_type]).to eq('closed')
        expect(result[:pr_data].state).to eq(AmlStudio::Values::PrStatus::Merged)
        expect(result[:pr_data].merged_at.to_i).to eq(merged_time.to_i)
        expect(result[:open_pr_related_event]).to be true
        expect(result[:merged_pr_event]).to be true
      end
    end

    context 'when PR is converted to draft' do
      it 'returns correct data' do
        result = described_class.parse_pull_request_event(draft_payload)

        expect(result[:action_type]).to eq('converted_to_draft')
        expect(result[:pr_data].state).to eq(AmlStudio::Values::PrStatus::Draft)
        expect(result[:pr_data].merged_at).to be_nil
        expect(result[:open_pr_related_event]).to be true
        expect(result[:merged_pr_event]).to be false
      end
    end
  end

  describe '.parse_repo_urls_from_payload' do
    let(:payload) do
      {
        'repository' => {
          'id' => 987_654,
          'name' => 'aml_studio',
          'full_name' => 'holistics/aml_studio',
          'private' => true,
          'html_url' => 'https://github.com/holistics/aml_studio',
          'git_url' => 'git://github.com/holistics/aml_studio.git',
          'ssh_url' => '**************:holistics/aml_studio.git',
          'clone_url' => 'https://github.com/holistics/aml_studio.git',
        },
      }
    end

    it 'returns correct urls' do
      result = described_class.parse_repo_urls_from_payload(payload)
      expect(result[:html_url]).to eq('https://github.com/holistics/aml_studio')
      expect(result[:ssh_url]).to eq('**************:holistics/aml_studio.git')
    end
  end
end
