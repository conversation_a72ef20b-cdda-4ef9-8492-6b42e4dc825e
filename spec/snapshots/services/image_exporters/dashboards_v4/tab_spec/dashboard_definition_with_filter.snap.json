{"uname": "my_dashboard_v4", "title": "Sample pro dashboard", "blocks": [{"type": "TextBlock", "uname": "text_1", "content": "\t\t    # Heading 1\n\t\t\n\t\t\t\t### Heading 3\n\t\t    \n\t\t    This is a **dashboard** with _canvas layout_", "settings": {"hide_controls": true}}, {"type": "FilterBlock", "label": "", "uname": "filter_1", "filter": {"label": "filter_1", "contraints": {}, "input_type": null, "filter_type": "string", "is_shareable": false, "filter_source": {"field_path": {"model_id": 1, "field_name": "name"}, "data_set_id": 13, "source_type": "DmFieldFilterSource"}, "default_condition": {"operator": "is", "modifier": null, "values": ["alice"], "options": null}}, "settings": {"hide_controls": true, "hide_labels": false, "enable_drillthrough": true}}, {"pop": {"label": "Pop", "input_type": null, "constraints": {}, "filter_type": "pop", "is_shareable": false, "filter_source": {"source_type": "ManualFilterSource", "manual_options": []}, "default_condition": {"operator": "is", "modifier": null, "values": ["alice"], "options": null}}, "type": "PopBlock", "label": "Pop", "uname": "p1", "settings": {"hide_label": false, "hide_controls": false}, "description": null}, {"type": "DateDrillBlock", "label": "Date Drill", "uname": "d1", "settings": {"hide_label": false, "hide_controls": false}, "date_drill": {"label": "Date Drill", "input_type": null, "constraints": {}, "filter_type": "date_drill", "is_shareable": false, "filter_source": {"source_type": "ManualFilterSource", "manual_options": []}, "default_condition": {"operator": "is", "modifier": null, "values": ["alice1"], "options": null}}, "description": null}, {"viz": {"dataset_id": 13, "viz_setting": {"fields": {"series": {}, "x_axis": {"type": "text", "path_hash": {"model_id": 1, "field_name": "name"}}, "y_axes": [{"columns": [{"type": "number", "path_hash": {"model_id": 1, "field_name": "name"}, "aggregation": "sum", "series_settings": {"palette_id": -3, "series_hash": {}, "series_type": "auto"}}]}]}, "settings": {"legend": {"enabled": true, "alignment": "bottom"}, "others": {"always_display_points": false, "include_empty_children_rows": false}, "x_axis": {}, "y_axes": [{"max": null, "min": null, "align": "left", "title": null, "scale_type": "linear", "stack_type": "normal", "stack_series": false, "group_long_tail": false, "show_data_label": false, "show_group_total": false, "show_stack_total": false, "show_series_percentage": false}]}, "viz_type": "bar_chart", "adhoc_fields": []}}, "type": "VizBlock", "label": "", "uname": "w1", "description": null, "settings": {"hide_controls": true, "hide_labels": false}}], "views": [{"tabs": [{"type": "CanvasLayout", "label": "Untitled", "theme": null, "uname": "tab2", "width": 1920, "blocks": {"text_1": {"layer": 4, "position": {"h": 100, "w": 420, "x": 20, "y": 20}}, "filter_1": {"layer": 1, "position": {"h": 340, "w": 1160, "x": 20, "y": 150}}, "p1": {"layer": 1, "position": {"h": 340, "w": 1160, "x": 20, "y": 550}}, "d1": {"layer": 1, "position": {"h": 340, "w": 1160, "x": 20, "y": 550}}, "w1": {"layer": 1, "position": {"h": 340, "w": 1160, "x": 20, "y": 550}}}, "height": 1960, "mobile": {"mode": "auto"}, "grid_size": 20, "default_zoom": null}, {"type": "CanvasLayout", "label": "Untitled", "theme": null, "uname": "tab3", "width": 1200, "blocks": {"w1": {"layer": 1, "position": {"h": 460, "w": 1160, "x": 20, "y": 20}}}, "height": 800, "mobile": {"mode": "auto"}, "grid_size": 20, "default_zoom": null}], "type": "TabLayout", "label": "View 1", "uname": "view_1"}], "description": "This is a sample dashboard", "interactions": [{"to": "w1", "from": "text_filter", "type": "FilterInteraction", "field_path": {"field_name": "status", "model_id": "orders"}}, {"to": "w2", "from": "product_cat", "type": "FilterInteraction"}, {"to": "w2", "from": "w1", "type": "CrossFilterInteraction"}]}