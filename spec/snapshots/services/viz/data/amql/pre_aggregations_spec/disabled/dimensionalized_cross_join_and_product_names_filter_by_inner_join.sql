WITH "aql__t4" AS (
  -- Metrics:
  --   count(demo_order_items.order_id)
  SELECT
    COUNT("demo_order_items"."order_id") AS "count_demo_order_items->order_id"
  FROM
    "demo"."order_items" "demo_order_items"
), "aql__t7" AS (
  SELECT
    "demo_products"."Id" AS "demo_products->id"
  FROM
    demo.products "demo_products"
    LEFT JOIN "demo"."order_items" "demo_order_items" ON "demo_products"."Id" = "demo_order_items"."product_id"
    CROSS JOIN "aql__t4"
    LEFT JOIN "demo"."orders" "demo_orders" ON "demo_order_items"."order_id" = "demo_orders"."id"
    LEFT JOIN "demo"."users" "demo_users" ON "demo_orders"."user_id" = "demo_users"."id"
  WHERE
    "demo_users"."raw_EMAIL" = 'asd'
  GROUP BY
    1
), "aql__t5" AS (
  -- Explore for cells
  -- Dimensions:
  --   demo_products.order_count
  -- Metrics:
  --   sum(demo_products, demo_products.price)
  SELECT
    "aql__t4"."count_demo_order_items->order_id" AS "dp_oc_dfd667",
    SUM("demo_products"."price") AS "s_dp_p_8f4186"
  FROM
    demo.products "demo_products"
    INNER JOIN "aql__t7" ON "demo_products"."Id" = "aql__t7"."demo_products->id"
    CROSS JOIN "aql__t4"
  GROUP BY
    1
)
SELECT
  "aql__t5"."dp_oc_dfd667" AS "dp_oc_dfd667",
  "aql__t5"."s_dp_p_8f4186" AS "s_dp_p_8f4186"
FROM
  "aql__t5"
WHERE
  "aql__t5"."s_dp_p_8f4186" = 1.0
