/* [Aggregate Awareness pre-processing]
   - aggregated_order_statuses is invalid (`pa_status_measure` uses "custom" aggregation but `demo_orders.status` is not a measure) */
-- [Aggregate Awareness - missed] (neither aggregated nor grouped)
WITH "aql__t4" AS (
  -- Metrics:
  --   count(demo_order_items.order_id)
  /* [Aggregate Awareness - Hit!]
     - Not using aggregated_product_price_by_name (field miss: "count_demo_order_items->order_id" - demo_order_items.order_id | count)
     - Not using aggregated_product_price_by_id (field miss: "count_demo_order_items->order_id" - demo_order_items.order_id | count)
     + Using aggregated_orders (in place of demo_order_items) */
  SELECT
    SUM("aggregated_orders"."pa_count") AS "count_demo_order_items->order_id"
  FROM
    "persisted"."pa_aggregated_orders" "aggregated_orders"
), "aql__t7" AS (
  /* [Aggregate Awareness - missed]
     - Not using aggregated_product_price_by_name (containing unprocessible JOINs)
     - Not using aggregated_product_price_by_id (containing unprocessible JOINs)
     - Not using aggregated_orders (containing unprocessible JOINs)
     - Not using aggregated_orders_by_user (containing unprocessible JOINs)
     - Not using aggregated_order_by_merchant_id (containing unprocessible JOINs)
     - Not using aggregated_order_by_product_id (containing unprocessible JOINs) */
  SELECT
    "demo_products"."Id" AS "demo_products->id"
  FROM
    demo.products "demo_products"
    LEFT JOIN "demo"."order_items" "demo_order_items" ON "demo_products"."Id" = "demo_order_items"."product_id"
    CROSS JOIN "aql__t4"
    LEFT JOIN "demo"."orders" "demo_orders" ON "demo_order_items"."order_id" = "demo_orders"."id"
    LEFT JOIN "demo"."users" "demo_users" ON "demo_orders"."user_id" = "demo_users"."id"
  WHERE
    "demo_users"."raw_EMAIL" = 'asd'
  GROUP BY
    1
), "aql__t5" AS (
  -- Explore for cells
  -- Dimensions:
  --   demo_products.order_count
  -- Metrics:
  --   sum(demo_products, demo_products.price)
  /* [Aggregate Awareness - Hit!]
     - Not using aggregated_product_price_by_name (field miss: "" - demo_products.id)
     + Using aggregated_product_price_by_id (in place of demo_products) */
  SELECT
    "aql__t4"."count_demo_order_items->order_id" AS "dp_oc_dfd667",
    SUM("aggregated_product_price_by_id"."pa_sum_of_demo_products_price") AS "s_dp_p_8f4186"
  FROM
    "persisted"."pa_aggregated_product_price_by_id" "aggregated_product_price_by_id"
    INNER JOIN "aql__t7" ON "aggregated_product_price_by_id"."pa_demo_products_id" = "aql__t7"."demo_products->id"
    CROSS JOIN "aql__t4"
  GROUP BY
    1
)
SELECT
  "aql__t5"."dp_oc_dfd667" AS "dp_oc_dfd667",
  "aql__t5"."s_dp_p_8f4186" AS "s_dp_p_8f4186"
FROM
  "aql__t5"
WHERE
  "aql__t5"."s_dp_p_8f4186" = 1.0
