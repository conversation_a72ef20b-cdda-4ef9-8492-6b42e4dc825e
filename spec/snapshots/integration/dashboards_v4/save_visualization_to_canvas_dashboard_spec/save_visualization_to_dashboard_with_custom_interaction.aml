Dashboard live_dashboard {
  title: 'Live'
  description: ''''''

  view: CanvasLayout {
    label: 'View 1'
    height: 840
    grid_size: 20
    block allowable_viz {
      position: pos(60, 180, 400, 300)
      layer: 1
    }
    block unallowable_viz {
      position: pos(640, 180, 400, 300)
      layer: 1
    }
    block allowable_filter {
      position: pos(80, 20, 300, 100)
    }
    block unallowable_filter {
      position: pos(660, 20, 300, 100)
    }
    block text {
      position: pos(80, 520, 380, 120)
      layer: 2
    }
  }

  theme: H.themes.vanilla
  block allowable_viz: VizBlock {
    label: 'allowable viz'
    viz: DataTable {
      dataset: dataset_users
      fields: [
        VizFieldFull {
          ref: ref('users', 'id')
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_number: true
        aggregate_awareness {
          enabled: true
          debug_comments: true
        }
      }
    }
  }
  block unallowable_viz: VizBlock {
    label: 'unallowable viz'
    viz: DataTable {
      dataset: dataset_products
      fields: [
        VizFieldFull {
          ref: ref('products', 'id')
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_number: true
        aggregate_awareness {
          enabled: true
          debug_comments: true
        }
      }
    }
  }
  block allowable_filter: FilterBlock {
    label: 'allowable filter block'
    type: 'field'
    source: FieldFilterSource {
      dataset: dataset_users
      field: ref('users', 'id')
    }
    default {
      operator: 'is'
      value: []
    }
  }
  block unallowable_filter: FilterBlock {
    label: 'unallowable filter block'
    type: 'field'
    source: FieldFilterSource {
      dataset: dataset_products
      field: ref('products', 'id')
    }
    default {
      operator: 'is'
      value: []
    }
  }
  block text: TextBlock {
    content: @md Text block;;
  }
  block v1: VizBlock {
    label: 'Viz block from dataset'
    viz: DataTable {
      dataset: dataset_users
      theme {

      }
      fields: [
        VizFieldFull {
          ref: ref('users', 'id')
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_number: true
      }
    }
  }
  interactions: [
    FilterInteraction {
      from: 'unallowable_filter'
      to: [
        CustomMapping {
          block: 'unallowable_viz'
          disabled: true
        }
      ]
    }
  ]
}