---
http_interactions:
- request:
    method: get
    uri: https://cloud.getdbt.com/api/v2/accounts/118469/runs/?job_definition_id=814627&limit=1&order_by=-finished_at&status=10
    body:
      encoding: ASCII-8BIT
      string: ''
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Host:
      - cloud.getdbt.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 07 May 2025 10:41:37 GMT
      Content-Type:
      - application/json
      Content-Length:
      - '1997'
      Connection:
      - close
      Server:
      - nginx
      Allow:
      - GET, POST, DELETE, HEAD, OPTIONS
      Vary:
      - Accept-Encoding, Cookie
      X-Frame-Options:
      - DENY
      - deny
      X-Content-Type-Options:
      - nosniff
      - nosniff
      Referrer-Policy:
      - same-origin
      Cross-Origin-Opener-Policy:
      - same-origin
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, DELETE, OPTIONS
      Access-Control-Allow-Headers:
      - Authorization, Content-Type
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      X-Request-Id:
      - de5e635b64dd80a5d8c50ccc3d8311fb
      X-Robots-Tag:
      - noindex
    body:
      encoding: UTF-8
      string: '{"status":{"code":200,"is_success":true,"user_message":"Success!","developer_message":""},"data":[{"id":*********,"trigger_id":*********,"account_id":118469,"environment_id":145338,"project_id":178551,"job_definition_id":814627,"status":10,"dbt_version":"1.3.0-latest","git_branch":"main","git_sha":"b0b8400880e80442153d97b86fe6a818948c9ab0","status_message":"","owner_thread_id":null,"executed_by_thread_id":"orc-dispatch-latest-x1-1-7466445c88-gth4h","deferring_run_id":null,"artifacts_saved":true,"artifact_s3_path":"prod/runs/*********/artifacts/target","has_docs_generated":false,"has_sources_generated":false,"notifications_sent":true,"blocked_by":[],"created_at":"2025-02-28
        08:07:33.961343+00:00","updated_at":"2025-02-28 08:08:49.657985+00:00","dequeued_at":"2025-02-28
        08:07:34.038714+00:00","started_at":"2025-02-28 08:07:35.013208+00:00","finished_at":"2025-02-28
        08:08:49.563271+00:00","last_checked_at":"2025-02-28 08:08:49.617624+00:00","last_heartbeat_at":"2025-02-28
        08:07:34.799408+00:00","should_start_at":"2025-02-28 08:07:33.961343+00:00","trigger":null,"job":null,"environment":null,"run_steps":[],"deprecation":null,"status_humanized":"Success","in_progress":false,"is_complete":true,"is_success":true,"is_error":false,"is_cancelled":false,"duration":"00:01:15","queued_duration":"00:00:01","run_duration":"00:01:14","duration_humanized":"1
        minute, 15 seconds","queued_duration_humanized":"1 second","run_duration_humanized":"1
        minute, 14 seconds","created_at_humanized":"2 months, 1 week ago","finished_at_humanized":"2
        months, 1 week ago","retrying_run_id":null,"can_retry":false,"retry_not_supported_reason":"RETRY_NOT_FAILED_RUN","job_id":814627,"is_running":null,"href":"https://cloud.getdbt.com/deploy/118469/projects/178551/runs/*********/","used_repo_cache":null}],"extra":{"filters":{"limit":1,"offset":0,"state":"all","account_id":118469,"job_definition_id":814627,"status":10},"order_by":"-finished_at","pagination":{"count":1,"total_count":1}},"error_code":null}'
  recorded_at: Wed, 07 May 2025 10:41:37 GMT
- request:
    method: get
    uri: https://cloud.getdbt.com/api/v2/accounts/118469/runs/?job_definition_id=167743&limit=1&order_by=-finished_at&status=10
    body:
      encoding: ASCII-8BIT
      string: ''
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Host:
      - cloud.getdbt.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 07 May 2025 10:41:40 GMT
      Content-Type:
      - application/json
      Content-Length:
      - '1993'
      Connection:
      - close
      Server:
      - nginx
      Allow:
      - GET, POST, DELETE, HEAD, OPTIONS
      Vary:
      - Accept-Encoding, Cookie
      X-Frame-Options:
      - DENY
      - deny
      X-Content-Type-Options:
      - nosniff
      - nosniff
      Referrer-Policy:
      - same-origin
      Cross-Origin-Opener-Policy:
      - same-origin
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, DELETE, OPTIONS
      Access-Control-Allow-Headers:
      - Authorization, Content-Type
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      X-Request-Id:
      - fc41af68e9f6bfe4c6be8b41b518a375
      X-Robots-Tag:
      - noindex
    body:
      encoding: UTF-8
      string: '{"status":{"code":200,"is_success":true,"user_message":"Success!","developer_message":""},"data":[{"id":*********,"trigger_id":*********,"account_id":118469,"environment_id":145338,"project_id":178551,"job_definition_id":167743,"status":10,"dbt_version":"1.3.0-latest","git_branch":"main","git_sha":"b0b8400880e80442153d97b86fe6a818948c9ab0","status_message":"","owner_thread_id":null,"executed_by_thread_id":"orc-dispatch-x1-1-797cdd8566-j5v4k","deferring_run_id":null,"artifacts_saved":true,"artifact_s3_path":"prod/runs/*********/artifacts/target","has_docs_generated":false,"has_sources_generated":false,"notifications_sent":true,"blocked_by":[],"created_at":"2025-02-28
        09:15:59.777003+00:00","updated_at":"2025-02-28 09:17:15.452884+00:00","dequeued_at":"2025-02-28
        09:15:59.843989+00:00","started_at":"2025-02-28 09:16:01.397739+00:00","finished_at":"2025-02-28
        09:17:15.338294+00:00","last_checked_at":"2025-02-28 09:17:15.397496+00:00","last_heartbeat_at":"2025-02-28
        09:16:01.149437+00:00","should_start_at":"2025-02-28 09:15:59.777003+00:00","trigger":null,"job":null,"environment":null,"run_steps":[],"deprecation":null,"status_humanized":"Success","in_progress":false,"is_complete":true,"is_success":true,"is_error":false,"is_cancelled":false,"duration":"00:01:15","queued_duration":"00:00:01","run_duration":"00:01:13","duration_humanized":"1
        minute, 15 seconds","queued_duration_humanized":"1 second","run_duration_humanized":"1
        minute, 13 seconds","created_at_humanized":"2 months, 1 week ago","finished_at_humanized":"2
        months, 1 week ago","retrying_run_id":null,"can_retry":false,"retry_not_supported_reason":"RETRY_NOT_FAILED_RUN","job_id":167743,"is_running":null,"href":"https://cloud.getdbt.com/deploy/118469/projects/178551/runs/*********/","used_repo_cache":null}],"extra":{"filters":{"limit":1,"offset":0,"state":"all","account_id":118469,"job_definition_id":167743,"status":10},"order_by":"-finished_at","pagination":{"count":1,"total_count":2173}},"error_code":null}'
  recorded_at: Wed, 07 May 2025 10:41:40 GMT
- request:
    method: get
    uri: https://cloud.getdbt.com/api/v2/accounts/118469/runs/*********/artifacts/manifest.json
    body:
      encoding: ASCII-8BIT
      string: ''
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Host:
      - cloud.getdbt.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 07 May 2025 10:41:48 GMT
      Content-Type:
      - text/html; charset=utf-8
      Content-Length:
      - '636948'
      Connection:
      - close
      Server:
      - nginx
      Content-Security-Policy:
      - default-src 'none'; form-action 'none'; frame-ancestors 'none'
      Allow:
      - GET, HEAD, OPTIONS
      Vary:
      - Accept-Encoding, Cookie
      X-Frame-Options:
      - DENY
      - deny
      X-Content-Type-Options:
      - nosniff
      - nosniff
      Referrer-Policy:
      - same-origin
      Cross-Origin-Opener-Policy:
      - same-origin
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, DELETE, OPTIONS
      Access-Control-Allow-Headers:
      - Authorization, Content-Type
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      X-Request-Id:
      - b82ed063135abb2168535b8cfb04208a
      X-Robots-Tag:
      - noindex
    body:
      encoding: UTF-8
      string: '{
  "metadata": {
    "dbt_schema_version": "https://schemas.getdbt.com/dbt/manifest/v4.json",
    "dbt_version": "1.0.0",
    "generated_at": "2021-09-15T10:25:27.475437Z",
    "invocation_id": "d363fd9c-a053-402e-b2f9-352eea6345e8",
    "env": {},
    "project_id": "ad62b91a4eaf9b7b0db62fedd3edbd93",
    "user_id": "ebdaed0a-6b7f-4dc1-a9fe-5ac64bccf281",
    "send_anonymous_usage_stats": true,
    "adapter_type": "bigquery"
  },
  "nodes": {},
  "sources": {},
  "macros": {},
  "docs": {},
  "exposures": {},
  "metrics": {},
  "selectors": {},
  "disabled": {},
  "parent_map": {},
  "child_map": {}
}'
  recorded_at: Wed, 07 May 2025 10:41:55 GMT
- request:
    method: get
    uri: https://cloud.getdbt.com/api/v2/accounts/118469/runs/*********/artifacts/manifest.json
    body:
      encoding: ASCII-8BIT
      string: ''
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Host:
      - cloud.getdbt.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 07 May 2025 10:41:56 GMT
      Content-Type:
      - text/html; charset=utf-8
      Content-Length:
      - '636948'
      Connection:
      - close
      Server:
      - nginx
      Content-Security-Policy:
      - default-src 'none'; form-action 'none'; frame-ancestors 'none'
      Allow:
      - GET, HEAD, OPTIONS
      Vary:
      - Accept-Encoding, Cookie
      X-Frame-Options:
      - DENY
      - deny
      X-Content-Type-Options:
      - nosniff
      - nosniff
      Referrer-Policy:
      - same-origin
      Cross-Origin-Opener-Policy:
      - same-origin
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, DELETE, OPTIONS
      Access-Control-Allow-Headers:
      - Authorization, Content-Type
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      X-Request-Id:
      - 50b66c86b8a5e7b63252bc62f0704841
      X-Robots-Tag:
      - noindex
    body:
      encoding: UTF-8
      string: '{
  "metadata": {
    "dbt_schema_version": "https://schemas.getdbt.com/dbt/manifest/v4.json",
    "dbt_version": "1.0.0",
    "generated_at": "2021-09-15T10:25:27.475437Z",
    "invocation_id": "d363fd9c-a053-402e-b2f9-352eea6345e8",
    "env": {},
    "project_id": "ad62b91a4eaf9b7b0db62fedd3edbd93",
    "user_id": "ebdaed0a-6b7f-4dc1-a9fe-5ac64bccf281",
    "send_anonymous_usage_stats": true,
    "adapter_type": "bigquery"
  },
  "nodes": {},
  "sources": {},
  "macros": {},
  "docs": {},
  "exposures": {},
  "metrics": {},
  "selectors": {},
  "disabled": {},
  "parent_map": {},
  "child_map": {}
}'
  recorded_at: Wed, 07 May 2025 10:41:58 GMT
recorded_with: VCR 6.2.0
