shared_context 'aml_studio_external_git' do
  include_context 'aml_studio_explicit'

  let(:remote_project) { AmlStudio::Project.create!(name: 'Remote', tenant_id: admin.tenant_id) }
  let(:remote_repo) do
    remote_project.repositories.create!(
      is_production: true,
      owner_id: admin.id,
      tenant_id: remote_project.tenant_id,
    )
  end
  let(:client) { proj_repo.source }
  let(:remote_client) { remote_repo.source }

  let(:admin_work_flow) { current_repo.work_flow(admin) }
  let(:admin_client) { current_repo.source }

  before do
    ThreadContext.reset(:web)
    ThreadContext.set(:aml_env, AmlStudio::WorkingEnvironment::Env::AmlStudio)
    deploy_flow.call
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, true)
    AmlStudio::Values::GitProvider.any_instance.stub(:normalize_ssh_url).and_return(remote_repo.storage_path)
  end

  after do
    FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXTERNAL_GIT, project.tenant_id, false)
  end

  def setup_external_git
    remote_client.init(bare: true)

    AmlStudio::ExternalGit::SetupConnection
      .new(project: project)
      .call('ssh://***********/owner/repo')
  end
end
