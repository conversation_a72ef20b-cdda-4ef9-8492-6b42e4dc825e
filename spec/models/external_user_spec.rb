# frozen_string_literal: true
# typed: false

require 'rails_helper'
describe ExternalUser do
  describe '#create_from_embed_user' do
    it 'returns the same user for concurrent case' do
      client_org_id = 'embed_org'
      embed_user_id = 'embed_user'

      external_users = []

      5.times.map do
        Thread.new do
          user = get_test_admin
          tenant = user.tenant

          ThreadContext.reset(:web)
          ThreadContext.set(:current_user, user)

          ActiveRecord::Base.transaction do
            external_users << described_class.create_from_client_user!(
              client_user_id: embed_user_id, client_org_id: client_org_id, tenant: tenant,
            )

            sleep 0.5
          end
        end
      end.each(&:join)

      expect(external_users.map(&:id).uniq).to eq([external_users.first.id])
      expect(external_users.map(&:external_org_id).uniq).to eq([external_users.first.external_org_id])
    end
  end
end
