GIT
  remote: https://github.com/holistics/creek.git
  revision: 130b6597e02234b805744546898ded83f4176258
  branch: gems/bump-http-5.2.0
  specs:
    creek (2.2.1)
      http (~> 5.2.0)
      nokogiri (>= 1.7.0)
      rubyzip (>= 1.0.0)

GIT
  remote: https://github.com/holistics/mysql2.git
  revision: a658ad9a5f99bb163a7e08eb546d7af94ac61814
  specs:
    mysql2 (0.5.6)

GIT
  remote: https://github.com/holistics/presto-client-ruby.git
  revision: ffc1339099c554803e5666fa082216b3661f9a65
  specs:
    presto-client (0.6.6)
      faraday (~> 1.0)
      faraday_middleware (~> 1.0)
      msgpack (>= 0.7.0)

GIT
  remote: https://github.com/holistics/prosopite
  revision: a196e60ac951d9e2d48be8855c1b0b27d25fa507
  branch: main
  specs:
    prosopite (0.0.1.pre.holistics)

GIT
  remote: https://github.com/jquery-ui-rails/jquery-ui-rails.git
  revision: 413265e81f790f795239e07e7e25e01429b2f18d
  tag: v7.0.0
  specs:
    jquery-ui-rails (7.0.0)
      railties (>= 3.2.16)

PATH
  remote: engines/aml_studio
  specs:
    aml_studio (0.2.0)
      active_model_serializers
      cancancan
      oj (~> 3.16)
      rails (~> *******)

PATH
  remote: gems/source_control
  specs:
    source_control (0.9.0)
      activesupport
      coding_utils (~> 1.0)
      dotenv
      http
      json_rpc_client (~> 0.4)
      logstash-logger
      oj
      puma
      rugged (= 1.7.2)
      sinatra (~> 2.2)
      sorbet-runtime

GEM
  remote: https://44ef5602:<EMAIL>/
  specs:
    sidekiq-pro (5.5.8)
      sidekiq (~> 6.0, >= 6.5.6)

GEM
  remote: https://rubygems.org/
  specs:
    ace-rails-ap (4.4)
    actioncable (********)
      actionpack (= ********)
      activesupport (= ********)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      mail (>= 2.7.1)
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activesupport (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (********)
      actionpack (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      nokogiri (>= 1.8.5)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.9.13)
      activemodel (>= 3.2)
      concurrent-ruby (~> 1.0)
    active_record_query_trace (1.8.2)
      activerecord (>= 6.0.0)
    activeadmin (3.2.2)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin_json_editor (0.0.10)
      ace-rails-ap
      railties (>= 3.0)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.6)
    activemodel (********)
      activesupport (= ********)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
    activerecord-import (1.0.6)
      activerecord (>= 3.2)
    activestorage (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activesupport (= ********)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    alba (3.4.0)
      ostruct (~> 0.6)
    anbt-sql-formatter (0.1.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.2)
    attr_encrypted (4.1.0)
      encryptor (~> 3.0.0)
    awesome_print (1.9.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.954.0)
    aws-sdk-athena (1.79.0)
      aws-sdk-core (~> 3, >= 3.188.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-core (3.201.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.156.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    batch_api (0.3.0)
      middleware
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.0)
    benchmark (0.4.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.8)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    brakeman (5.4.1)
    browser (5.3.1)
    bson (5.0.1)
    builder (3.3.0)
    byebug (11.1.3)
    cancancan (3.4.0)
    capybara (3.39.2)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    childprocess (3.0.0)
    chronic (0.10.2)
    chunky_png (1.4.0)
    claide (1.1.0)
    claide-plugins (0.9.2)
      cork
      nap
      open4 (~> 1.3)
    cli-ui (2.2.3)
    coderay (1.1.3)
    colored2 (3.1.2)
    colorize (0.8.1)
    combine_pdf (1.0.21)
      ruby-rc4 (>= 0.1.5)
    committee (5.4.0)
      json_schema (~> 0.14, >= 0.14.3)
      openapi_parser (~> 2.0)
      rack (>= 1.5, < 3.1)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    cork (0.3.0)
      colored2 (~> 3.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    crawler_detect (1.2.4)
      qonfig (>= 0.24)
    css_parser (1.17.1)
      addressable
    csv (3.3.0)
    danger (9.5.1)
      base64 (~> 0.2)
      claide (~> 1.0)
      claide-plugins (>= 0.9.2)
      colored2 (~> 3.1)
      cork (~> 0.1)
      faraday (>= 0.9.0, < 3.0)
      faraday-http-cache (~> 2.0)
      git (~> 1.13)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      octokit (>= 4.0)
      pstore (~> 0.1)
      terminal-table (>= 1, < 4)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    declarative-option (0.1.0)
    deprecation_toolkit (2.2.0)
      activesupport (>= 6.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-two-factor (4.1.0)
      activesupport (< 7.1)
      attr_encrypted (>= 1.3, < 5, != 2)
      devise (~> 4.0)
      railties (< 7.1)
      rotp (~> 6.0)
    diff-lcs (1.5.1)
    docile (1.4.0)
    dogstatsd-ruby (5.4.0)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    dry-cli (1.1.0)
    ed25519 (1.3.0)
    encryptor (3.0.0)
    erubi (1.13.0)
    event_stream_parser (1.0.0)
    execjs (2.9.1)
    extpp (0.1.1)
    factory_bot (6.4.5)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.3.1)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-http-cache (2.5.1)
      faraday (>= 0.8)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    fast_blank (1.0.1)
    fast_excel (0.5.0)
      ffi (> 1.9, < 2)
    ffi (1.16.3)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    fiddle (1.1.2)
    fixture_builder (0.5.3.rc2)
      activerecord (>= 2)
      activesupport (>= 2)
      hashdiff
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    gio2 (4.2.2)
      fiddle
      gobject-introspection (= 4.2.2)
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    glib2 (4.2.2)
      native-package-installer (>= 1.0.3)
      pkg-config (>= 1.3.5)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gobject-introspection (4.2.2)
      glib2 (= 4.2.2)
    google-apis-analytics_v3 (0.13.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-analyticsreporting_v4 (0.14.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-bigquery_v2 (0.61.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-core (0.11.3)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-drive_v3 (0.46.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-oauth2_v2 (0.15.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-people_v1 (0.36.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-sheets_v4 (0.26.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-cloud-bigquery (1.45.0)
      concurrent-ruby (~> 1.0)
      google-apis-bigquery_v2 (~> 0.1)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    google-cloud-core (1.6.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.3.1)
    google-protobuf (3.25.5-arm64-darwin)
    google-protobuf (3.25.5-x86_64-darwin)
    google-protobuf (3.25.5-x86_64-linux)
    googleapis-common-protos-types (1.3.2)
      google-protobuf (~> 3.14)
    googleauth (0.17.1)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.15)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.2)
    hashid-rails (1.4.1)
      activerecord (>= 4.0)
      hashids (~> 1.0)
    hashids (1.0.6)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httpclient (2.8.3)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.6.3)
    json_schema (0.21.0)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (3.17.0.2)
    licensed (4.4.0)
      json (~> 2.6)
      licensee (~> 9.16)
      parallel (~> 1.22)
      pathname-common_prefix (~> 0.0.1)
      reverse_markdown (~> 2.1)
      ruby-xxHash (~> 0.4.0)
      thor (~> 1.2)
      tomlrb (~> 2.0)
    licensee (9.16.0)
      dotenv (~> 2.0)
      octokit (>= 4.20, < 7.0)
      reverse_markdown (>= 1, < 3)
      rugged (>= 0.24, < 2.0)
      thor (>= 0.19, < 2.0)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    lockbox (1.1.1)
    logger (1.6.1)
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    logstash-event (1.2.02)
    logstash-logger (0.26.1)
      logstash-event (~> 1.2)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lz4-ruby (0.3.3)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.4)
    marginalia (1.11.1)
      actionpack (>= 5.2)
      activerecord (>= 5.2)
    matrix (0.4.2)
    memoist (0.16.2)
    meta_request (0.8.2)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 8)
    method_source (1.1.0)
    middleware (0.1.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mini_mime (1.1.5)
    minitest (5.25.4)
    mongo (2.20.1)
      bson (>= 4.14.1, < 6.0.0)
    msgpack (1.7.5)
    msgpack_rails (0.4.3)
      activesupport (>= 3.0)
      msgpack
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    nap (1.1.0)
    native-package-installer (1.1.9)
    net-http (0.4.1)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.2.3)
    netrc (0.11.0)
    nio4r (2.7.3)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    observer (0.1.2)
    octokit (4.21.0)
      faraday (>= 0.9)
      sawyer (~> 0.8.0, >= 0.5.3)
    oj (3.16.7)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.1)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.1)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    omniauth-saml (2.2.3)
      omniauth (~> 2.1)
      ruby-saml (~> 1.18)
    open4 (1.3.4)
    openapi_parser (2.2.4)
    opentelemetry-api (1.1.0)
    opentelemetry-common (0.19.6)
      opentelemetry-api (~> 1.0)
    opentelemetry-exporter-otlp (0.23.0)
      google-protobuf (~> 3.19)
      googleapis-common-protos-types (~> 1.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.6)
      opentelemetry-sdk (~> 1.0)
      opentelemetry-semantic_conventions
    opentelemetry-instrumentation-action_pack (0.2.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
      opentelemetry-instrumentation-rack (~> 0.21.0)
    opentelemetry-instrumentation-action_view (0.3.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.1)
      opentelemetry-instrumentation-base (~> 0.20)
    opentelemetry-instrumentation-active_record (0.4.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
      ruby2_keywords
    opentelemetry-instrumentation-active_support (0.2.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-aws_sdk (0.3.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-base (0.21.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-registry (~> 0.1)
    opentelemetry-instrumentation-concurrent_ruby (0.20.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-faraday (0.21.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-http (0.20.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-http_client (0.21.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-net_http (0.21.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-pg (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-rack (0.21.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-rails (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-action_pack (~> 0.2.0)
      opentelemetry-instrumentation-action_view (~> 0.3.0)
      opentelemetry-instrumentation-active_record (~> 0.4.0)
      opentelemetry-instrumentation-active_support (~> 0.2.0)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-redis (0.24.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-instrumentation-sidekiq (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-instrumentation-base (~> 0.21.0)
    opentelemetry-registry (0.2.0)
      opentelemetry-api (~> 1.1)
    opentelemetry-sdk (1.1.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.19.3)
      opentelemetry-registry (~> 0.1)
      opentelemetry-semantic_conventions
    opentelemetry-semantic_conventions (1.8.0)
      opentelemetry-api (~> 1.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    pairing_heap (3.1.0)
    parallel (1.25.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pastel (0.8.0)
      tty-color (~> 0.5)
    pathname-common_prefix (0.0.2)
    pg (1.4.6)
    pg_query (5.1.0)
      google-protobuf (>= 3.22.3)
    pgslice (0.6.1)
      pg (>= 1)
      thor
    pkg-config (1.5.6)
    polyfill (1.9.0)
    premailer (1.11.1)
      addressable
      css_parser (>= 1.6.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prism (1.3.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-doc (1.5.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pstore (0.1.3)
    public_activity (2.0.2)
      actionpack (>= 5.0.0)
      activerecord (>= 5.0)
      i18n (>= 0.5.0)
      railties (>= 5.0.0)
    public_suffix (5.1.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    puma_worker_killer (1.0.0)
      bigdecimal (>= 2.0)
      get_process_mem (>= 0.2)
      puma (>= 2.7)
    qonfig (0.29.0)
    racc (1.8.1)
    rack (2.2.16)
    rack-attack (6.4.0)
      rack (>= 1.0, < 3)
    rack-contrib (2.5.0)
      rack (< 4)
    rack-protection (2.2.3)
      rack
    rack-proxy (0.7.7)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (********)
      actioncable (= ********)
      actionmailbox (= ********)
      actionmailer (= ********)
      actionpack (= ********)
      actiontext (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      bundler (>= 1.15.0)
      railties (= ********)
      sprockets-rails (>= 2.0.0)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.0.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rbi (0.2.4)
      prism (~> 1.0)
      sorbet-runtime (>= 0.5.9204)
    rbtree (0.4.6)
    rchardet (1.8.0)
    rdoc (6.3.4.1)
    recaptcha (5.12.3)
      json
    red-arrow (17.0.0)
      bigdecimal (>= 3.1.0)
      csv
      extpp (>= 0.1.1)
      gio2 (>= 3.5.0)
      native-package-installer
      pkg-config
    red-arrow-flight (17.0.0)
      red-arrow (= 17.0.0)
    redis (4.5.1)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    request_store (1.4.1)
      rack (>= 1.4)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    retriable (3.1.2)
    reverse_markdown (2.1.1)
      nokogiri
    rexml (3.3.9)
    rgl (0.5.10)
      pairing_heap (>= 0.3.0)
      rexml (~> 3.2, >= 3.2.4)
      stream (~> 0.5.3)
    rmagick (6.1.1)
      observer (~> 0.1)
      pkg-config (~> 1.4)
    rotp (6.3.0)
    rouge (4.3.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (4.0.1)
      actionpack (>= 4.2)
      activesupport (>= 4.2)
      railties (>= 4.2)
      rspec-core (~> 3.9)
      rspec-expectations (~> 3.9)
      rspec-mocks (~> 3.9)
      rspec-support (~> 3.9)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.63.5)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.3)
      parser (>= 3.3.1.0)
    rubocop-capybara (2.21.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-rspec (2.29.2)
      rubocop (~> 1.40)
      rubocop-capybara (~> 2.17)
      rubocop-factory_bot (~> 2.22)
      rubocop-rspec_rails (~> 2.28)
    rubocop-rspec_rails (2.29.1)
      rubocop (~> 1.61)
    ruby-oci8 (2.2.12)
    ruby-odbc (0.999992)
    ruby-ole (********)
    ruby-openai (7.3.1)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-saml (1.18.0)
      nokogiri (>= 1.13.10)
      rexml
    ruby-xxHash (*******)
    ruby2_keywords (0.0.5)
    rubyXL (3.4.27)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    rubyzip (2.3.2)
    rugged (1.7.2)
    safe_type (1.1.1)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sawyer (0.8.2)
      addressable (>= 2.3.5)
      faraday (> 0.8, < 2.0)
    selenium-webdriver (4.9.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    set (1.0.4)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sinatra (2.2.3)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.3)
      tilt (~> 2.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sorbet (0.5.11376)
      sorbet-static (= 0.5.11376)
    sorbet-coerce (0.7.0)
      polyfill (~> 1.8)
      safe_type (~> 1.1, >= 1.1.1)
      sorbet-runtime (>= 0.4.4704)
    sorbet-runtime (0.5.11376)
    sorbet-static (0.5.11376-universal-darwin)
    sorbet-static (0.5.11376-x86_64-linux)
    sorbet-static-and-runtime (0.5.11376)
      sorbet (= 0.5.11376)
      sorbet-runtime (= 0.5.11376)
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    spoom (1.5.3)
      erubi (>= 1.10.0)
      prism (>= 0.28.0)
      rbi (>= 0.2.3)
      sorbet-static-and-runtime (>= 0.5.10187)
      thor (>= 0.19.2)
    spreadsheet (1.3.0)
      ruby-ole
    sprockets (3.7.3)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stackprof (0.2.26)
    stream (0.5.5)
    strong_migrations (1.0.0)
      activerecord (>= 5.2)
    tabulo (2.8.2)
      tty-screen (= 0.8.1)
      unicode-display_width (~> 2.2)
    tapioca (0.16.10)
      benchmark
      bundler (>= 2.2.25)
      netrc (>= 0.11.0)
      parallel (>= 1.21.0)
      rbi (~> 0.2)
      sorbet-static-and-runtime (>= 0.5.11087)
      spoom (>= 1.2.0)
      thor (>= 1.2.0)
      yard-sorbet
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    terser (1.2.3)
      execjs (>= 0.3.0, < 3)
    thor (1.3.2)
    tilt (2.4.0)
    timecop (0.9.6)
    timeout (0.4.3)
    tiny_tds (2.1.5)
    tomlrb (2.0.3)
    tty-color (0.6.0)
    tty-cursor (0.7.1)
    tty-prompt (0.23.1)
      pastel (~> 0.8)
      tty-reader (~> 0.8)
    tty-reader (0.9.0)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.8)
      wisper (~> 2.0)
    tty-screen (0.8.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2022.7)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    unicode-display_width (2.4.2)
    uri (0.12.4)
    vcr (6.2.0)
    version_gem (1.1.6)
    vite_rails (3.0.19)
      railties (>= 5.1, < 9)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.9.0)
      dry-cli (>= 0.7, < 2)
      logger (~> 1.6)
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    webdrivers (4.7.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (> 3.141, < 5.0)
    webmock (3.23.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.2)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    whenever-test (1.0.1)
      whenever
    wisper (2.0.1)
    xmlrpc (0.3.3)
      webrick
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.36)
    yard-sorbet (0.8.1)
      sorbet-runtime (>= 0.5)
      yard (>= 0.9)
    zeitwerk (2.6.18)

GEM
  remote: https://rubygems.pkg.github.com/holistics/
  specs:
    aml (4.1.0)
      coding_utils (~> 1.0)
      json_rpc_client (~> 0.4)
      oj (~> 3.16)
      sorbet-runtime (~> 0.5)
    coding_utils (1.0.3)
      activesupport (~> 6.1.0)
      connection_pool (~> 2.2)
      hashdiff (~> 1.1)
      logger (~> 1.6)
      lz4-ruby (~> 0.3.3)
      msgpack_rails (~> 0.4.3)
      oj (~> 3.13)
      redis (~> 4.5.0)
      rspec (~> 3.0)
      sorbet-coerce (~> 0.7.0)
      sorbet-runtime (~> 0.5)
    data_connector (3.2.0)
      coding_utils (~> 1.0)
      http (~> 5.2.0)
      pg (= 1.4.6)
      retriable (~> 3.1.2)
      ruby-odbc (~> 0.99999)
      sorbet-runtime (~> 0.5)
    holistics_aml (5.1.0)
      aml (~> 4.0)
      coding_utils (~> 1.0)
      data_connector (~> 3.0)
      rspec (~> 3.0)
      sorbet-runtime (~> 0.5)
      sql_generation (~> 5.0)
    holistics_config (0.4.1)
      rubocop (~> 1.63.0)
      rubocop-rspec (~> 2.29.0)
    json_rpc_client (0.4.3)
      activesupport (~> 6.1)
      coding_utils (~> 1.0)
      http (~> 5.2.0)
      sorbet-runtime (~> 0.5)
    postgres_cache (3.0.3)
      coding_utils (~> 1.0)
      connection_pool (~> 2.2)
      data_connector (~> 3.1)
      pg (= 1.4.6)
      rspec (~> 3.0)
      sorbet-runtime (~> 0.5)
    sql_generation (5.8.0)
      coding_utils (~> 1.0)
      pg (= 1.4.6)
      rgl (~> 0.5.7)
      sorbet-runtime (~> 0.5)
      zeitwerk

PLATFORMS
  arm64-darwin-22
  arm64-darwin-23
  arm64-darwin-24
  x86_64-darwin-22
  x86_64-darwin-24
  x86_64-linux

DEPENDENCIES
  actionmailer
  active_model_serializers (~> 0.9.13)
  active_record_query_trace (= 1.8.2)
  activeadmin (~> 3.2.2)
  activeadmin_json_editor (~> 0.0.10)
  activerecord-import
  alba (= 3.4.0)
  aml (~> 4.0)!
  aml_studio!
  anbt-sql-formatter
  awesome_print
  aws-sdk-athena (= 1.79.0)
  aws-sdk-s3 (~> 1.156)
  batch_api (~> 0.3.0)
  bcrypt_pbkdf (~> 1.1)
  better_errors
  binding_of_caller
  bootsnap
  brakeman (~> 5.4.1)
  browser
  cancancan (~> 3.4.0)
  capybara
  caxlsx
  childprocess (~> 3.0)
  chronic
  chunky_png
  cli-ui
  coding_utils (~> 1.0, >= 1.0.3)!
  colorize
  combine_pdf (~> 1.0)
  committee (~> 5.4)
  crawler_detect
  creek!
  danger (= 9.5.1)
  data_connector (~> 3.2)!
  database_cleaner (= 2.0.2)
  debug
  deprecation_toolkit
  devise (~> 4.9.4)
  devise-two-factor (~> 4.1.0)
  dogstatsd-ruby
  dotenv-rails
  ed25519 (~> 1.2, >= 1.2.4)
  factory_bot_rails
  faker
  fast_blank
  fast_excel
  ffi (< 1.17.0)
  fixture_builder
  google-apis-analytics_v3
  google-apis-analyticsreporting_v4
  google-apis-drive_v3
  google-apis-oauth2_v2
  google-apis-people_v1
  google-apis-sheets_v4
  google-cloud-bigquery
  hashdiff
  hashid-rails (~> 1.4)
  holistics_aml (= 5.1.0)!
  holistics_config (~> 0.3)!
  http (~> 5.2.0)
  jquery-ui-rails!
  jwt
  licensed
  lockbox
  lograge
  logstash-event
  logstash-logger
  lz4-ruby
  marginalia
  matrix (~> 0.4)
  meta_request (~> 0.8.2)
  mime-types (~> 3.4)
  mongo (~> 2.20.0)
  msgpack_rails
  mysql2!
  net-http
  net-imap
  net-pop
  net-sftp (~> 4.0)
  net-smtp
  net-ssh
  nokogiri
  octokit (~> 4.21)
  oj
  omniauth-google-oauth2 (~> 1.2.1)
  omniauth-rails_csrf_protection (~> 1.0.1)
  omniauth-saml (~> 2.2.3)
  opentelemetry-exporter-otlp
  opentelemetry-instrumentation-action_view
  opentelemetry-instrumentation-active_record
  opentelemetry-instrumentation-active_support
  opentelemetry-instrumentation-aws_sdk
  opentelemetry-instrumentation-base
  opentelemetry-instrumentation-concurrent_ruby
  opentelemetry-instrumentation-faraday
  opentelemetry-instrumentation-http
  opentelemetry-instrumentation-http_client
  opentelemetry-instrumentation-net_http
  opentelemetry-instrumentation-pg
  opentelemetry-instrumentation-rails
  opentelemetry-instrumentation-redis
  opentelemetry-instrumentation-sidekiq
  opentelemetry-sdk
  opentelemetry-semantic_conventions
  parallel
  pg (= 1.4.6)
  pg_query
  pgslice
  postgres_cache (~> 3.0.1)!
  premailer-rails
  presto-client!
  prosopite!
  pry-byebug
  pry-doc
  public_activity (~> 2.0.2)
  puma
  puma_worker_killer
  rack (~> 2.2)
  rack-attack (~> 6.4.0)
  rails (~> 6.1.0)
  rails-controller-testing
  recaptcha
  red-arrow (~> 17.0)
  red-arrow-flight (~> 17.0)
  redis
  representable (= 3.0.4)
  retriable
  rgl (~> 0.5.7)
  rmagick
  rspec-mocks
  rspec-rails (~> 4.0)
  rspec-retry
  rspec_junit_formatter
  rubocop
  rubocop-rspec
  ruby-oci8 (= 2.2.12)
  ruby-odbc
  ruby-openai (= 7.3.1)
  rubyXL (= 3.4.27)
  sass-rails
  selenium-webdriver
  shoulda-matchers
  sidekiq (< 7)
  sidekiq-pro (< 6)!
  simplecov
  sorbet
  sorbet-coerce
  sorbet-runtime
  sorbet-static-and-runtime
  sorted_set
  source_control!
  spreadsheet
  sprockets (< 4)
  sql_generation (~> 5.8)!
  stackprof
  strong_migrations (~> 1.0.0)
  tabulo
  tapioca (= 0.16.10)
  terser
  thor
  timecop
  tiny_tds (~> 2.1.5)
  tty-prompt
  tzinfo-data (= 1.2022.7)
  uri (= 0.12.4)
  vcr
  vite_rails (~> 3.0)
  webdrivers
  webmock
  whenever
  whenever-test
  xmlrpc

BUNDLED WITH
   2.4.22
